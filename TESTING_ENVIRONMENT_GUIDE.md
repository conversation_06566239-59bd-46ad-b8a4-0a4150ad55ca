# YendorCats Testing Environment Guide

This guide explains how to set up and use the Docker-based testing environment for the YendorCats application.

## Overview

The testing environment consists of the following services:

- **API Backend**: .NET Core application using SQLite for data storage
- **MinIO**: S3-compatible storage service (replaces BackBlaze B2 in production)
- **File Uploader**: Node.js service for handling file uploads
- **Nginx**: Web server for serving frontend static files

All services are containerized using Docker and orchestrated with Docker Compose.

## Requirements

- Docker and Docker Compose installed on your system
- Git (to clone the repository)
- AWS CLI (optional, for manual S3 operations)

## Getting Started

### 1. Environment Setup

First, create a copy of the environment variables file:

```bash
cp .env.testing .env
```

### 2. Directory Structure

Ensure you have the following directory structure for test images:

```
test-images/
├── gallery/
├── kittens/
├── queens/
└── studs/
```

Place sample cat images in their respective directories.

### 3. Start the Testing Environment

Start all services with Docker Compose:

```bash
docker-compose -f docker-compose.testing.yml up -d
```

This will:
- Build and start all containers
- Create necessary volumes for data persistence
- Set up networking between services
- Configure MinIO with the required bucket

### 4. Load Test Data

The following steps initialize the test environment with sample data:

#### Initialize the SQLite Database

```bash
# Copy the SQL initialization script to the API container
docker cp test-data/init-test-db.sql yendorcats-test-api:/app/

# Execute the SQL script
docker exec yendorcats-test-api sh -c "sqlite3 /app/data/yendorcats-test.db < /app/init-test-db.sql"
```

#### Upload Test Images to MinIO

```bash
# Make the script executable
chmod +x test-data/upload-test-images.sh

# Run the script to upload test images
docker exec yendorcats-test-api sh -c "/app/test-data/upload-test-images.sh"
```

## Accessing Services

Once the environment is running, you can access the services at:

- **Frontend**: http://localhost:8080
- **API Swagger Documentation**: http://localhost:5100/swagger
- **File Uploader**: http://localhost:5102
- **MinIO Console**: http://localhost:9001 (Login: minioadmin/minioadmin)

## Test Accounts

The environment is pre-configured with the following test accounts:

### Admin Accounts
- Username: `testadmin`, Password: `TestPassword123!`
- Username: `admin2`, Password: `TestPassword456!`

### User Account
- Username: `testuser`, Password: `UserTest789!`

## Managing the Environment

### View Logs

```bash
# View logs for all services
docker-compose -f docker-compose.testing.yml logs

# View logs for a specific service
docker-compose -f docker-compose.testing.yml logs api
```

### Stop the Environment

```bash
docker-compose -f docker-compose.testing.yml down
```

### Reset the Environment

To completely reset the environment (removing all volumes and data):

```bash
docker-compose -f docker-compose.testing.yml down -v
```

## Data Persistence

The testing environment uses Docker volumes for data persistence:

- `api-test-data`: SQLite database files
- `api-test-logs`: API service logs
- `minio-test-data`: MinIO storage (S3 objects)

## Troubleshooting

### Service Health Checks

Check the health of all services:

```bash
docker-compose -f docker-compose.testing.yml ps
```

### Database Issues

If you encounter database issues, you can reset just the database:

```bash
docker volume rm yendorcats_api-test-data
docker-compose -f docker-compose.testing.yml up -d
```

### MinIO Issues

If MinIO is not accessible, check if the bucket was created properly:

```bash
docker exec yendorcats-test-minio mc ls local/yendor-test
```

To recreate the bucket:

```bash
docker exec yendorcats-test-minio mc mb --ignore-existing local/yendor-test
docker exec yendorcats-test-minio mc policy set download local/yendor-test
```

## Integration with CI/CD

For CI/CD pipelines, you can use the following command to start the environment and wait for all services to be healthy:

```bash
docker-compose -f docker-compose.testing.yml up -d && \
docker-compose -f docker-compose.testing.yml ps | grep -q "healthy" || \
(echo "Services failed to start properly" && exit 1)
```

---

For additional assistance or to report issues with the testing environment, contact the development team.