version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: backend/YendorCats.API/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_URLS=http://+:8080
      - YENDOR_S3_ACCESS_KEY=${YENDOR_S3_ACCESS_KEY}
      - YENDOR_S3_SECRET_KEY=${YENDOR_S3_SECRET_KEY}
      - YENDOR_DEFAULT_ADMIN_USERNAME=${YENDOR_DEFAULT_ADMIN_USERNAME}
      - YENDOR_DEFAULT_ADMIN_EMAIL=${YENDOR_DEFAULT_ADMIN_EMAIL}
      - YENDOR_DEFAULT_ADMIN_PASSWORD=${YENDOR_DEFAULT_ADMIN_PASSWORD}
      - YENDOR_JWT_SECRET=${YENDOR_JWT_SECRET}
    networks:
      - yendorcats-network

  frontend:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./frontend:/usr/share/nginx/html
      - ./frontend/nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
    networks:
      - yendorcats-network

networks:
  yendorcats-network:
    driver: bridge
