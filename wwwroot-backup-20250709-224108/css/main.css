/*
 * Yendor Cats - Main CSS File
 * Imports all CSS modules in the correct order
 */

/* Import variables first so they're available to all other modules */
@import 'variables.css';

/* Base styles and resets */
@import 'base.css';

/* Component styles */
@import 'components/buttons.css';
@import 'components/navbar.css';
@import 'components/footer.css';
@import 'components/carousel.css';

/* Section styles */
@import 'sections.css';

/* Add any additional CSS imports here */

/* --------------------------------------------------
 * Cat Carousel Navigation Buttons
 * -------------------------------------------------- */
.category-section {
    position: relative;
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 10;
    transition: all 0.3s ease;
}

.carousel-nav:hover {
    background-color: white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.carousel-nav.prev {
    left: 10px;
}

.carousel-nav.next {
    right: 10px;
}

@media (max-width: 768px) {
    .carousel-nav {
        width: 35px;
        height: 35px;
        font-size: 1.2rem;
    }
} 