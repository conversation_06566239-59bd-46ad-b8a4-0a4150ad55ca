// Photo Upload Functionality
class PhotoUploader {
    constructor() {
        this.apiBaseUrl = '/api/PhotoUpload';
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        this.recentUploads = JSON.parse(localStorage.getItem('recentUploads') || '[]');
        
        this.initializeElements();
        this.bindEvents();
        this.loadRecentUploads();
    }

    initializeElements() {
        this.form = document.getElementById('photo-upload-form');
        this.fileInput = document.getElementById('photo-file');
        this.uploadArea = document.getElementById('file-upload-area');
        this.previewArea = document.getElementById('preview-area');
        this.previewImage = document.getElementById('preview-image');
        this.removeFileBtn = document.getElementById('remove-file-btn');
        this.uploadBtn = document.getElementById('upload-btn');
        this.uploadStatus = document.getElementById('upload-status');
        this.recentUploadsSection = document.getElementById('recent-uploads');
        this.uploadsGrid = document.getElementById('uploads-grid');
        this.chooseFileBtn = document.querySelector('.choose-file-btn');
    }

    bindEvents() {
        // File input change
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // Drag and drop
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Choose file button
        this.chooseFileBtn.addEventListener('click', () => this.fileInput.click());
        
        // Remove file button
        this.removeFileBtn.addEventListener('click', () => this.removeFile());
        
        // Form submission
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // Set default date to today
        document.getElementById('date-taken').valueAsDate = new Date();
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.validateAndPreviewFile(file);
        }
    }

    handleDragOver(event) {
        event.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.fileInput.files = files;
            this.validateAndPreviewFile(files[0]);
        }
    }

    validateAndPreviewFile(file) {
        // Validate file type
        if (!this.allowedTypes.includes(file.type)) {
            this.showStatus('error', 'Invalid file type', 
                `Please select a valid image file. Allowed types: ${this.allowedTypes.join(', ')}`);
            return;
        }

        // Validate file size
        if (file.size > this.maxFileSize) {
            this.showStatus('error', 'File too large', 
                `File size must be less than ${this.maxFileSize / (1024 * 1024)}MB`);
            return;
        }

        // Show preview
        this.showPreview(file);
        this.hideStatus();
    }

    showPreview(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            this.previewImage.src = e.target.result;
            this.uploadArea.style.display = 'none';
            this.previewArea.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }

    removeFile() {
        this.fileInput.value = '';
        this.previewArea.style.display = 'none';
        this.uploadArea.style.display = 'block';
        this.hideStatus();
    }

    async handleSubmit(event) {
        event.preventDefault();
        
        if (!this.fileInput.files[0]) {
            this.showStatus('error', 'No file selected', 'Please select a photo to upload');
            return;
        }

        const formData = new FormData(this.form);
        
        // Validate required fields
        const catName = formData.get('catName');
        if (!catName || catName.trim() === '') {
            this.showStatus('error', 'Cat name required', 'Please enter the cat\'s name');
            return;
        }

        this.setLoading(true);
        this.hideStatus();

        try {
            const response = await fetch(`${this.apiBaseUrl}/upload`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showStatus('success', 'Upload successful!', 
                    `Photo uploaded for ${result.data.catName}. File: ${result.data.fileName}`);
                
                // Add to recent uploads
                this.addToRecentUploads(result.data);
                
                // Reset form
                this.resetForm();
            } else {
                this.showStatus('error', 'Upload failed', result.message || 'An error occurred during upload');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showStatus('error', 'Upload failed', 'Network error. Please check your connection and try again.');
        } finally {
            this.setLoading(false);
        }
    }

    setLoading(loading) {
        this.uploadBtn.disabled = loading;
        if (loading) {
            this.uploadBtn.classList.add('loading');
        } else {
            this.uploadBtn.classList.remove('loading');
        }
    }

    showStatus(type, message, details = '') {
        this.uploadStatus.className = `upload-status ${type}`;
        this.uploadStatus.style.display = 'block';
        
        const icon = type === 'success' ? '✅' : '❌';
        this.uploadStatus.querySelector('.status-icon').textContent = icon;
        this.uploadStatus.querySelector('.status-message').textContent = message;
        this.uploadStatus.querySelector('.status-details').textContent = details;
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => this.hideStatus(), 5000);
        }
    }

    hideStatus() {
        this.uploadStatus.style.display = 'none';
    }

    resetForm() {
        this.form.reset();
        this.removeFile();
        document.getElementById('date-taken').valueAsDate = new Date();
    }

    addToRecentUploads(uploadData) {
        const upload = {
            id: uploadData.photoId,
            catName: uploadData.catName,
            fileName: uploadData.fileName,
            fileUrl: uploadData.fileUrl,
            category: uploadData.category,
            dateTaken: uploadData.dateTaken,
            uploadedAt: new Date().toISOString()
        };

        this.recentUploads.unshift(upload);
        
        // Keep only last 10 uploads
        if (this.recentUploads.length > 10) {
            this.recentUploads = this.recentUploads.slice(0, 10);
        }

        localStorage.setItem('recentUploads', JSON.stringify(this.recentUploads));
        this.loadRecentUploads();
    }

    loadRecentUploads() {
        if (this.recentUploads.length === 0) {
            this.recentUploadsSection.style.display = 'none';
            return;
        }

        this.recentUploadsSection.style.display = 'block';
        this.uploadsGrid.innerHTML = '';

        this.recentUploads.forEach(upload => {
            const uploadElement = this.createUploadElement(upload);
            this.uploadsGrid.appendChild(uploadElement);
        });
    }

    createUploadElement(upload) {
        const div = document.createElement('div');
        div.className = 'upload-item';
        
        const uploadDate = new Date(upload.uploadedAt).toLocaleDateString();
        const dateTaken = new Date(upload.dateTaken).toLocaleDateString();
        
        div.innerHTML = `
            <img src="${upload.fileUrl}" alt="${upload.catName}" loading="lazy">
            <div class="upload-item-info">
                <h4>${upload.catName}</h4>
                <p>Category: ${upload.category}</p>
                <p>Taken: ${dateTaken}</p>
                <p>Uploaded: ${uploadDate}</p>
            </div>
        `;
        
        // Add click handler to view cat profile
        div.addEventListener('click', () => {
            window.location.href = `cat-profile.html?cat=${encodeURIComponent(upload.catName)}`;
        });
        
        return div;
    }
}

// Initialize uploader when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PhotoUploader();
});

// Auto-fill cat name from URL parameter (if coming from cat profile)
document.addEventListener('DOMContentLoaded', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const catName = urlParams.get('cat');
    if (catName) {
        document.getElementById('cat-name').value = decodeURIComponent(catName);
    }
});
