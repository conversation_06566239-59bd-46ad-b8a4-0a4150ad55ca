/**
 * Yendor Cats - Available Cats JavaScript
 * 
 * This file contains the functionality for the available cats section, including:
 * - Loading cat data from the API
 * - Filtering and searching cats
 * - Pagination
 * - Handling cat details view
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const catsContainer = document.getElementById('available-cats-container');
    const catsLoader = document.getElementById('cats-loader');
    const breedFilter = document.getElementById('breed-filter');
    const catSearch = document.getElementById('cat-search');
    const catsPagination = document.getElementById('cats-pagination');
    
    // Check if cats container exists on the page
    if (!catsContainer) return;
    
    // Sample cat data (in a real application, this would come from an API)
    const cats = [
        {
            id: 1,
            name: 'Luna',
            breed: 'Bengal',
            gender: 'F',
            age: '1 year',
            color: 'Brown Spotted Tabby',
            description: '<PERSON> is a beautiful Bengal with a playful personality. She loves interactive toys and climbing to high places.',
            price: 1800,
            status: 'available',
            image: 'images/cats/bengal1.jpg',
            images: [
                'images/cats/bengal1.jpg',
                'images/cats/bengal1-2.jpg',
                'images/cats/bengal1-3.jpg'
            ],
            markings: 'Distinctive rosettes with high contrast',
            dateOfBirth: '2022-05-15'
        },
        {
            id: 2,
            name: 'Thor',
            breed: 'Maine Coon',
            gender: 'M',
            age: '2 years',
            color: 'Red Tabby',
            description: 'Thor is a magnificent Maine Coon with a gentle temperament. He enjoys lounging around and being brushed.',
            price: 1500,
            status: 'available',
            image: 'images/cats/mainecoon1.jpg',
            images: [
                'images/cats/mainecoon1.jpg',
                'images/cats/mainecoon1-2.jpg'
            ],
            markings: 'Tufted ears and bushy tail',
            dateOfBirth: '2021-03-10'
        },
        {
            id: 3,
            name: 'Savvy',
            breed: 'Savannah',
            gender: 'F',
            age: '8 months',
            color: 'Spotted Tabby',
            description: 'Savvy is an energetic F5 Savannah with a curious nature. She loves to explore and play fetch.',
            price: 2500,
            status: 'reserved',
            image: 'images/cats/savannah1.jpg',
            images: [
                'images/cats/savannah1.jpg',
                'images/cats/savannah1-2.jpg'
            ],
            markings: 'Bold spots and stripes',
            dateOfBirth: '2022-10-20'
        },
        {
            id: 4,
            name: 'Leo',
            breed: 'Bengal',
            gender: 'M',
            age: '1.5 years',
            color: 'Silver Spotted Tabby',
            description: 'Leo is a striking silver Bengal with a friendly disposition. He gets along well with other cats and children.',
            price: 1900,
            status: 'available',
            image: 'images/cats/bengal2.jpg',
            images: [
                'images/cats/bengal2.jpg'
            ],
            markings: 'Silver coat with dark spots',
            dateOfBirth: '2021-12-05'
        },
        {
            id: 5,
            name: 'Maple',
            breed: 'Maine Coon',
            gender: 'F',
            age: '3 years',
            color: 'Tortie',
            description: 'Maple is a beautiful tortoiseshell Maine Coon with a sweet personality. She loves to cuddle and purr.',
            price: 1200,
            status: 'sold',
            image: 'images/cats/mainecoon2.jpg',
            images: [
                'images/cats/mainecoon2.jpg',
                'images/cats/mainecoon2-2.jpg'
            ],
            markings: 'Tortoiseshell pattern with white chest',
            dateOfBirth: '2020-08-18'
        },
        {
            id: 6,
            name: 'Safari',
            breed: 'Savannah',
            gender: 'M',
            age: '1 year',
            color: 'Brown Spotted Tabby',
            description: 'Safari is an F4 Savannah with a playful and affectionate personality. He loves to jump and climb.',
            price: 2800,
            status: 'available',
            image: 'images/cats/savannah2.jpg',
            images: [
                'images/cats/savannah2.jpg'
            ],
            markings: 'Large spots with golden background',
            dateOfBirth: '2022-01-30'
        }
    ];
    
    // Pagination variables
    const itemsPerPage = 3;
    let currentPage = 1;
    let filteredCats = [...cats];
    
    /**
     * Initialize the available cats section
     */
    function initAvailableCats() {
        // Remove loader
        if (catsLoader) catsLoader.remove();
        
        // Populate breed filter
        populateBreedFilter();
        
        // Add event listeners for filters
        addFilterListeners();
        
        // Render first page of cats
        renderCats(filteredCats, currentPage);
        
        // Create pagination
        createPagination(filteredCats.length);
    }
    
    /**
     * Populate the breed filter dropdown with unique breeds
     */
    function populateBreedFilter() {
        if (!breedFilter) return;
        
        // Get unique breeds
        const breeds = [...new Set(cats.map(cat => cat.breed))];
        
        // Sort breeds alphabetically
        breeds.sort();
        
        // Add each breed to the filter
        breeds.forEach(breed => {
            const option = document.createElement('option');
            option.value = breed;
            option.textContent = breed;
            breedFilter.appendChild(option);
        });
    }
    
    /**
     * Add event listeners for filter and search inputs
     */
    function addFilterListeners() {
        // Breed filter change event
        if (breedFilter) {
            breedFilter.addEventListener('change', filterCats);
        }
        
        // Search input event
        if (catSearch) {
            catSearch.addEventListener('input', filterCats);
        }
    }
    
    /**
     * Filter cats based on search and breed filter
     */
    function filterCats() {
        const searchValue = catSearch ? catSearch.value.toLowerCase() : '';
        const breedValue = breedFilter ? breedFilter.value : '';
        
        // Filter cats based on search and breed
        filteredCats = cats.filter(cat => {
            // Filter by search
            const matchesSearch = 
                !searchValue || 
                cat.name.toLowerCase().includes(searchValue) || 
                cat.description.toLowerCase().includes(searchValue) || 
                cat.color.toLowerCase().includes(searchValue);
            
            // Filter by breed
            const matchesBreed = !breedValue || cat.breed === breedValue;
            
            // Return true if both conditions are met
            return matchesSearch && matchesBreed;
        });
        
        // Reset to first page
        currentPage = 1;
        
        // Render filtered cats
        renderCats(filteredCats, currentPage);
        
        // Update pagination
        createPagination(filteredCats.length);
    }
    
    /**
     * Render cats for a specific page
     */
    function renderCats(cats, page) {
        // Clear the container
        catsContainer.innerHTML = '';
        
        // Calculate start and end index for current page
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, cats.length);
        
        // If no cats match the filter
        if (cats.length === 0) {
            const noResults = document.createElement('div');
            noResults.classList.add('no-results');
            noResults.textContent = 'No cats match your criteria. Please try a different search.';
            catsContainer.appendChild(noResults);
            return;
        }
        
        // Render cats for current page
        for (let i = startIndex; i < endIndex; i++) {
            const cat = cats[i];
            const catCard = createCatCard(cat);
            catsContainer.appendChild(catCard);
        }
    }
    
    /**
     * Create a card element for a cat
     */
    function createCatCard(cat) {
        const catCard = document.createElement('div');
        catCard.classList.add('cat-card');
        
        // Format price to currency
        const formattedPrice = cat.status === 'sold' ? 'SOLD' : `$${cat.price.toLocaleString()}`;
        
        // Determine status class and text
        let statusClass = '';
        let statusText = '';
        
        switch (cat.status) {
            case 'available':
                statusClass = 'status-available';
                statusText = 'Available';
                break;
            case 'reserved':
                statusClass = 'status-reserved';
                statusText = 'Reserved';
                break;
            case 'sold':
                statusClass = 'status-sold';
                statusText = 'Sold';
                break;
        }
        
        // Calculate age from date of birth for more accurate display
        const dob = new Date(cat.dateOfBirth);
        const today = new Date();
        const ageInMonths = (today.getFullYear() - dob.getFullYear()) * 12 + (today.getMonth() - dob.getMonth());
        
        let ageText = '';
        if (ageInMonths < 12) {
            ageText = ageInMonths === 1 ? '1 month' : `${ageInMonths} months`;
        } else {
            const years = Math.floor(ageInMonths / 12);
            const months = ageInMonths % 12;
            ageText = years === 1 ? '1 year' : `${years} years`;
            if (months > 0) {
                ageText += months === 1 ? ', 1 month' : `, ${months} months`;
            }
        }
        
        // Create card HTML
        catCard.innerHTML = `
            <div class="cat-image">
                <span class="cat-status ${statusClass}">${statusText}</span>
                <img src="${cat.image}" alt="${cat.name} - ${cat.breed} cat">
            </div>
            <div class="cat-content">
                <h3>${cat.name} <span class="cat-price">${formattedPrice}</span></h3>
                <div class="cat-details">
                    <div class="cat-detail"><i class="fas fa-paw"></i> ${cat.breed}</div>
                    <div class="cat-detail"><i class="fas fa-venus-mars"></i> ${cat.gender === 'M' ? 'Male' : 'Female'}</div>
                    <div class="cat-detail"><i class="fas fa-birthday-cake"></i> ${ageText}</div>
                    <div class="cat-detail"><i class="fas fa-palette"></i> ${cat.color}</div>
                </div>
                <p>${cat.description}</p>
                <div class="cat-footer">
                    <button class="btn-primary view-cat-details" data-id="${cat.id}">${cat.status === 'sold' ? 'View Details' : 'Inquire About This Cat'}</button>
                </div>
            </div>
        `;
        
        // Add click event for the details button
        const detailsButton = catCard.querySelector('.view-cat-details');
        detailsButton.addEventListener('click', function() {
            const catId = parseInt(this.getAttribute('data-id'));
            viewCatDetails(catId);
        });
        
        return catCard;
    }
    
    /**
     * Create pagination controls
     */
    function createPagination(totalItems) {
        if (!catsPagination) return;
        
        // Clear pagination
        catsPagination.innerHTML = '';
        
        // Calculate total pages
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        
        // Don't show pagination if only one page
        if (totalPages <= 1) return;
        
        // Create previous button
        const prevBtn = document.createElement('span');
        prevBtn.classList.add('pagination-item');
        prevBtn.innerHTML = '&laquo;';
        prevBtn.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                renderCats(filteredCats, currentPage);
                updatePaginationActive();
            }
        });
        catsPagination.appendChild(prevBtn);
        
        // Create page numbers
        for (let i = 1; i <= totalPages; i++) {
            const pageBtn = document.createElement('span');
            pageBtn.classList.add('pagination-item');
            if (i === currentPage) pageBtn.classList.add('active');
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', function() {
                currentPage = i;
                renderCats(filteredCats, currentPage);
                updatePaginationActive();
            });
            catsPagination.appendChild(pageBtn);
        }
        
        // Create next button
        const nextBtn = document.createElement('span');
        nextBtn.classList.add('pagination-item');
        nextBtn.innerHTML = '&raquo;';
        nextBtn.addEventListener('click', function() {
            if (currentPage < totalPages) {
                currentPage++;
                renderCats(filteredCats, currentPage);
                updatePaginationActive();
            }
        });
        catsPagination.appendChild(nextBtn);
    }
    
    /**
     * Update the active state of pagination buttons
     */
    function updatePaginationActive() {
        const paginationItems = catsPagination.querySelectorAll('.pagination-item');
        paginationItems.forEach((item, index) => {
            // Skip first and last items (prev/next buttons)
            if (index === 0 || index === paginationItems.length - 1) return;
            
            if (index === currentPage) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
    
    /**
     * View details for a specific cat
     * This would typically open a modal or navigate to a details page
     * For this example, we'll just log to the console and show an alert
     */
    function viewCatDetails(catId) {
        const cat = cats.find(c => c.id === catId);
        if (!cat) return;
        
        console.log('Viewing details for cat:', cat);
        
        // In a real application, you might open a modal with details
        // or navigate to a details page
        alert(`You've selected ${cat.name}, a ${cat.breed} cat. In a full implementation, this would open a detailed view with more images and information.`);
    }
    
    // Initialize the available cats section
    initAvailableCats();
}); 