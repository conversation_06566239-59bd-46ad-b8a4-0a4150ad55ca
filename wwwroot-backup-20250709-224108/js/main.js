/**
 * Yendor Cats - Main JavaScript
 * 
 * This file contains the main JavaScript functionality for the website,
 * including navigation, scrolling effects, testimonials slider, and general UI interactions.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Wait for the DOM to be fully loaded before executing scripts
document.addEventListener('DOMContentLoaded', function() {
    
    // ------------------------------------------------
    // Navigation and Header functionality
    // ------------------------------------------------
    const header = document.querySelector('.header');
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navList = document.querySelector('.nav-list');
    
    // Extra handling for mobile menu toggle to ensure it works
    if (mobileMenuToggle && navList) {
        // Direct inline event handling as a fallback
        mobileMenuToggle.onclick = function(e) {
            e.preventDefault();
            mobileMenuToggle.classList.toggle('active');
            navList.classList.toggle('active');
            console.log('Mobile menu toggled (backup handler)');
            return false;
        };
    }
    
    
    // Add 'scrolled' class to header when page is scrolled
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
    
    // ------------------------------------------------
    // Back to Top Button
    // ------------------------------------------------
    const backToTopBtn = document.getElementById('back-to-top');
    
    if (backToTopBtn) {
        // Show/hide back to top button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });
        
        // Scroll to top when button is clicked
        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    // ------------------------------------------------
    // Testimonials Slider
    // ------------------------------------------------
    const testimonialsSlider = document.getElementById('testimonials-slider');
    const testimonialDots = document.getElementById('testimonial-dots');
    
    if (testimonialsSlider && testimonialDots) {
        const testimonialItems = testimonialsSlider.querySelectorAll('.testimonial-item');
        let currentSlide = 0;
        
        // Create dots for each testimonial
        testimonialItems.forEach((_, index) => {
            const dot = document.createElement('span');
            dot.classList.add('testimonial-dot');
            if (index === 0) dot.classList.add('active');
            dot.addEventListener('click', () => {
                goToSlide(index);
            });
            testimonialDots.appendChild(dot);
        });
        
        const dots = testimonialDots.querySelectorAll('.testimonial-dot');
        
        // Show a specific slide
        function goToSlide(index) {
            testimonialsSlider.style.transform = `translateX(-${index * 100}%)`;
            
            // Update active dot
            dots.forEach(dot => dot.classList.remove('active'));
            dots[index].classList.add('active');
            
            currentSlide = index;
        }
        
        // Automatic slider
        setInterval(() => {
            currentSlide = (currentSlide + 1) % testimonialItems.length;
            goToSlide(currentSlide);
        }, 5000);
    }
    
    // ------------------------------------------------
    // Form Handling
    // ------------------------------------------------
    const contactForm = document.getElementById('contact-form');
    const newsletterForm = document.getElementById('newsletter-form');
    
    // Contact form submission
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const formObject = {};
            formData.forEach((value, key) => {
                formObject[key] = value;
            });
            
            // In a real application, you would send this data to a server
            console.log('Contact form submitted:', formObject);
            
            
            // Show success message (in a real app, this would happen after successful API call)
            const successMessage = document.createElement('div');
            successMessage.classList.add('form-success');
            successMessage.innerHTML = 'Thank you for your message! We will get back to you soon.';
            
            contactForm.reset();
            contactForm.parentNode.appendChild(successMessage);
            
            // Remove success message after a delay
            setTimeout(() => {
                successMessage.remove();
            }, 5000);
        });
    }
    
    // Newsletter form submission
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[type="email"]').value;
            
            // In a real application, you would send this data to a server
            console.log('Newsletter subscription:', email);
            
            // Show success message
            const successMessage = document.createElement('div');
            successMessage.classList.add('form-success');
            successMessage.innerHTML = 'Thank you for subscribing to our newsletter!';
            
            newsletterForm.reset();
            newsletterForm.parentNode.appendChild(successMessage);
            
            // Remove success message after a delay
            setTimeout(() => {
                successMessage.remove();
            }, 5000);
        });
    }
    
    // ------------------------------------------------
    // Initialize Breed Data
    // ------------------------------------------------
    function initializeBreeds() {
        const breedsContainer = document.getElementById('breeds-container');
        const breedsLoader = document.getElementById('breeds-loader');
        
        if (!breedsContainer) return;
        
        // Sample breed data (in a real application, this would come from an API)
        const breeds = [
            {
                id: 1,
                name: 'Maine Coon',
                description: 'Known for their large size, tufted ears, and shaggy coat, Maine Coons are gentle giants with friendly personalities.',
                image: 'images/breeds/maine-coon.jpg',
                traits: {
                    size: 'Large',
                    coat: 'Long',
                    temperament: 'Friendly'
                }
            },
            {
                id: 2,
                name: 'Bengal',
                description: 'With their distinctive spotted or marbled coat, Bengals resemble wild leopards but have the temperament of a domestic cat.',
                image: 'images/breeds/bengal.jpg',
                traits: {
                    size: 'Medium',
                    coat: 'Short',
                    temperament: 'Active'
                }
            },
            {
                id: 3,
                name: 'Savannah',
                description: 'A cross between a domestic cat and a serval, Savannahs are tall, lean cats with exotic markings and high energy levels.',
                image: 'images/breeds/savannah.jpg',
                traits: {
                    size: 'Large',
                    coat: 'Short',
                    temperament: 'Energetic'
                }
            }
        ];
        
        // Remove loader and display breeds
        if (breedsLoader) breedsLoader.remove();
        
        // Create breed cards
        breeds.forEach(breed => {
            const breedCard = document.createElement('div');
            breedCard.classList.add('breed-card');
            
            breedCard.innerHTML = `
                <div class="breed-image">
                    <img src="${breed.image}" alt="${breed.name} cat">
                </div>
                <div class="breed-content">
                    <h3>${breed.name}</h3>
                    <p>${breed.description}</p>
                    <div class="breed-meta">
                        <span><i class="fas fa-ruler"></i> ${breed.traits.size}</span>
                        <span><i class="fas fa-cat"></i> ${breed.traits.coat}</span>
                        <span><i class="fas fa-heart"></i> ${breed.traits.temperament}</span>
                    </div>
                    <a href="#studs-section" class="breed-link">View Our Cats</a>
                </div>
            `;
            
            breedsContainer.appendChild(breedCard);
        });
    }
    
    // Initialize the breeds section with sample data
    initializeBreeds();
    
    // ------------------------------------------------
    // Maine Coon Legends Popup
    // ------------------------------------------------
    const showLegendsLink = document.getElementById('show-legends');
    const legendsPopup = document.getElementById('legends-popup');
    const closePopup = document.querySelector('.close-popup');
    
    if (showLegendsLink && legendsPopup) {
        // Ensure popup is hidden by default
        legendsPopup.style.display = 'none';
        
        showLegendsLink.addEventListener('click', function(e) {
            e.preventDefault();
            legendsPopup.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        });
        
        closePopup.addEventListener('click', function() {
            legendsPopup.style.display = 'none';
            document.body.style.overflow = 'auto';
        });
        
        // Close if clicking outside content
        legendsPopup.addEventListener('click', function(e) {
            if (e.target === legendsPopup) {
                legendsPopup.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
        
        // Close on escape key press
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && legendsPopup.style.display === 'flex') {
                legendsPopup.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    }
    
    // ------------------------------------------------
    // Cat Carousel Navigation
    // ------------------------------------------------
    const carousels = document.querySelectorAll('.carousel:not(#gallery-carousel)');
    
    console.log(`Main.js: Found ${carousels.length} standard carousels to initialize`);
    
    // Initialize each carousel except gallery
    carousels.forEach(carousel => {
        // Get options from data attributes
        const options = {
            autoplay: carousel.dataset.autoplay !== 'false',
            autoplaySpeed: parseInt(carousel.dataset.autoplaySpeed) || 5000,
            pauseOnHover: carousel.dataset.pauseOnHover !== 'false',
            infinite: carousel.dataset.infinite !== 'false',
            indicators: carousel.dataset.indicators !== 'false',
            navigation: carousel.dataset.navigation !== 'false'
        };
        
        // Initialize
        console.log(`Initializing standard carousel: ${carousel.id}`);
        new Carousel(carousel, options);
    });
    
    console.log('Main.js: Standard carousel initialization complete');
});
