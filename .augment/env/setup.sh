#!/bin/bash

# YendorCats Development Environment Setup
echo "Setting up YendorCats development environment..."

# Update package lists
sudo apt-get update

# Install .NET 8 SDK
echo "Installing .NET 8 SDK..."
wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
rm packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y dotnet-sdk-8.0

# Install Node.js and npm
echo "Installing Node.js and npm..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python and pip (for utility scripts)
echo "Installing Python and pip..."
sudo apt-get install -y python3 python3-pip

# Install required Python packages for utility scripts
pip3 install boto3 python-dotenv requests

# Verify installations
echo "Verifying installations..."
dotnet --version
node --version
npm --version
python3 --version

# Add tools to PATH in user profile
echo 'export PATH="$HOME/.dotnet/tools:$PATH"' >> $HOME/.profile

# Navigate to backend and restore dependencies
echo "Restoring .NET dependencies..."
cd backend/YendorCats.API
dotnet restore

# Navigate to file uploader and install dependencies
echo "Installing Node.js dependencies for file uploader..."
cd ../../tools/file-uploader
npm install

# Navigate to frontend and install dependencies (if package.json exists)
echo "Installing frontend dependencies..."
cd ../../frontend
if [ -f "package.json" ]; then
    npm install
fi

# Return to root directory
cd ..

echo "Setup completed successfully!"