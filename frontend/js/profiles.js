// Profiles Page Functionality
class CatProfilesPage {
    constructor() {
        this.apiBaseUrl = '/api/PhotoUpload';
        this.profiles = [];
        
        this.initializeElements();
        this.loadProfiles();
    }

    initializeElements() {
        this.loadingContainer = document.getElementById('loading-container');
        this.errorContainer = document.getElementById('error-container');
        this.profilesContainer = document.getElementById('profiles-container');
        this.profilesGrid = document.getElementById('profiles-grid');
        this.profileCountElement = document.getElementById('profile-count');
        this.photoCountElement = document.getElementById('photo-count');
        this.searchInputElement = document.getElementById('search-input');
        this.sortSelectElement = document.getElementById('sort-select');
    }

    bindEvents() {
        this.searchInputElement.addEventListener('input', () => this.filterProfiles());
        this.sortSelectElement.addEventListener('change', () => this.sortAndRenderProfiles());
    }

    async loadProfiles() {
        try {
            this.showLoading();
            
            const response = await fetch(`${this.apiBaseUrl}/profiles`);
            const result = await response.json();

            if (response.ok && result.success) {
                this.profiles = result.profiles || [];
                this.displayProfiles();
                this.bindEvents(); // Bind events after profiles are loaded
            } else {
                this.showError();
            }
        } catch (error) {
            console.error('Error loading profiles:', error);
            this.showError();
        }
    }

    showLoading() {
        this.loadingContainer.style.display = 'flex';
        this.errorContainer.style.display = 'none';
        this.profilesContainer.style.display = 'none';
    }

    showError() {
        this.loadingContainer.style.display = 'none';
        this.errorContainer.style.display = 'flex';
        this.profilesContainer.style.display = 'none';
    }

    displayProfiles() {
        this.loadingContainer.style.display = 'none';
        this.errorContainer.style.display = 'none';
        this.profilesContainer.style.display = 'block';
        
        this.updateStats();
        this.sortAndRenderProfiles();
    }

    updateStats() {
        const totalProfiles = this.profiles.length;
        const totalPhotos = this.profiles.reduce((sum, profile) => sum + (profile.totalPhotoCount || 0), 0);
        
        this.profileCountElement.textContent = totalProfiles;
        this.photoCountElement.textContent = totalPhotos;
    }

    filterProfiles() {
        const searchTerm = this.searchInputElement.value.toLowerCase();
        const filteredProfiles = this.profiles.filter(profile => {
            return (profile.name && profile.name.toLowerCase().includes(searchTerm)) ||
                   (profile.breed && profile.breed.toLowerCase().includes(searchTerm));
        });
        this.renderProfiles(filteredProfiles);
    }

    sortAndRenderProfiles() {
        const sortValue = this.sortSelectElement.value;
        let sortedProfiles = [...this.profiles];

        switch (sortValue) {
            case 'name-asc':
                sortedProfiles.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'name-desc':
                sortedProfiles.sort((a, b) => b.name.localeCompare(a.name));
                break;
            case 'photos-desc':
                sortedProfiles.sort((a, b) => (b.totalPhotoCount || 0) - (a.totalPhotoCount || 0));
                break;
            case 'photos-asc':
                sortedProfiles.sort((a, b) => (a.totalPhotoCount || 0) - (b.totalPhotoCount || 0));
                break;
        }
        
        this.renderProfiles(sortedProfiles);
    }

    renderProfiles(profilesToRender) {
        this.profilesGrid.innerHTML = '';

        if (profilesToRender.length === 0) {
            this.profilesGrid.innerHTML = '<p>No profiles found.</p>';
            return;
        }

        profilesToRender.forEach(profile => {
            const profileCard = document.createElement('div');
            profileCard.className = 'profile-card';
            profileCard.innerHTML = this.createProfileCardHTML(profile);
            
            profileCard.addEventListener('click', () => {
                window.location.href = `cat-profile.html?cat=${encodeURIComponent(profile.name)}`;
            });

            this.profilesGrid.appendChild(profileCard);
        });
    }

    createProfileCardHTML(profile) {
        const photoCount = profile.totalPhotoCount || 0;
        const hasDbProfile = profile.hasProfile;

        return `
            <div class="profile-card-image">
                <img src="${profile.latestPhotoUrl || 'images/placeholder-cat.jpg'}" alt="${profile.name}" loading="lazy">
                <div class="profile-card-overlay">
                    <span class="photo-count">${photoCount} photo${photoCount !== 1 ? 's' : ''}</span>
                </div>
            </div>
            <div class="profile-card-info">
                <h3>${profile.name}</h3>
                <p>${profile.breed || 'Breed not specified'}</p>
                <span class="status-badge ${hasDbProfile ? 'has-profile' : 'no-profile'}">
                    ${hasDbProfile ? 'Full Profile' : 'Photos Only'}
                </span>
            </div>
        `;
    }
}

// Initialize profiles page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CatProfilesPage();
});
