document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('login-form');
    const adminPanel = document.getElementById('admin-panel');
    const adminContent = document.getElementById('admin-content');
    const adminUsername = document.getElementById('admin-username');
    const logoutBtn = document.getElementById('logout-btn');
    const apiBaseUrl = 'http://localhost:5002/api/AdminAuth';

    // Check for existing token
    const token = localStorage.getItem('admin_token');
    if (token) {
        showAdminPanel(token);
    }

    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const username = e.target.username.value;
        const password = e.target.password.value;

        try {
            const response = await fetch(`${apiBaseUrl}/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password }),
            });

            if (response.ok) {
                const data = await response.json();
                localStorage.setItem('admin_token', data.token);
                showAdminPanel(data.token);
            } else {
                alert('Login failed. Please check your credentials.');
            }
        } catch (error) {
            console.error('Login error:', error);
            alert('An error occurred during login.');
        }
    });

    logoutBtn.addEventListener('click', () => {
        localStorage.removeItem('admin_token');
        adminPanel.style.display = 'none';
        loginForm.style.display = 'block';
        adminContent.innerHTML = '';
    });

    async function showAdminPanel(token) {
        loginForm.style.display = 'none';
        adminPanel.style.display = 'block';

        try {
            const response = await fetch(`${apiBaseUrl}/me`, {
                headers: { Authorization: `Bearer ${token}` },
            });

            if (response.ok) {
                const user = await response.json();
                adminUsername.textContent = user.username;
                // I will load the metadata editor here
                adminContent.innerHTML = '<iframe src="../scripts/metadata-editor.html" style="width:100%; height: 600px; border:none;"></iframe>';
            } else {
                // Token might be invalid, so log out
                logoutBtn.click();
            }
        } catch (error) {
            console.error('Failed to fetch user info:', error);
            logoutBtn.click();
        }
    }
});
