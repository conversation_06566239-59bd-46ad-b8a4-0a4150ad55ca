/**
 * Navbar functionality for Yendor Cats
 * - Mobile menu toggle
 * - Expanding/collapsing navbar on scroll
 * - Back to top button
 */

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const header = document.querySelector('.header');
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navList = document.querySelector('.nav-list');
    const backToTopButton = document.querySelector('.back-to-top');
    
    // Add scroll indicator to the header if it doesn't exist yet
    if (!document.querySelector('.scroll-indicator')) {
        const scrollIndicator = document.createElement('div');
        scrollIndicator.className = 'scroll-indicator';
        scrollIndicator.innerHTML = `
            <p>Scroll Down</p>
            <svg class="scroll-indicator-arrow" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <polyline points="19 12 12 19 5 12"></polyline>
            </svg>
        `;
        header.appendChild(scrollIndicator);
    }
    
    // Mobile menu toggle functionality - Fix with proper event handling
    if (mobileMenuToggle) {
        // Force remove and recreate the element
        mobileMenuToggle.outerHTML = `
        <button class="mobile-menu-toggle" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>`;
        
        // Get the new element
        const newMobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        
        // Add direct onclick handler and event listener for redundancy
        newMobileMenuToggle.onclick = function(e) {
            e.preventDefault();
            this.classList.toggle('active');
            navList.classList.toggle('active');
            
            // Force display style to ensure it's visible
            if (navList.classList.contains('active')) {
                navList.style.display = 'flex';
                document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
            } else {
                setTimeout(() => {
                    navList.style.display = '';
                }, 300); // Match transition time
                document.body.style.overflow = ''; // Restore scrolling
            }
            
            console.log('Mobile menu toggled (direct handler)');
            return false;
        };
    }
    
    // Collapse navbar on scroll
    function handleScroll() {
        const scrollPosition = window.scrollY;
        
        // When user scrolls down more than 100px, collapse the navbar
        if (scrollPosition > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
        
        // Show back to top button when scrolled down
        if (scrollPosition > 500) {
            backToTopButton?.classList.add('visible');
        } else {
            backToTopButton?.classList.remove('visible');
        }
    }
    
    // Initially check scroll position (in case page is refreshed mid-scroll)
    handleScroll();
    
    // Listen for scroll events
    window.addEventListener('scroll', handleScroll);
    
    // Close mobile menu when a link is clicked
    document.querySelectorAll('.nav-list a').forEach(link => {
        link.addEventListener('click', function() {
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            
            navList.classList.remove('active');
            navList.style.display = '';
            
            if (mobileToggle) {
                mobileToggle.classList.remove('active');
            }
            
            document.body.style.overflow = ''; // Restore scrolling
        });
    });
    
    // Back to top button functionality
    if (backToTopButton) {
        backToTopButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Scroll down when clicking the scroll indicator
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const mainContent = document.querySelector('main');
            if (mainContent) {
                mainContent.scrollIntoView({ behavior: 'smooth' });
            } else {
                // If no main element, scroll down the viewport height
                window.scrollTo({
                    top: window.innerHeight,
                    behavior: 'smooth'
                });
            }
        });
    }
    
    // Debug check - log navigation elements to console for troubleshooting
    console.log('Navigation initialization complete');
    console.log('Mobile menu toggle exists:', !!document.querySelector('.mobile-menu-toggle'));
    console.log('Nav list exists:', !!document.querySelector('.nav-list'));
}); 