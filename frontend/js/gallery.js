/**
 * Yendor Cats - Gallery JavaScript
 *
 * This file contains the functionality for the image gallery, including:
 * - Loading gallery images from API
 * - Filtering gallery images by category
 * - Handling gallery image modal for larger views
 * - Lazy loading of images for performance
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Gallery elements
    const galleryContainer = document.getElementById('gallery-container');
    const galleryLoader = document.getElementById('gallery-loader');
    const galleryFilter = document.querySelector('.gallery-filter');
    const galleryModal = document.getElementById('gallery-modal');
    const modalImage = document.getElementById('modal-image');
    const modalCaption = document.getElementById('modal-caption');
    const modalClose = document.querySelector('.modal-close');
    const modalPrev = document.getElementById('modal-prev');
    const modalNext = document.getElementById('modal-next');

    // Check if gallery elements exist on the page
    if (!galleryContainer) return;

    // Sample gallery data (in a real application, this would come from an API)
    const galleryItems = [
        {
            id: 1,
            image: 'images/gallery/cat1.jpg',
            thumbnail: 'images/gallery/thumbnails/cat1.jpg',
            title: 'Bengal Kitten',
            description: 'Our beautiful Bengal kitten at 12 weeks old',
            category: 'bengal'
        },
        {
            id: 2,
            image: 'images/gallery/cat2.jpg',
            thumbnail: 'images/gallery/thumbnails/cat2.jpg',
            title: 'Maine Coon Adult',
            description: 'Majestic Maine Coon showing off his magnificent coat',
            category: 'maine-coon'
        },
        {
            id: 3,
            image: 'images/gallery/cat3.jpg',
            thumbnail: 'images/gallery/thumbnails/cat3.jpg',
            title: 'Savannah Cat',
            description: 'F5 Savannah cat with beautiful spotted pattern',
            category: 'savannah'
        },
        {
            id: 4,
            image: 'images/gallery/cat4.jpg',
            thumbnail: 'images/gallery/thumbnails/cat4.jpg',
            title: 'Bengal Adult',
            description: 'Adult Bengal showcasing his glittered coat',
            category: 'bengal'
        },
        {
            id: 5,
            image: 'images/gallery/cat5.jpg',
            thumbnail: 'images/gallery/thumbnails/cat5.jpg',
            title: 'Maine Coon Kitten',
            description: 'Adorable Maine Coon kitten with tufted ears',
            category: 'maine-coon'
        },
        {
            id: 6,
            image: 'images/gallery/cat6.jpg',
            thumbnail: 'images/gallery/thumbnails/cat6.jpg',
            title: 'Savannah Kitten',
            description: 'Young Savannah kitten showing early serval traits',
            category: 'savannah'
        },
        {
            id: 7,
            image: 'images/gallery/cat7.jpg',
            thumbnail: 'images/gallery/thumbnails/cat7.jpg',
            title: 'Bengal Playing',
            description: 'Playful Bengal enjoying interactive toys',
            category: 'bengal'
        },
        {
            id: 8,
            image: 'images/gallery/cat8.jpg',
            thumbnail: 'images/gallery/thumbnails/cat8.jpg',
            title: 'Maine Coon Family',
            description: 'Mother Maine Coon with her kittens',
            category: 'maine-coon'
        },
        {
            id: 9,
            image: 'images/gallery/cat9.jpg',
            thumbnail: 'images/gallery/thumbnails/cat9.jpg',
            title: 'Savannah Outdoors',
            description: 'Savannah cat exploring in a secure outdoor area',
            category: 'savannah'
        }
    ];

    // Global variables for gallery
    let currentItems = galleryItems;
    let currentIndex = 0;

    /**
     * Initialize the gallery
     */
    function initGallery() {
        // Remove the loader
        if (galleryLoader) galleryLoader.remove();

        // Add filter buttons
        addFilterButtons();

        // Render all gallery items
        renderGalleryItems(galleryItems);

        // Initialize modal events
        initModal();
    }

    /**
     * Create filter buttons based on unique categories
     */
    function addFilterButtons() {
        if (!galleryFilter) return;

        // Get unique categories
        const categories = [...new Set(galleryItems.map(item => item.category))];

        // Add filter buttons for each category
        categories.forEach(category => {
            const button = document.createElement('button');
            button.classList.add('filter-btn');
            button.setAttribute('data-filter', category);
            button.textContent = formatCategoryName(category);

            button.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Add active class to clicked button
                this.classList.add('active');

                // Filter gallery items
                const filterValue = this.getAttribute('data-filter');
                filterGallery(filterValue);
            });

            galleryFilter.appendChild(button);
        });
    }

    /**
     * Format category name for display (e.g., "maine-coon" -> "Maine Coon")
     */
    function formatCategoryName(category) {
        return category
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    /**
     * Filter gallery items by category
     */
    function filterGallery(category) {
        if (category === 'all') {
            currentItems = galleryItems;
            renderGalleryItems(galleryItems);
        } else {
            currentItems = galleryItems.filter(item => item.category === category);
            renderGalleryItems(currentItems);
        }
    }

    /**
     * Render gallery items to the container
     */
    function renderGalleryItems(items) {
        // Clear the container
        galleryContainer.innerHTML = '';

        // Add items to the container
        items.forEach((item, index) => {
            const galleryItem = document.createElement('div');
            galleryItem.classList.add('gallery-item');
            galleryItem.setAttribute('data-id', item.id);

            galleryItem.innerHTML = `
                <img src="${item.thumbnail || item.image}" alt="${item.title}" loading="lazy">
                <div class="gallery-item-overlay">
                    <h4>${item.title}</h4>
                    <p>${item.description}</p>
                </div>
            `;

            // Add click event to open modal
            galleryItem.addEventListener('click', function() {
                openModal(index, items);
            });

            galleryContainer.appendChild(galleryItem);
        });
    }

    /**
     * Initialize modal events
     */
    function initModal() {
        if (!galleryModal) return;

        // Close modal when clicking the close button
        if (modalClose) {
            modalClose.addEventListener('click', closeModal);
        }

        // Close modal when clicking outside the image
        galleryModal.addEventListener('click', function(e) {
            if (e.target === galleryModal) {
                closeModal();
            }
        });

        // Handle keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (!galleryModal.style.display || galleryModal.style.display === 'none') return;

            if (e.key === 'Escape') {
                closeModal();
            } else if (e.key === 'ArrowLeft') {
                showPrevItem();
            } else if (e.key === 'ArrowRight') {
                showNextItem();
            }
        });

        // Previous and next buttons
        if (modalPrev) {
            modalPrev.addEventListener('click', showPrevItem);
        }

        if (modalNext) {
            modalNext.addEventListener('click', showNextItem);
        }
    }

    /**
     * Open the modal with the selected image
     */
    function openModal(index, items) {
        currentIndex = index;
        currentItems = items;

        const item = items[index];

        if (modalImage) {
            modalImage.src = item.image;
            modalImage.alt = item.title;
        }

        if (modalCaption) {
            // Build a more comprehensive caption with all available metadata
            let captionHTML = `<h4>${item.title}</h4>`;

            // Add description if available
            if (item.description) {
                captionHTML += `<p class="description">${item.description}</p>`;
            }

            // Start metadata section
            captionHTML += '<div class="metadata-section">';

            // Add age if available
            if (item.age) {
                captionHTML += `<p><strong>Age:</strong> ${item.age} years</p>`;
            }

            // Add gender if available
            if (item.gender) {
                const genderText = item.gender === 'M' ? 'Male' : item.gender === 'F' ? 'Female' : item.gender;
                captionHTML += `<p><strong>Gender:</strong> ${genderText}</p>`;
            }

            // Add color if available
            if (item.color) {
                captionHTML += `<p><strong>Color/Pattern:</strong> ${item.color}</p>`;
            }

            // Add traits if available
            if (item.traits) {
                captionHTML += `<p><strong>Traits:</strong> ${item.traits}</p>`;
            }

            // Add parents if available
            if (item.mother || item.father) {
                let parentsText = '<strong>Parents:</strong> ';
                if (item.mother) parentsText += `Mother: ${item.mother}`;
                if (item.mother && item.father) parentsText += ', ';
                if (item.father) parentsText += `Father: ${item.father}`;
                captionHTML += `<p>${parentsText}</p>`;
            }

            // Close metadata section
            captionHTML += '</div>';

            modalCaption.innerHTML = captionHTML;
        }

        // Show the modal
        galleryModal.style.display = 'block';

        // Prevent scrolling when modal is open
        document.body.style.overflow = 'hidden';
    }

    /**
     * Close the modal
     */
    function closeModal() {
        galleryModal.style.display = 'none';
        document.body.style.overflow = '';
    }

    /**
     * Show the previous item in the modal
     */
    function showPrevItem() {
        currentIndex = (currentIndex - 1 + currentItems.length) % currentItems.length;
        const item = currentItems[currentIndex];

        if (modalImage) {
            modalImage.src = item.image;
            modalImage.alt = item.title;
        }

        if (modalCaption) {
            // Build a more comprehensive caption with all available metadata
            let captionHTML = `<h4>${item.title}</h4>`;

            // Add description if available
            if (item.description) {
                captionHTML += `<p class="description">${item.description}</p>`;
            }

            // Start metadata section
            captionHTML += '<div class="metadata-section">';

            // Add age if available
            if (item.age) {
                captionHTML += `<p><strong>Age:</strong> ${item.age} years</p>`;
            }

            // Add gender if available
            if (item.gender) {
                const genderText = item.gender === 'M' ? 'Male' : item.gender === 'F' ? 'Female' : item.gender;
                captionHTML += `<p><strong>Gender:</strong> ${genderText}</p>`;
            }

            // Add color if available
            if (item.color) {
                captionHTML += `<p><strong>Color/Pattern:</strong> ${item.color}</p>`;
            }

            // Add traits if available
            if (item.traits) {
                captionHTML += `<p><strong>Traits:</strong> ${item.traits}</p>`;
            }

            // Add parents if available
            if (item.mother || item.father) {
                let parentsText = '<strong>Parents:</strong> ';
                if (item.mother) parentsText += `Mother: ${item.mother}`;
                if (item.mother && item.father) parentsText += ', ';
                if (item.father) parentsText += `Father: ${item.father}`;
                captionHTML += `<p>${parentsText}</p>`;
            }

            // Close metadata section
            captionHTML += '</div>';

            modalCaption.innerHTML = captionHTML;
        }
    }

    /**
     * Show the next item in the modal
     */
    function showNextItem() {
        currentIndex = (currentIndex + 1) % currentItems.length;
        const item = currentItems[currentIndex];

        if (modalImage) {
            modalImage.src = item.image;
            modalImage.alt = item.title;
        }

        if (modalCaption) {
            // Build a more comprehensive caption with all available metadata
            let captionHTML = `<h4>${item.title}</h4>`;

            // Add description if available
            if (item.description) {
                captionHTML += `<p class="description">${item.description}</p>`;
            }

            // Start metadata section
            captionHTML += '<div class="metadata-section">';

            // Add age if available
            if (item.age) {
                captionHTML += `<p><strong>Age:</strong> ${item.age} years</p>`;
            }

            // Add gender if available
            if (item.gender) {
                const genderText = item.gender === 'M' ? 'Male' : item.gender === 'F' ? 'Female' : item.gender;
                captionHTML += `<p><strong>Gender:</strong> ${genderText}</p>`;
            }

            // Add color if available
            if (item.color) {
                captionHTML += `<p><strong>Color/Pattern:</strong> ${item.color}</p>`;
            }

            // Add traits if available
            if (item.traits) {
                captionHTML += `<p><strong>Traits:</strong> ${item.traits}</p>`;
            }

            // Add parents if available
            if (item.mother || item.father) {
                let parentsText = '<strong>Parents:</strong> ';
                if (item.mother) parentsText += `Mother: ${item.mother}`;
                if (item.mother && item.father) parentsText += ', ';
                if (item.father) parentsText += `Father: ${item.father}`;
                captionHTML += `<p>${parentsText}</p>`;
            }

            // Close metadata section
            captionHTML += '</div>';

            modalCaption.innerHTML = captionHTML;
        }
    }

    // Initialize the gallery
    initGallery();
});