<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yendor Cats - Metadata Editor</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/main.css">
    <style>
        .metadata-editor {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .editor-tabs {
            display: flex;
            background: #f5f5f5;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
            margin-bottom: 0;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: #f5f5f5;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: white;
            color: #2c3e50;
        }

        .tab-button:hover {
            background: #e8e8e8;
        }

        .tab-content {
            background: white;
            border: 1px solid #ddd;
            border-radius: 0 0 8px 8px;
            padding: 30px;
            min-height: 600px;
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .cat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .cat-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .cat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .cat-header {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }

        .cat-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .cat-info {
            color: #666;
            font-size: 0.9em;
        }

        .cat-photos {
            padding: 15px;
        }

        .photo-thumbnail {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.2s ease;
        }

        .photo-thumbnail:hover {
            border-color: #3498db;
        }

        .photo-thumbnail.selected {
            border-color: #e74c3c;
        }

        .bulk-actions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .bulk-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .search-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metadata-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .family-tree {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            border: 1px solid transparent;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-error {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .selected-count {
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="metadata-editor">
        <header style="text-align: center; margin-bottom: 30px;">
            <h1>Yendor Cats Metadata Management System</h1>
            <p>Professional pedigree and photo organization for Maine Coon breeding</p>
        </header>

        <div class="editor-tabs">
            <button class="tab-button active" data-tab="overview">Overview</button>
            <button class="tab-button" data-tab="cats">Cat Profiles</button>
            <button class="tab-button" data-tab="photos">Photo Management</button>
            <button class="tab-button" data-tab="bulk">Bulk Operations</button>
            <button class="tab-button" data-tab="pedigree">Pedigree Builder</button>
        </div>

        <div class="tab-content">
            <!-- Overview Tab -->
            <div class="tab-panel active" id="overview-panel">
                <h2>Cattery Overview</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-cats">-</div>
                        <div>Total Cats</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="total-photos">-</div>
                        <div>Total Photos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="unlinked-photos">-</div>
                        <div>Unlinked Photos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="available-kittens">-</div>
                        <div>Available Kittens</div>
                    </div>
                </div>

                <div class="bulk-actions">
                    <h3>Quick Actions</h3>
                    <div class="bulk-controls">
                        <button class="btn btn-primary" onclick="refreshData()">Refresh Data</button>
                        <button class="btn btn-secondary" onclick="exportMetadata()">Export Metadata</button>
                        <button class="btn btn-warning" onclick="showS3Config()">S3 Configuration</button>
                        <button class="btn btn-success" onclick="switchToTab('photos')">Manage Unlinked Photos</button>
                    </div>
                </div>

                <div id="recent-activity">
                    <h3>Recent Activity</h3>
                    <div id="activity-list">
                        <p>Loading recent metadata changes...</p>
                    </div>
                </div>
            </div>

            <!-- Cat Profiles Tab -->
            <div class="tab-panel" id="cats-panel">
                <h2>Cat Profiles Management</h2>
                
                <div class="search-filters">
                    <div class="form-group">
                        <label>Search by Name</label>
                        <input type="text" id="cat-search" placeholder="Enter cat name...">
                    </div>
                    <div class="form-group">
                        <label>Filter by Breed</label>
                        <select id="breed-filter">
                            <option value="">All Breeds</option>
                            <option value="Maine Coon">Maine Coon</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Filter by Bloodline</label>
                        <select id="bloodline-filter">
                            <option value="">All Bloodlines</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Breeding Status</label>
                        <select id="status-filter">
                            <option value="">All Status</option>
                            <option value="available-kitten">Available Kitten</option>
                            <option value="breeding-queen">Breeding Queen</option>
                            <option value="stud">Stud</option>
                            <option value="retired">Retired</option>
                        </select>
                    </div>
                </div>

                <div class="bulk-controls" style="margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="searchCats()">Search</button>
                    <button class="btn btn-secondary" onclick="clearFilters()">Clear Filters</button>
                    <button class="btn btn-success" onclick="createNewCatProfile()">New Cat Profile</button>
                </div>

                <div class="cat-grid" id="cat-profiles-grid">
                    <p>Loading cat profiles...</p>
                </div>
            </div>

            <!-- Photo Management Tab -->
            <div class="tab-panel" id="photos-panel">
                <h2>Photo Management</h2>
                
                <div class="bulk-actions">
                    <h3>Photo Organization Tools</h3>
                    <div class="bulk-controls">
                        <button class="btn btn-primary" onclick="loadUnlinkedPhotos()">Load Unlinked Photos</button>
                        <button class="btn btn-secondary" onclick="loadAllPhotos()">Load All Photos</button>
                        <button class="btn btn-warning" onclick="bulkSelectPhotos()">Bulk Select Mode</button>
                        <span class="selected-count" id="selected-count" style="display: none;">0 selected</span>
                    </div>
                    
                    <div id="bulk-photo-actions" style="display: none; margin-top: 15px;">
                        <h4>Apply to Selected Photos:</h4>
                        <div class="metadata-form">
                            <div class="form-group">
                                <label>Cat Name</label>
                                <input type="text" id="bulk-cat-name" placeholder="Cat name">
                            </div>
                            <div class="form-group">
                                <label>Breed</label>
                                <input type="text" id="bulk-breed" value="Maine Coon">
                            </div>
                            <div class="form-group">
                                <label>Bloodline</label>
                                <input type="text" id="bulk-bloodline" placeholder="Bloodline">
                            </div>
                            <div class="form-group">
                                <label>Age/Stage</label>
                                <select id="bulk-age">
                                    <option value="">Select age/stage</option>
                                    <option value="newborn">Newborn (0-2 weeks)</option>
                                    <option value="kitten-young">Young Kitten (2-8 weeks)</option>
                                    <option value="kitten">Kitten (8 weeks - 6 months)</option>
                                    <option value="young-adult">Young Adult (6 months - 2 years)</option>
                                    <option value="adult">Adult (2+ years)</option>
                                </select>
                            </div>
                        </div>
                        <div class="bulk-controls" style="margin-top: 15px;">
                            <button class="btn btn-success" onclick="applyBulkMetadata()">Apply Metadata</button>
                            <button class="btn btn-secondary" onclick="clearSelection()">Clear Selection</button>
                        </div>
                    </div>
                </div>

                <div class="cat-grid" id="photos-grid">
                    <p>Click "Load Unlinked Photos" to start organizing photos</p>
                </div>
            </div>

            <!-- Bulk Operations Tab -->
            <div class="tab-panel" id="bulk-panel">
                <h2>Advanced Bulk Operations</h2>
                
                <div class="bulk-actions">
                    <h3>Litter Management</h3>
                    <p>Process entire litters efficiently with common metadata</p>
                    <div class="metadata-form">
                        <div class="form-group">
                            <label>Litter Date (Birth Date)</label>
                            <input type="date" id="litter-birth-date">
                        </div>
                        <div class="form-group">
                            <label>Mother Cat ID</label>
                            <input type="text" id="litter-mother-id" placeholder="Mother's cat ID">
                        </div>
                        <div class="form-group">
                            <label>Father Cat ID</label>
                            <input type="text" id="litter-father-id" placeholder="Father's cat ID">
                        </div>
                        <div class="form-group">
                            <label>Bloodline</label>
                            <input type="text" id="litter-bloodline" placeholder="Primary bloodline">
                        </div>
                        <div class="form-group">
                            <label>Number of Kittens</label>
                            <input type="number" id="litter-count" min="1" max="10">
                        </div>
                    </div>
                    <div class="bulk-controls">
                        <button class="btn btn-success" onclick="processLitterWizard()">Start Litter Wizard</button>
                        <button class="btn btn-primary" onclick="generateKittenProfiles()">Generate Kitten Profiles</button>
                    </div>
                </div>

                <div class="bulk-actions" style="margin-top: 30px;">
                    <h3>Bloodline Operations</h3>
                    <p>Apply bloodline information across multiple cats</p>
                    <div class="metadata-form">
                        <div class="form-group">
                            <label>Select Bloodline</label>
                            <select id="bloodline-select">
                                <option value="">Choose bloodline...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Champion Title</label>
                            <input type="text" id="champion-title" placeholder="e.g., International Champion">
                        </div>
                        <div class="form-group">
                            <label>Generation Level</label>
                            <select id="generation-level">
                                <option value="1">Parent Generation</option>
                                <option value="2">Grandparent Generation</option>
                                <option value="3">Great-Grandparent Generation</option>
                            </select>
                        </div>
                    </div>
                    <div class="bulk-controls">
                        <button class="btn btn-warning" onclick="propagateBloodline()">Propagate Bloodline</button>
                        <button class="btn btn-secondary" onclick="validatePedigree()">Validate Pedigree</button>
                    </div>
                </div>

                <div id="bulk-progress" style="display: none;">
                    <h3>Processing Progress</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div id="progress-text">Processing...</div>
                </div>
            </div>

            <!-- Pedigree Builder Tab -->
            <div class="tab-panel" id="pedigree-panel">
                <h2>Pedigree & Family Tree Builder</h2>
                
                <div class="bulk-actions">
                    <h3>Family Relationship Management</h3>
                    <div class="metadata-form">
                        <div class="form-group">
                            <label>Select Cat</label>
                            <select id="pedigree-cat-select">
                                <option value="">Choose cat...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Father</label>
                            <select id="father-select">
                                <option value="">Choose father...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Mother</label>
                            <select id="mother-select">
                                <option value="">Choose mother...</option>
                            </select>
                        </div>
                    </div>
                    <div class="bulk-controls">
                        <button class="btn btn-success" onclick="updateFamilyRelationships()">Update Relationships</button>
                        <button class="btn btn-primary" onclick="generateFamilyTree()">Generate Family Tree</button>
                        <button class="btn btn-warning" onclick="validateRelationships()">Validate Relationships</button>
                    </div>
                </div>

                <div class="family-tree" id="family-tree-display">
                    <h3>Family Tree Visualization</h3>
                    <p>Select a cat above to view its family tree</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Modals -->
    <div id="cat-edit-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Edit Cat Profile</h2>
            <div id="cat-edit-form">
                <!-- Cat editing form will be populated here -->
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let selectedPhotos = new Set();
        let allCats = [];
        let allPhotos = [];
        let isSelectMode = false;
        const apiBaseUrl = 'http://localhost:5002/api';
        const token = localStorage.getItem('admin_token');

        // Tab management
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;
                switchToTab(tabName);
            });
        });

        function switchToTab(tabName) {
            // Update buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Update panels
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            document.getElementById(`${tabName}-panel`).classList.add('active');

            // Load tab-specific data
            loadTabData(tabName);
        }

        function loadTabData(tabName) {
            switch(tabName) {
                case 'overview':
                    loadOverviewData();
                    break;
                case 'cats':
                    loadCatProfiles();
                    break;
                case 'photos':
                    // Photos loaded on demand
                    break;
                case 'bulk':
                    loadBloodlineOptions();
                    break;
                case 'pedigree':
                    loadPedigreeCats();
                    break;
            }
        }

        // API Helper Functions
        async function apiRequest(endpoint, options = {}) {
            const defaultOptions = {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            };

            const response = await fetch(`${apiBaseUrl}${endpoint}`, {
                ...defaultOptions,
                ...options,
                headers: { ...defaultOptions.headers, ...options.headers }
            });

            if (!response.ok) {
                throw new Error(`API Error: ${response.status} ${response.statusText}`);
            }

            return response.json();
        }

        function showLoading() {
            document.getElementById('loading-overlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        function showAlert(message, type = 'success') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            // Insert at top of current tab
            const activePanel = document.querySelector('.tab-panel.active');
            activePanel.insertBefore(alert, activePanel.firstChild);
            
            setTimeout(() => alert.remove(), 5000);
        }

        // Overview Tab Functions
        async function loadOverviewData() {
            try {
                showLoading();
                
                const [catsData, unlinkedData] = await Promise.all([
                    apiRequest('/Admin/cats/list-all'),
                    apiRequest('/Admin/photos/unlinked')
                ]);

                document.getElementById('total-cats').textContent = catsData.totalCats || 0;
                document.getElementById('total-photos').textContent = catsData.totalPhotos || 0;
                document.getElementById('unlinked-photos').textContent = unlinkedData.count || 0;
                
                // Count available kittens
                const availableKittens = catsData.cats?.filter(cat => 
                    cat.availabilityStatus === 'available' || 
                    cat.breedingStatus === 'available-kitten'
                ).length || 0;
                document.getElementById('available-kittens').textContent = availableKittens;

            } catch (error) {
                console.error('Error loading overview:', error);
                showAlert('Error loading overview data', 'error');
            } finally {
                hideLoading();
            }
        }

        // Cat Profiles Functions
        async function loadCatProfiles() {
            try {
                showLoading();
                const data = await apiRequest('/Admin/cats/list-all');
                allCats = data.cats || [];
                renderCatProfiles(allCats);
                populateBloodlineFilter();
            } catch (error) {
                console.error('Error loading cats:', error);
                showAlert('Error loading cat profiles', 'error');
            } finally {
                hideLoading();
            }
        }

        function renderCatProfiles(cats) {
            const grid = document.getElementById('cat-profiles-grid');
            
            if (!cats || cats.length === 0) {
                grid.innerHTML = '<p>No cat profiles found. Create your first cat profile to get started.</p>';
                return;
            }

            grid.innerHTML = cats.map(cat => `
                <div class="cat-card">
                    <div class="cat-header">
                        <div class="cat-name">${cat.catName || 'Unknown Cat'}</div>
                        <div class="cat-info">
                            ${cat.breed} • ${cat.bloodline || 'No bloodline'}<br>
                            ${cat.photoCount} photo${cat.photoCount !== 1 ? 's' : ''} • 
                            ${cat.breedingStatus || 'Unknown status'}
                        </div>
                    </div>
                    <div class="cat-photos">
                        ${cat.photos.slice(0, 6).map(photo => `
                            <img src="${photo.url}" alt="Photo" class="photo-thumbnail" 
                                 onclick="viewPhoto('${photo.s3Key}')">
                        `).join('')}
                        ${cat.photos.length > 6 ? `<span>+${cat.photos.length - 6} more</span>` : ''}
                    </div>
                    <div style="padding: 15px; border-top: 1px solid #eee;">
                        <button class="btn btn-primary" onclick="editCat('${cat.catId}', '${cat.catName}')">
                            Edit Profile
                        </button>
                        <button class="btn btn-secondary" onclick="viewPedigree('${cat.catId}')">
                            View Pedigree
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function populateBloodlineFilter() {
            const bloodlines = [...new Set(allCats.map(cat => cat.bloodline).filter(Boolean))];
            const select = document.getElementById('bloodline-filter');
            
            select.innerHTML = '<option value="">All Bloodlines</option>' +
                bloodlines.map(bloodline => `<option value="${bloodline}">${bloodline}</option>`).join('');
        }

        // Photo Management Functions
        async function loadUnlinkedPhotos() {
            try {
                showLoading();
                const data = await apiRequest('/Admin/photos/unlinked');
                allPhotos = data.photos || [];
                renderPhotos(allPhotos, 'Unlinked Photos');
            } catch (error) {
                console.error('Error loading unlinked photos:', error);
                showAlert('Error loading unlinked photos', 'error');
            } finally {
                hideLoading();
            }
        }

        async function loadAllPhotos() {
            try {
                showLoading();
                const data = await apiRequest('/Admin/cats/list-all');
                allPhotos = [];
                data.cats?.forEach(cat => {
                    cat.photos.forEach(photo => {
                        allPhotos.push({...photo, catName: cat.catName});
                    });
                });
                renderPhotos(allPhotos, 'All Photos');
            } catch (error) {
                console.error('Error loading all photos:', error);
                showAlert('Error loading all photos', 'error');
            } finally {
                hideLoading();
            }
        }

        function renderPhotos(photos, title) {
            const grid = document.getElementById('photos-grid');
            
            if (!photos || photos.length === 0) {
                grid.innerHTML = `<p>No ${title.toLowerCase()} found.</p>`;
                return;
            }

            grid.innerHTML = `
                <h3>${title} (${photos.length})</h3>
                <div class="cat-grid">
                    ${photos.map(photo => `
                        <div class="cat-card">
                            <div class="cat-header">
                                <div class="cat-name">${photo.catName || 'Unlinked Photo'}</div>
                                <div class="cat-info">
                                    Size: ${(photo.size / 1024).toFixed(1)} KB<br>
                                    Modified: ${new Date(photo.lastModified).toLocaleDateString()}
                                </div>
                            </div>
                            <div class="cat-photos">
                                <img src="${photo.url}" alt="Photo" class="photo-thumbnail ${selectedPhotos.has(photo.s3Key) ? 'selected' : ''}" 
                                     onclick="togglePhotoSelection('${photo.s3Key}')" style="width: 100%; height: 200px;">
                            </div>
                            <div style="padding: 15px; border-top: 1px solid #eee;">
                                <button class="btn btn-primary" onclick="editPhotoMetadata('${photo.s3Key}')">
                                    Edit Metadata
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function togglePhotoSelection(s3Key) {
            if (!isSelectMode) return;
            
            if (selectedPhotos.has(s3Key)) {
                selectedPhotos.delete(s3Key);
            } else {
                selectedPhotos.add(s3Key);
            }
            
            updateSelectedCount();
            
            // Update visual selection
            const img = document.querySelector(`img[onclick*="${s3Key}"]`);
            if (img) {
                img.classList.toggle('selected', selectedPhotos.has(s3Key));
            }
        }

        function bulkSelectPhotos() {
            isSelectMode = !isSelectMode;
            const button = event.target;
            const actionsDiv = document.getElementById('bulk-photo-actions');
            
            if (isSelectMode) {
                button.textContent = 'Exit Select Mode';
                button.className = 'btn btn-danger';
                actionsDiv.style.display = 'block';
                showAlert('Click photos to select them for bulk operations', 'warning');
            } else {
                button.textContent = 'Bulk Select Mode';
                button.className = 'btn btn-warning';
                actionsDiv.style.display = 'none';
                clearSelection();
            }
        }

        function clearSelection() {
            selectedPhotos.clear();
            updateSelectedCount();
            document.querySelectorAll('.photo-thumbnail.selected').forEach(img => {
                img.classList.remove('selected');
            });
        }

        function updateSelectedCount() {
            const countElement = document.getElementById('selected-count');
            if (selectedPhotos.size > 0) {
                countElement.textContent = `${selectedPhotos.size} selected`;
                countElement.style.display = 'inline';
            } else {
                countElement.style.display = 'none';
            }
        }

        async function applyBulkMetadata() {
            if (selectedPhotos.size === 0) {
                showAlert('No photos selected', 'warning');
                return;
            }

            const bulkData = {
                catName: document.getElementById('bulk-cat-name').value,
                breed: document.getElementById('bulk-breed').value,
                bloodline: document.getElementById('bulk-bloodline').value,
                age: document.getElementById('bulk-age').value
            };

            // Filter out empty values
            const metadata = Object.fromEntries(
                Object.entries(bulkData).filter(([key, value]) => value.trim() !== '')
            );

            if (Object.keys(metadata).length === 0) {
                showAlert('Please enter at least one metadata field', 'warning');
                return;
            }

            try {
                showLoading();
                const requests = Array.from(selectedPhotos).map(s3Key => ({
                    s3Key,
                    ...metadata
                }));

                const response = await apiRequest('/S3Metadata/bulk-update', {
                    method: 'POST',
                    body: JSON.stringify(requests)
                });

                showAlert(`Successfully updated ${selectedPhotos.size} photos`, 'success');
                clearSelection();
                
                // Refresh the current photo view
                if (allPhotos.length > 0) {
                    loadUnlinkedPhotos();
                }
            } catch (error) {
                console.error('Error applying bulk metadata:', error);
                showAlert('Error applying bulk metadata', 'error');
            } finally {
                hideLoading();
            }
        }

        // Placeholder functions for features to be implemented
        function refreshData() {
            loadTabData(document.querySelector('.tab-button.active').dataset.tab);
            showAlert('Data refreshed', 'success');
        }

        function exportMetadata() {
            showAlert('Export functionality coming soon', 'warning');
        }

        async function showS3Config() {
            try {
                const data = await apiRequest('/Admin/s3/config');
                alert(`S3 Configuration:\nBucket: ${data.s3Config.bucketName}\nRegion: ${data.s3Config.region}\nCDN: ${data.s3Config.useCdn ? 'Enabled' : 'Disabled'}`);
            } catch (error) {
                showAlert('Error loading S3 configuration', 'error');
            }
        }

        function searchCats() {
            showAlert('Search functionality coming soon', 'warning');
        }

        function clearFilters() {
            document.getElementById('cat-search').value = '';
            document.getElementById('breed-filter').value = '';
            document.getElementById('bloodline-filter').value = '';
            document.getElementById('status-filter').value = '';
            loadCatProfiles();
        }

        function createNewCatProfile() {
            showAlert('New cat profile functionality coming soon', 'warning');
        }

        function editCat(catId, catName) {
            showAlert(`Edit functionality for ${catName} coming soon`, 'warning');
        }

        function viewPedigree(catId) {
            showAlert('Pedigree view coming soon', 'warning');
        }

        function viewPhoto(s3Key) {
            window.open(`${apiBaseUrl.replace('/api', '')}/photos/${s3Key}`, '_blank');
        }

        function editPhotoMetadata(s3Key) {
            showAlert('Individual photo metadata editing coming soon', 'warning');
        }

        function loadBloodlineOptions() {
            // Populate bloodline options from existing cats
            if (allCats.length === 0) {
                loadCatProfiles().then(() => {
                    populateBloodlineSelect();
                });
            } else {
                populateBloodlineSelect();
            }
        }

        function populateBloodlineSelect() {
            const bloodlines = [...new Set(allCats.map(cat => cat.bloodline).filter(Boolean))];
            const select = document.getElementById('bloodline-select');
            
            select.innerHTML = '<option value="">Choose bloodline...</option>' +
                bloodlines.map(bloodline => `<option value="${bloodline}">${bloodline}</option>`).join('');
        }

        function loadPedigreeCats() {
            // Populate cat selects for pedigree management
            if (allCats.length === 0) {
                loadCatProfiles().then(() => {
                    populateCatSelects();
                });
            } else {
                populateCatSelects();
            }
        }

        function populateCatSelects() {
            const catOptions = allCats.map(cat => 
                `<option value="${cat.catId}">${cat.catName} (${cat.breed})</option>`
            ).join('');

            document.getElementById('pedigree-cat-select').innerHTML = '<option value="">Choose cat...</option>' + catOptions;
            document.getElementById('father-select').innerHTML = '<option value="">Choose father...</option>' + catOptions;
            document.getElementById('mother-select').innerHTML = '<option value="">Choose mother...</option>' + catOptions;
        }

        // Bulk operation functions
        function processLitterWizard() {
            showAlert('Litter wizard functionality coming soon', 'warning');
        }

        function generateKittenProfiles() {
            showAlert('Kitten profile generation coming soon', 'warning');
        }

        function propagateBloodline() {
            showAlert('Bloodline propagation coming soon', 'warning');
        }

        function validatePedigree() {
            showAlert('Pedigree validation coming soon', 'warning');
        }

        function updateFamilyRelationships() {
            showAlert('Family relationship updates coming soon', 'warning');
        }

        function generateFamilyTree() {
            showAlert('Family tree generation coming soon', 'warning');
        }

        function validateRelationships() {
            showAlert('Relationship validation coming soon', 'warning');
        }

        // Modal handling
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                e.target.closest('.modal').style.display = 'none';
            });
        });

        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            // Check for admin token
            if (!token) {
                alert('Admin authentication required. Please log in first.');
                window.location.href = 'admin.html';
                return;
            }

            // Load initial overview data
            loadOverviewData();
        });
    </script>
</body>
</html>
