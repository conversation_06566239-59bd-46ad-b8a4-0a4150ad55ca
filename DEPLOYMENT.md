# YendorCats Deployment Guide

This guide explains how to deploy the YendorCats application using Enhance Control Panel.

## Prerequisites

- Ubuntu 24.04 VPS server with Enhance Control Panel installed
- Backblaze B2 account with an S3-compatible bucket
- Domain name (optional, but recommended)

## Deployment Steps

### 1. Prepare Your Environment

1. Clone the repository to your local machine:
   ```bash
   git clone <repository-url>
   cd yendorcats
   ```

2. Create a `.env` file based on the `.env.example` file:
   ```bash
   cp .env.example .env
   ```

3. Edit the `.env` file with your credentials:
   ```bash
   nano .env
   ```

### 2. Deploy Using Enhance Control Panel

1. SSH into your Ubuntu 24.04 VPS server where Enhance Control Panel is installed.

2. Upload your project files to the server:
   ```bash
   # From your local machine
   scp -r /path/to/yendorcats user@your-server-ip:/path/to/destination
   ```

3. Log in to Enhance Control Panel:
   ```bash
   enhance login
   ```

4. Navigate to your project directory:
   ```bash
   cd /path/to/destination/yendorcats
   ```

5. Deploy the application:
   ```bash
   enhance deploy
   ```

6. Follow the prompts to configure your deployment.

### 3. Configure Backblaze B2

1. Create a Backblaze B2 bucket if you haven't already.

2. Create an application key with read and write access to the bucket.

3. Update the `.env` file on your server with the Backblaze B2 credentials:
   ```bash
   nano .env
   ```

4. Restart the application:
   ```bash
   enhance restart
   ```

### 4. Configure Domain (Optional)

1. Add your domain to Enhance Control Panel:
   ```bash
   enhance domain add yourdomain.com
   ```

2. Follow the prompts to configure SSL certificates.

### 5. Monitoring and Management

- View logs:
  ```bash
  enhance logs
  ```

- Restart services:
  ```bash
  enhance restart
  ```

- Stop services:
  ```bash
  enhance stop
  ```

- Start services:
  ```bash
  enhance start
  ```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Check your MariaDB credentials in the `.env` file.
   - Ensure the MariaDB service is running.

2. **S3 Upload Errors**:
   - Verify your Backblaze B2 credentials.
   - Check that the bucket exists and is accessible.
   - Ensure the application key has the correct permissions.

3. **Docker Issues**:
   - Check Docker logs: `enhance logs`
   - Ensure Docker and Docker Compose are installed correctly.

### Getting Help

If you encounter issues not covered in this guide, please:

1. Check the Enhance documentation: https://enhance.com/docs
2. Contact your system administrator
3. Open an issue in the project repository

## Backup and Restore

### Backing Up Data

1. Back up the MariaDB database:
   ```bash
   enhance backup db
   ```

2. Back up uploaded files:
   ```bash
   enhance backup files
   ```

### Restoring Data

1. Restore the MariaDB database:
   ```bash
   enhance restore db <backup-file>
   ```

2. Restore uploaded files:
   ```bash
   enhance restore files <backup-file>
   ```

## Updating the Application

1. Pull the latest changes:
   ```bash
   git pull
   ```

2. Rebuild and restart:
   ```bash
   enhance rebuild
   enhance restart
   ```
