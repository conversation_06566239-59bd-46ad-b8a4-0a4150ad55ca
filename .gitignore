
# IDE - Visual Studio / Rider
.vs/
.vscode/
.idea/
*.suo
*.user
*.userosscache
*.sln.docstates
.obsidian
wwwroot/
backend/YendorCats.API/wwwroot/

# Database
*.sqlite
*.sqlite-shm
*.sqlite-wal

# Frontend
frontend/node_modules/
frontend/.cache/

# wwwroot (static files served by backend)
backend/YendorCats.API/wwwroot/node_modules/
backend/YendorCats.API/wwwroot/package-lock.json
backend/YendorCats.API/wwwroot/**/*.bak

# Prevent cloud sync duplicates
**/* [0-9].*
**/* [0-9]/
**/*\ 2.*
**/*\ 3.*
**/*\ 4.*
**/*\ 2/
**/*\ 3/
**/*\ 4/

# Environment variables and secrets
.env
.env.local
.env.*.local
.env.production
.env.staging
*.env

# Security and credentials
appsettings.Production.json
appsettings.Staging.json
secrets.json

# Backend
backend/**/bin/
backend/**/obj/
backend/**/*.sqlite
backend/**/*.sqlite-shm
backend/**/*.sqlite-wal

# Certificates
*.key
*.crt
*.csr
backend/CabUCA.API/certs/

# Misc
.DS_Store
*.log
*.env
.env
.env.*
.env.local
.env.testing
.env.development
.crt
.csr
.key
.pfx
.p12
.pem
.crt
.csr
.key