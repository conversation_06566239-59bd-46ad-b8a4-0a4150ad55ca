
# IDE - Visual Studio / Rider
.vs/
.vscode/
.idea/
*.suo
*.user
*.userosscache
*.sln.docstates
.obsidian

# Database
*.sqlite
*.sqlite-shm
*.sqlite-wal

# Frontend
frontend/node_modules/
frontend/.cache/

# Backend
backend/**/bin/
backend/**/obj/
backend/**/*.sqlite
backend/**/*.sqlite-shm
backend/**/*.sqlite-wal

# Certificates
*.key
*.crt
*.csr
backend/CabUCA.API/certs/

# Misc
.DS_Store
*.log
*.env
.env
.env.*
.env.local
.env.testing
.env.development
.crt
.csr
.key
.pfx
.p12
.pem
.crt
.csr
.key