<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YendorCats Metadata Editor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        .btn {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-success {
            background-color: #27ae60;
        }
        .btn-success:hover {
            background-color: #229954;
        }
        .image-preview {
            max-width: 300px;
            max-height: 200px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .csv-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="loginSection" style="display: none;">
            <h1>🔐 Admin Login</h1>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">Username or Email:</label>
                    <input type="text" id="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" required>
                </div>
                <button type="submit" class="btn">Login</button>
            </form>
            <div id="loginError" style="color: red; margin-top: 10px; display: none;"></div>
        </div>

        <div id="editorSection" style="display: none;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h1>🏷️ YendorCats Metadata Editor</h1>
                <div>
                    <span id="userInfo" style="margin-right: 15px;"></span>
                    <button class="btn" onclick="logout()">Logout</button>
                </div>
            </div>
        
        <form id="metadataForm">
            <div class="grid">
                <div>
                    <div class="form-group">
                        <label for="s3Key">S3 Key (e.g., studs/louie/image.jpg):</label>
                        <input type="text" id="s3Key" required placeholder="studs/louie/image.jpg">
                    </div>
                    
                    <div class="form-group">
                        <label for="catName">Cat Name:</label>
                        <input type="text" id="catName" required placeholder="Louie">
                    </div>
                    
                    <div class="form-group">
                        <label for="age">Age (years):</label>
                        <input type="number" id="age" step="0.1" placeholder="3.5">
                    </div>
                    
                    <div class="form-group">
                        <label for="dateTaken">Date Taken:</label>
                        <input type="datetime-local" id="dateTaken">
                    </div>
                    
                    <div class="form-group">
                        <label for="breed">Breed:</label>
                        <select id="breed">
                            <option value="Maine Coon">Maine Coon</option>
                            <option value="Norwegian Forest Cat">Norwegian Forest Cat</option>
                            <option value="Siberian">Siberian</option>
                            <option value="Mixed">Mixed</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="gender">Gender:</label>
                        <select id="gender">
                            <option value="">Select Gender</option>
                            <option value="M">Male</option>
                            <option value="F">Female</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <div class="form-group">
                        <label for="color">Color/Pattern:</label>
                        <input type="text" id="color" placeholder="Brown Tabby">
                    </div>
                    
                    <div class="form-group">
                        <label for="personality">Personality:</label>
                        <input type="text" id="personality" placeholder="Gentle and playful">
                    </div>
                    
                    <div class="form-group">
                        <label for="bloodline">Bloodline:</label>
                        <input type="text" id="bloodline" placeholder="Champion bloodline">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description:</label>
                        <textarea id="description" placeholder="Beautiful Maine Coon with excellent features..."></textarea>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="button" class="btn" onclick="addToCSV()">Add to CSV</button>
                <button type="button" class="btn btn-success" onclick="downloadCSV()">Download CSV</button>
                <button type="button" class="btn" onclick="uploadMetadataToS3()" style="background-color: #e74c3c;">Upload to S3</button>
                <button type="button" class="btn" onclick="clearForm()">Clear Form</button>
            </div>
        </form>
        
        <div class="csv-output" id="csvOutput">
            <strong>CSV Output (copy this or download):</strong><br>
            s3_key,cat_name,age,date_taken,description,breed,gender,color,personality,bloodline<br>
        </div>
    </div>

    <script>
        let csvData = [];
        let authToken = null;
        const API_BASE = 'http://localhost:5002/api';

        // Check authentication on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });

        async function checkAuth() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                showLogin();
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/AdminAuth/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const user = await response.json();
                    authToken = token;
                    showEditor(user);
                } else {
                    localStorage.removeItem('admin_token');
                    showLogin();
                }
            } catch (error) {
                console.error('Auth check failed:', error);
                showLogin();
            }
        }

        function showLogin() {
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('editorSection').style.display = 'none';
        }

        function showEditor(user) {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('editorSection').style.display = 'block';
            document.getElementById('userInfo').textContent = `Welcome, ${user.username} (${user.role})`;
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('loginError');

            try {
                const response = await fetch(`${API_BASE}/AdminAuth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    localStorage.setItem('admin_token', result.token);
                    authToken = result.token;
                    showEditor(result.user);
                    errorDiv.style.display = 'none';
                } else {
                    errorDiv.textContent = result.message || 'Login failed';
                    errorDiv.style.display = 'block';
                }
            } catch (error) {
                errorDiv.textContent = 'Network error. Please try again.';
                errorDiv.style.display = 'block';
            }
        });

        function logout() {
            localStorage.removeItem('admin_token');
            authToken = null;
            showLogin();
        }

        async function uploadMetadataToS3() {
            if (!authToken) {
                alert('Please login first');
                return;
            }

            const s3Key = document.getElementById('s3Key').value;
            const catName = document.getElementById('catName').value;

            if (!s3Key || !catName) {
                alert('Please fill in S3 Key and Cat Name');
                return;
            }

            const metadata = {
                s3Key: s3Key,
                catName: catName,
                age: document.getElementById('age').value || '0',
                dateTaken: document.getElementById('dateTaken').value ?
                    new Date(document.getElementById('dateTaken').value).toISOString() : '',
                description: document.getElementById('description').value,
                breed: document.getElementById('breed').value,
                gender: document.getElementById('gender').value,
                color: document.getElementById('color').value,
                personality: document.getElementById('personality').value,
                bloodline: document.getElementById('bloodline').value
            };

            try {
                const response = await fetch(`${API_BASE}/S3Metadata/update`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(metadata)
                });

                if (response.ok) {
                    alert('Metadata uploaded successfully!');
                    clearForm();
                } else {
                    const error = await response.text();
                    alert(`Upload failed: ${error}`);
                }
            } catch (error) {
                alert(`Network error: ${error.message}`);
            }
        }
        
        function addToCSV() {
            const form = document.getElementById('metadataForm');
            const formData = new FormData(form);
            
            const row = {
                s3_key: document.getElementById('s3Key').value,
                cat_name: document.getElementById('catName').value,
                age: document.getElementById('age').value || '0',
                date_taken: document.getElementById('dateTaken').value ? 
                    new Date(document.getElementById('dateTaken').value).toISOString() : '',
                description: document.getElementById('description').value,
                breed: document.getElementById('breed').value,
                gender: document.getElementById('gender').value,
                color: document.getElementById('color').value,
                personality: document.getElementById('personality').value,
                bloodline: document.getElementById('bloodline').value
            };
            
            if (!row.s3_key || !row.cat_name) {
                alert('Please fill in S3 Key and Cat Name');
                return;
            }
            
            csvData.push(row);
            updateCSVDisplay();
            clearForm();
            
            alert('Added to CSV! Add more entries or download when ready.');
        }
        
        function updateCSVDisplay() {
            const output = document.getElementById('csvOutput');
            let csvText = 's3_key,cat_name,age,date_taken,description,breed,gender,color,personality,bloodline\n';
            
            csvData.forEach(row => {
                csvText += `"${row.s3_key}","${row.cat_name}","${row.age}","${row.date_taken}","${row.description}","${row.breed}","${row.gender}","${row.color}","${row.personality}","${row.bloodline}"\n`;
            });
            
            output.innerHTML = '<strong>CSV Output (' + csvData.length + ' entries):</strong><br>' + csvText;
        }
        
        function downloadCSV() {
            if (csvData.length === 0) {
                alert('No data to download. Add some entries first.');
                return;
            }
            
            let csvContent = 's3_key,cat_name,age,date_taken,description,breed,gender,color,personality,bloodline\n';
            
            csvData.forEach(row => {
                csvContent += `"${row.s3_key}","${row.cat_name}","${row.age}","${row.date_taken}","${row.description}","${row.breed}","${row.gender}","${row.color}","${row.personality}","${row.bloodline}"\n`;
            });
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'yendorcats_metadata.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }
        
        function clearForm() {
            document.getElementById('metadataForm').reset();
            // Set current date/time as default
            const now = new Date();
            now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
            document.getElementById('dateTaken').value = now.toISOString().slice(0, 16);
        }
        
        // Initialize with current date/time
        clearForm();
    </script>
</body>
</html>
