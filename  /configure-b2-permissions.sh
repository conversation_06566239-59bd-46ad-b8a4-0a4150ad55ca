#!/bin/bash

# Backblaze B2 Bucket Configuration Script
# Sets up public read access while maintaining admin control

echo "🔧 Configuring Backblaze B2 Bucket Permissions"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUCKET_NAME="yendor"
B2_CLI_PATH="b2"  # Adjust if b2 CLI is in different location

echo -e "${BLUE}Step 1: Install B2 CLI (if not already installed)${NC}"
echo "Run: pip install b2"
echo ""

echo -e "${BLUE}Step 2: Authorize B2 CLI${NC}"
echo "Run: b2 authorize-account <your_key_id> <your_application_key>"
echo ""

echo -e "${BLUE}Step 3: Set Bucket to Public Read${NC}"
echo "This allows anyone to read files but restricts other operations"
echo ""

# Check if b2 CLI is available
if ! command -v $B2_CLI_PATH &> /dev/null; then
    echo -e "${RED}❌ B2 CLI not found. Please install it first:${NC}"
    echo "pip install b2"
    exit 1
fi

echo -e "${YELLOW}Setting bucket '$BUCKET_NAME' to public read...${NC}"

# Set bucket to public read
$B2_CLI_PATH update-bucket --bucketType allPublic $BUCKET_NAME

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Bucket set to public read successfully!${NC}"
else
    echo -e "${RED}❌ Failed to update bucket permissions${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}Step 4: Verify Configuration${NC}"

# Get bucket info
echo "Bucket information:"
$B2_CLI_PATH get-bucket $BUCKET_NAME

echo ""
echo -e "${GREEN}🎉 Configuration Complete!${NC}"
echo ""
echo -e "${BLUE}What this means:${NC}"
echo "✅ Anyone can READ files from your bucket"
echo "✅ Only your API key can UPLOAD/DELETE/MODIFY files"
echo "✅ Visitors can view cat photos without authentication"
echo "✅ Admin operations remain secure"
echo ""
echo -e "${BLUE}Public URL format:${NC}"
echo "https://f004.backblazeb2.com/file/yendor/path/to/image.jpg"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Update your application to use public URLs for viewing"
echo "2. Keep using authenticated API for admin operations"
echo "3. Test public access with a browser"
