#!/bin/bash
# Vault Manual Intervention Demo Scenarios

export VAULT_ADDR='http://127.0.0.1:8200'

echo "🎭 Vault Manual Intervention Demo"
echo "================================="
echo ""
echo "This script demonstrates common scenarios where you need to manually intervene with Vault."
echo "Each scenario shows the problem, diagnosis, and solution."
echo ""

# Function to wait for user
wait_for_user() {
    echo ""
    read -p "Press Enter to continue..."
    echo ""
}

# Function to show command and execute
show_and_execute() {
    echo "💻 Command: $1"
    echo "📤 Output:"
    eval "$1" 2>&1 | sed 's/^/   /'
    echo ""
}

# Scenario 1: Vault Server Down
demo_server_down() {
    echo "🎬 SCENARIO 1: Vault Server is Down"
    echo "==================================="
    echo ""
    echo "Problem: Your application can't connect to Vault"
    echo "Symptoms: Connection refused errors"
    echo ""
    
    # Simulate the problem
    echo "🔧 Simulating the problem..."
    pkill vault 2>/dev/null || true
    sleep 2
    
    echo "🔍 Diagnosing the issue:"
    show_and_execute "vault status"
    
    echo "🔍 Checking if process is running:"
    show_and_execute "ps aux | grep vault | grep -v grep"
    
    echo "💡 Solution: Start the Vault server"
    show_and_execute "vault server -config=~/.vault-config.hcl > ~/.vault-server.log 2>&1 &"
    
    echo "⏳ Waiting for server to start..."
    sleep 3
    
    echo "✅ Verifying the fix:"
    show_and_execute "vault status"
    
    wait_for_user
}

# Scenario 2: Vault is Sealed
demo_vault_sealed() {
    echo "🎬 SCENARIO 2: Vault is Sealed"
    echo "=============================="
    echo ""
    echo "Problem: Vault is running but sealed (encrypted)"
    echo "Symptoms: 'Sealed: true' in status"
    echo ""
    
    # Ensure Vault is running first
    if ! pgrep vault >/dev/null; then
        vault server -config=~/.vault-config.hcl > ~/.vault-server.log 2>&1 &
        sleep 3
    fi
    
    echo "🔍 Current status:"
    show_and_execute "vault status"
    
    if vault status -format=json | jq -r '.sealed' | grep -q "false"; then
        echo "🔧 Sealing Vault to demonstrate..."
        show_and_execute "vault operator seal"
    fi
    
    echo "🔍 Now Vault is sealed:"
    show_and_execute "vault status"
    
    echo "💡 Solution: Unseal with 3 keys"
    
    if [ -f ~/.vault-unseal-keys.txt ]; then
        local count=0
        while read key && [ $count -lt 3 ]; do
            ((count++))
            echo "🔑 Using unseal key $count/3:"
            show_and_execute "vault operator unseal $key"
        done < ~/.vault-unseal-keys.txt
    else
        echo "❌ Unseal keys not found. You would need to:"
        echo "   1. Locate your unseal keys"
        echo "   2. Run: vault operator unseal <key1>"
        echo "   3. Run: vault operator unseal <key2>"
        echo "   4. Run: vault operator unseal <key3>"
    fi
    
    echo "✅ Verifying Vault is unsealed:"
    show_and_execute "vault status"
    
    wait_for_user
}

# Scenario 3: Token Expired
demo_token_expired() {
    echo "🎬 SCENARIO 3: Application Token Expired"
    echo "========================================"
    echo ""
    echo "Problem: Application can't authenticate with Vault"
    echo "Symptoms: 'permission denied' errors"
    echo ""
    
    # Ensure we're authenticated first
    if [ -f ~/.vault-root-token.txt ]; then
        vault auth "$(cat ~/.vault-root-token.txt)" >/dev/null 2>&1
    fi
    
    echo "🔧 Creating a short-lived token to demonstrate expiry:"
    SHORT_TOKEN=$(vault token create -ttl=10s -format=json | jq -r '.auth.client_token')
    echo "   Token: ${SHORT_TOKEN:0:20}..."
    
    echo "🔍 Testing token immediately:"
    show_and_execute "VAULT_TOKEN=$SHORT_TOKEN vault token lookup"
    
    echo "⏳ Waiting for token to expire (10 seconds)..."
    sleep 11
    
    echo "🔍 Testing expired token:"
    show_and_execute "VAULT_TOKEN=$SHORT_TOKEN vault token lookup"
    
    echo "💡 Solution: Create new token and update application"
    
    echo "🔑 Authenticating with root token:"
    show_and_execute "vault auth $(cat ~/.vault-root-token.txt)"
    
    echo "🎫 Creating new long-lived token:"
    NEW_TOKEN=$(vault token create -ttl=8760h -format=json | jq -r '.auth.client_token')
    echo "   New token: ${NEW_TOKEN:0:20}..."
    
    echo "🔧 Updating application configuration:"
    if [ -f "backend/YendorCats.API/appsettings.Development.json" ]; then
        cp backend/YendorCats.API/appsettings.Development.json backend/YendorCats.API/appsettings.Development.json.backup
        sed -i.tmp "s/hvs\.[^\"]*/$NEW_TOKEN/g" backend/YendorCats.API/appsettings.Development.json
        echo "   ✅ Configuration updated"
        echo "   💾 Backup saved as appsettings.Development.json.backup"
    else
        echo "   ⚠️  Application config not found"
    fi
    
    echo "✅ Testing new token:"
    show_and_execute "VAULT_TOKEN=$NEW_TOKEN vault token lookup"
    
    wait_for_user
}

# Scenario 4: Secrets Missing
demo_secrets_missing() {
    echo "🎬 SCENARIO 4: Secrets Accidentally Deleted"
    echo "==========================================="
    echo ""
    echo "Problem: Application secrets are missing"
    echo "Symptoms: 'No value found' errors"
    echo ""
    
    # Ensure we're authenticated
    if [ -f ~/.vault-root-token.txt ]; then
        vault auth "$(cat ~/.vault-root-token.txt)" >/dev/null 2>&1
    fi
    
    echo "🔍 Checking current secrets:"
    show_and_execute "vault kv get secret/yendorcats/app-secrets"
    
    echo "🔧 Simulating accidental deletion:"
    show_and_execute "vault kv delete secret/yendorcats/app-secrets"
    
    echo "🔍 Confirming secrets are gone:"
    show_and_execute "vault kv get secret/yendorcats/app-secrets"
    
    echo "💡 Solution 1: Restore from version history"
    echo "🔍 Checking secret metadata:"
    show_and_execute "vault kv metadata secret/yendorcats/app-secrets"
    
    echo "🔄 Undeleting the secret:"
    show_and_execute "vault kv undelete -versions=1 secret/yendorcats/app-secrets"
    
    echo "✅ Verifying restoration:"
    show_and_execute "vault kv get secret/yendorcats/app-secrets"
    
    echo ""
    echo "💡 Alternative Solution: Recreate from backup"
    echo "   If version history was destroyed, you would:"
    echo "   1. Re-enable KV engine: vault secrets enable -path=secret kv-v2"
    echo "   2. Restore from backup: vault kv put secret/yendorcats/app-secrets @backup.json"
    echo "   3. Or recreate manually with your credentials"
    
    wait_for_user
}

# Scenario 5: Performance Issues
demo_performance_issues() {
    echo "🎬 SCENARIO 5: Vault Performance Issues"
    echo "======================================"
    echo ""
    echo "Problem: Vault is responding slowly"
    echo "Symptoms: High response times, timeouts"
    echo ""
    
    echo "🔍 Testing current response time:"
    start_time=$(date +%s%N)
    vault kv get secret/yendorcats/app-secrets >/dev/null 2>&1
    end_time=$(date +%s%N)
    response_time=$(( (end_time - start_time) / 1000000 ))
    echo "   Response time: ${response_time}ms"
    
    echo "🔍 Checking Vault health:"
    show_and_execute "vault read sys/health"
    
    echo "🔍 Checking system resources:"
    show_and_execute "ps aux | grep vault | grep -v grep"
    
    echo "🔍 Checking recent logs for errors:"
    if [ -f ~/.vault-server.log ]; then
        echo "   Last 5 log entries:"
        tail -5 ~/.vault-server.log | sed 's/^/   /'
    fi
    
    echo "💡 Solutions for performance issues:"
    echo "   1. Restart Vault server"
    echo "   2. Check system resources (CPU, memory, disk)"
    echo "   3. Review Vault configuration"
    echo "   4. Enable debug logging to identify bottlenecks"
    echo "   5. Consider storage backend optimization"
    
    echo ""
    echo "🔧 Demonstrating restart solution:"
    echo "   Stopping Vault..."
    pkill vault 2>/dev/null || true
    sleep 2
    
    echo "   Starting Vault..."
    vault server -config=~/.vault-config.hcl > ~/.vault-server.log 2>&1 &
    sleep 3
    
    # Unseal if needed
    if vault status -format=json | jq -r '.sealed' | grep -q "true"; then
        echo "   Unsealing Vault..."
        head -3 ~/.vault-unseal-keys.txt | while read key; do
            vault operator unseal "$key" >/dev/null 2>&1
        done
    fi
    
    echo "✅ Testing response time after restart:"
    start_time=$(date +%s%N)
    vault kv get secret/yendorcats/app-secrets >/dev/null 2>&1
    end_time=$(date +%s%N)
    response_time=$(( (end_time - start_time) / 1000000 ))
    echo "   Response time: ${response_time}ms"
    
    wait_for_user
}

# Main menu
show_demo_menu() {
    echo "🎭 Choose a scenario to demonstrate:"
    echo "1. Vault Server Down"
    echo "2. Vault is Sealed"
    echo "3. Token Expired"
    echo "4. Secrets Missing"
    echo "5. Performance Issues"
    echo "6. Run All Scenarios"
    echo "7. Exit"
    echo ""
    read -p "Choose an option (1-7): " choice
}

# Main script
echo "⚠️  WARNING: This demo will modify your Vault state for demonstration purposes."
echo "Make sure you have backups and are running in a development environment."
echo ""
read -p "Do you want to continue? (y/N): " confirm

if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "Demo cancelled."
    exit 0
fi

while true; do
    echo ""
    show_demo_menu
    
    case $choice in
        1)
            demo_server_down
            ;;
        2)
            demo_vault_sealed
            ;;
        3)
            demo_token_expired
            ;;
        4)
            demo_secrets_missing
            ;;
        5)
            demo_performance_issues
            ;;
        6)
            echo "🎬 Running all scenarios..."
            demo_server_down
            demo_vault_sealed
            demo_token_expired
            demo_secrets_missing
            demo_performance_issues
            echo "🎉 All scenarios completed!"
            ;;
        7)
            echo "👋 Demo finished!"
            exit 0
            ;;
        *)
            echo "❌ Invalid option. Please choose 1-7."
            ;;
    esac
done
