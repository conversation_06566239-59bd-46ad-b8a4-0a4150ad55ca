#!/bin/bash
# Quick Vault Development Setup Script

echo "🔐 Setting up Hashi<PERSON>orp Vault for Development"
echo "============================================="

# Check if Vault is installed
if ! command -v vault &> /dev/null; then
    echo "📦 Installing HashiCorp Vault..."
    
    # Detect OS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew tap hashicorp/tap
            brew install hashicorp/tap/vault
        else
            echo "❌ Please install Homebrew first: https://brew.sh"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
        sudo apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main"
        sudo apt-get update && sudo apt-get install vault
    else
        echo "❌ Unsupported OS. Please install Vault manually from: https://www.vaultproject.io/downloads"
        exit 1
    fi
fi

echo "✅ Vault installed successfully"

# Create Vault data directory
mkdir -p ~/.vault-data

# Create Vault config file
cat > ~/.vault-config.hcl << 'EOF'
ui = true

storage "file" {
  path = "~/.vault-data"
}

listener "tcp" {
  address = "127.0.0.1:8200"
  tls_disable = true
}

api_addr = "http://127.0.0.1:8200"
disable_mlock = true
EOF

echo "📝 Created Vault configuration at ~/.vault-config.hcl"

# Create startup script
cat > ~/.vault-start.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting HashiCorp Vault..."
export VAULT_ADDR='http://127.0.0.1:8200'

# Start Vault server in background
vault server -config=~/.vault-config.hcl > ~/.vault-server.log 2>&1 &
VAULT_PID=$!
echo $VAULT_PID > ~/.vault-server.pid

echo "⏳ Waiting for Vault to start..."
sleep 3

# Check if Vault is running
if vault status > /dev/null 2>&1; then
    echo "✅ Vault is running at http://127.0.0.1:8200"
    echo "📊 Vault UI available at: http://127.0.0.1:8200/ui"
    echo "🔑 Server PID: $VAULT_PID (saved to ~/.vault-server.pid)"
else
    echo "❌ Failed to start Vault. Check ~/.vault-server.log for details"
fi
EOF

chmod +x ~/.vault-start.sh

# Create stop script
cat > ~/.vault-stop.sh << 'EOF'
#!/bin/bash
echo "🛑 Stopping HashiCorp Vault..."

if [ -f ~/.vault-server.pid ]; then
    PID=$(cat ~/.vault-server.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        echo "✅ Vault server stopped (PID: $PID)"
        rm ~/.vault-server.pid
    else
        echo "⚠️  Vault server was not running"
        rm ~/.vault-server.pid
    fi
else
    echo "⚠️  No PID file found. Vault may not be running"
fi
EOF

chmod +x ~/.vault-stop.sh

echo ""
echo "🎉 Vault setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Start Vault: ~/.vault-start.sh"
echo "2. Initialize Vault (first time only)"
echo "3. Store your B2 credentials"
echo "4. Update your application configuration"
echo ""
echo "🔧 Management commands:"
echo "  Start:  ~/.vault-start.sh"
echo "  Stop:   ~/.vault-stop.sh"
echo "  Status: vault status"
echo ""
