#!/bin/bash
set -e

echo "Setting up SSL certificates for Vault on Enhance Control Panel..."

# Create TLS directory
mkdir -p /home/<USER>/vault/config/tls

# Option 1: Self-signed certificate (development)
echo "Generating a self-signed certificate for development purposes..."
openssl req -x509 -newkey rsa:4096 -keyout /home/<USER>/vault/config/tls/vault.key \
    -out /home/<USER>/vault/config/tls/vault.crt -days 365 -nodes \
    -subj "/C=AU/ST=State/L=City/O=YendorCats/CN=vault.yourdomain.com"

# Set permissions
chmod 600 /home/<USER>/vault/config/tls/vault.key
chmod 644 /home/<USER>/vault/config/tls/vault.crt

echo "Self-signed SSL certificate generated successfully at /home/<USER>/vault/config/tls/"
echo "Please ensure that the path '/home/<USER>/vault/' is correct for your Enhance Control Panel setup."
echo "For production, consider using Enhance Control Panel's SSL management to generate Let's Encrypt certificates instead."
echo "If the path is incorrect, modify the script with the correct domain or path before running."
