#!/bin/bash
# Vault Troubleshooting and Manual Intervention Script

export VAULT_ADDR='http://127.0.0.1:8200'

echo "🔧 Vault Troubleshooting Tool"
echo "============================"
echo ""

# Function to check Vault status
check_vault_status() {
    echo "🔍 Checking Vault status..."
    
    if vault status >/dev/null 2>&1; then
        STATUS=$(vault status -format=json)
        SEALED=$(echo "$STATUS" | jq -r '.sealed')
        INITIALIZED=$(echo "$STATUS" | jq -r '.initialized')
        
        echo "✅ Vault is accessible"
        echo "   Initialized: $INITIALIZED"
        echo "   Sealed: $SEALED"
        
        if [ "$SEALED" = "true" ]; then
            echo "⚠️  Vault is SEALED - needs unsealing"
            return 1
        fi
        
        return 0
    else
        echo "❌ Vault is not accessible"
        return 2
    fi
}

# Function to start Vault
start_vault() {
    echo ""
    echo "🚀 Starting Vault server..."
    
    # Kill any existing Vault process
    pkill vault 2>/dev/null || true
    sleep 2
    
    # Start Vault
    if [ -f ~/.vault-config.hcl ]; then
        vault server -config=~/.vault-config.hcl > ~/.vault-server.log 2>&1 &
        VAULT_PID=$!
        echo $VAULT_PID > ~/.vault-server.pid
        
        echo "⏳ Waiting for Vault to start..."
        sleep 5
        
        if vault status >/dev/null 2>&1; then
            echo "✅ Vault started successfully (PID: $VAULT_PID)"
            return 0
        else
            echo "❌ Failed to start Vault"
            echo "📋 Check logs: cat ~/.vault-server.log"
            return 1
        fi
    else
        echo "❌ Vault config not found at ~/.vault-config.hcl"
        echo "   Run: ./scripts/quick-vault-setup.sh"
        return 1
    fi
}

# Function to unseal Vault
unseal_vault() {
    echo ""
    echo "🔓 Unsealing Vault..."
    
    if [ ! -f ~/.vault-unseal-keys.txt ]; then
        echo "❌ Unseal keys not found at ~/.vault-unseal-keys.txt"
        echo "   You may need to reinitialize Vault"
        return 1
    fi
    
    # Unseal with first 3 keys
    local count=0
    while read key && [ $count -lt 3 ]; do
        vault operator unseal "$key" >/dev/null 2>&1
        ((count++))
        echo "   Unseal progress: $count/3"
    done < ~/.vault-unseal-keys.txt
    
    # Check if unsealed
    if vault status -format=json | jq -r '.sealed' | grep -q "false"; then
        echo "✅ Vault unsealed successfully"
        return 0
    else
        echo "❌ Failed to unseal Vault"
        return 1
    fi
}

# Function to check secrets
check_secrets() {
    echo ""
    echo "🔍 Checking secrets accessibility..."
    
    # First authenticate if needed
    if ! vault token lookup >/dev/null 2>&1; then
        echo "🔑 Authenticating with root token..."
        if [ -f ~/.vault-root-token.txt ]; then
            vault auth "$(cat ~/.vault-root-token.txt)" >/dev/null 2>&1
        else
            echo "❌ Root token not found"
            return 1
        fi
    fi
    
    # Check if secrets exist
    if vault kv get secret/yendorcats/app-secrets >/dev/null 2>&1; then
        echo "✅ Secrets are accessible"
        
        # Show secret keys
        echo "📋 Available secrets:"
        vault kv get -format=json secret/yendorcats/app-secrets | jq -r '.data.data | keys[]' | sed 's/^/   - /'
        
        return 0
    else
        echo "❌ Cannot access secrets"
        echo "   Path: secret/yendorcats/app-secrets"
        return 1
    fi
}

# Function to fix token issues
fix_token() {
    echo ""
    echo "🎫 Fixing token issues..."
    
    # Authenticate with root token
    if [ -f ~/.vault-root-token.txt ]; then
        vault auth "$(cat ~/.vault-root-token.txt)" >/dev/null 2>&1
        echo "✅ Authenticated with root token"
    else
        echo "❌ Root token not found"
        return 1
    fi
    
    # Create new application token
    echo "🔧 Creating new application token..."
    NEW_TOKEN=$(vault token create -policy=default -ttl=8760h -format=json | jq -r '.auth.client_token')
    
    if [ -n "$NEW_TOKEN" ] && [ "$NEW_TOKEN" != "null" ]; then
        echo "✅ New token created: ${NEW_TOKEN:0:20}..."
        
        # Update application configuration
        if [ -f "backend/YendorCats.API/appsettings.Development.json" ]; then
            sed -i.bak "s/hvs\.[^\"]*/$NEW_TOKEN/g" backend/YendorCats.API/appsettings.Development.json
            echo "✅ Updated application configuration"
        fi
        
        return 0
    else
        echo "❌ Failed to create new token"
        return 1
    fi
}

# Function to recreate secrets
recreate_secrets() {
    echo ""
    echo "💾 Recreating secrets..."
    
    # Check if KV engine is enabled
    if ! vault secrets list | grep -q "secret/"; then
        echo "🔧 Enabling KV secrets engine..."
        vault secrets enable -path=secret kv-v2
    fi
    
    echo "📝 Please provide your credentials:"
    read -p "Backblaze B2 Key ID: " B2_KEY_ID
    read -s -p "Backblaze B2 Application Key: " B2_SECRET_KEY
    echo ""
    
    if [ -z "$B2_KEY_ID" ] || [ -z "$B2_SECRET_KEY" ]; then
        echo "❌ Credentials cannot be empty"
        return 1
    fi
    
    # Store secrets
    vault kv put secret/yendorcats/app-secrets \
        DbConnectionString="Server=localhost;Database=YendorCats;User=root;Password=password;Port=3306;" \
        JwtSecret="$(openssl rand -base64 64)" \
        JwtIssuer="YendorCatsApi" \
        JwtAudience="YendorCatsClients" \
        JwtExpiryMinutes=60 \
        RefreshExpiryDays=7 \
        S3AccessKey="$B2_KEY_ID" \
        S3SecretKey="$B2_SECRET_KEY" \
        S3SessionToken="" \
        ApiKey="$(openssl rand -hex 32)"
    
    echo "✅ Secrets recreated successfully"
}

# Function to show diagnostics
show_diagnostics() {
    echo ""
    echo "🔍 Vault Diagnostics"
    echo "==================="
    
    echo ""
    echo "📊 Server Status:"
    vault status 2>/dev/null || echo "❌ Cannot get status"
    
    echo ""
    echo "🔐 Authentication:"
    vault token lookup 2>/dev/null || echo "❌ Not authenticated"
    
    echo ""
    echo "🗂️  Secrets Engines:"
    vault secrets list 2>/dev/null || echo "❌ Cannot list secrets engines"
    
    echo ""
    echo "👥 Auth Methods:"
    vault auth list 2>/dev/null || echo "❌ Cannot list auth methods"
    
    echo ""
    echo "📋 Recent Logs (last 10 lines):"
    if [ -f ~/.vault-server.log ]; then
        tail -10 ~/.vault-server.log
    else
        echo "❌ No log file found"
    fi
    
    echo ""
    echo "💾 Storage Usage:"
    if [ -d ~/.vault-data ]; then
        du -sh ~/.vault-data
    else
        echo "❌ No data directory found"
    fi
}

# Main menu
show_menu() {
    echo ""
    echo "🛠️  What would you like to do?"
    echo "1. Check Vault status"
    echo "2. Start Vault server"
    echo "3. Unseal Vault"
    echo "4. Check secrets"
    echo "5. Fix token issues"
    echo "6. Recreate secrets"
    echo "7. Show diagnostics"
    echo "8. Run full health check"
    echo "9. Exit"
    echo ""
    read -p "Choose an option (1-9): " choice
}

# Full health check
full_health_check() {
    echo ""
    echo "🏥 Full Health Check"
    echo "==================="
    
    local issues=0
    
    # Check 1: Vault accessibility
    if ! check_vault_status; then
        ((issues++))
        echo "❌ Issue 1: Vault not accessible or sealed"
    fi
    
    # Check 2: Secrets accessibility
    if ! check_secrets; then
        ((issues++))
        echo "❌ Issue 2: Secrets not accessible"
    fi
    
    # Check 3: Application configuration
    if [ -f "backend/YendorCats.API/appsettings.Development.json" ]; then
        if grep -q "your-vault-token-will-be-set-here" backend/YendorCats.API/appsettings.Development.json; then
            ((issues++))
            echo "❌ Issue 3: Application token not configured"
        fi
    else
        ((issues++))
        echo "❌ Issue 3: Application configuration not found"
    fi
    
    echo ""
    if [ $issues -eq 0 ]; then
        echo "🎉 All checks passed! Vault is healthy."
    else
        echo "⚠️  Found $issues issue(s). Use the menu options to fix them."
    fi
}

# Main script
while true; do
    show_menu
    
    case $choice in
        1)
            check_vault_status
            ;;
        2)
            start_vault
            ;;
        3)
            unseal_vault
            ;;
        4)
            check_secrets
            ;;
        5)
            fix_token
            ;;
        6)
            recreate_secrets
            ;;
        7)
            show_diagnostics
            ;;
        8)
            full_health_check
            ;;
        9)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid option. Please choose 1-9."
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
done
