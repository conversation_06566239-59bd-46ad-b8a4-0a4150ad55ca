#!/bin/bash
set -e

echo "Deploying Vault on Enhance Control Panel..."

# Navigate to the Vault directory
cd /home/<USER>/vault

# Start Vault container
docker-compose up -d

# Wait for Vault to start
echo "Waiting for Vault to start..."
sleep 30

# Check Vault status
docker-compose logs vault

echo "Vault deployment completed!"
echo "Access Vault UI at: https://vault.yourdomain.com:8200"
echo "Remember to initialize and unseal Vault!"
echo "Please ensure that the path '/home/<USER>/vault/' and the domain 'vault.yourdomain.com' are correct for your setup. If not, modify the configuration files before running this script."
