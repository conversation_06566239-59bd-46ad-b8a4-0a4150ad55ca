#!/bin/bash
# setup-secure-backup.sh
# Quick setup script for secure Vault backup procedures

set -euo pipefail

echo "🔐 Setting up Secure Vault Backup System"
echo "========================================"

# Configuration
BACKUP_BASE_DIR="/secure/vault-backups"
SCRIPTS_DIR="/usr/local/bin"
LOG_DIR="/var/log/vault"
VAULT_USER="vault"
BACKUP_USER="vault-backup"

# Create necessary directories
echo "📁 Creating directory structure..."
sudo mkdir -p "$BACKUP_BASE_DIR" "$LOG_DIR"
sudo mkdir -p "$SCRIPTS_DIR"

# Set up backup user and permissions
echo "👤 Setting up backup user..."
if ! id "$BACKUP_USER" &>/dev/null; then
    sudo useradd -r -s /bin/bash -d /home/<USER>"$BACKUP_USER"
fi

# Create backup group
if ! getent group vault-backup-operators &>/dev/null; then
    sudo groupadd vault-backup-operators
fi

sudo usermod -a -G vault-backup-operators "$BACKUP_USER"

# Set directory permissions
sudo chown -R "$BACKUP_USER:vault-backup-operators" "$BACKUP_BASE_DIR"
sudo chmod 750 "$BACKUP_BASE_DIR"

# Generate GPG key for backups
echo "🔑 Setting up GPG encryption..."
sudo -u "$BACKUP_USER" bash << 'EOF'
if ! gpg --list-keys <EMAIL> &>/dev/null; then
    echo "Generating GPG key for backup encryption..."
    gpg --batch --generate-key << GPGEOF
Key-Type: RSA
Key-Length: 4096
Subkey-Type: RSA
Subkey-Length: 4096
Name-Real: Vault Backup Key
Name-Email: <EMAIL>
Expire-Date: 2y
Passphrase: 
%commit
GPGEOF
    
    echo "✓ GPG key generated"
    
    # Export public key
    gpg --armor --export <EMAIL> > /home/<USER>/vault-backup-public.key
    echo "✓ Public key exported to /home/<USER>/vault-backup-public.key"
else
    echo "✓ GPG key already exists"
fi
EOF

# Create main backup script
echo "📝 Creating backup script..."
sudo tee "$SCRIPTS_DIR/vault-secure-backup.sh" > /dev/null << 'EOF'
#!/bin/bash
# Secure Vault Backup Script

set -euo pipefail

# Configuration
VAULT_DATA_DIR="/vault/data"
BACKUP_BASE_DIR="/secure/vault-backups"
LOG_FILE="/var/log/vault/backup.log"
GPG_RECIPIENT="<EMAIL>"
RETENTION_DAYS=30

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Error handling
trap 'log "ERROR: Backup failed at line $LINENO"' ERR

log "Starting secure Vault backup"

# Pre-backup checks
if ! vault status >/dev/null 2>&1; then
    log "ERROR: Vault is not accessible"
    exit 1
fi

if ! gpg --list-keys "$GPG_RECIPIENT" >/dev/null 2>&1; then
    log "ERROR: GPG key not found for $GPG_RECIPIENT"
    exit 1
fi

# Create timestamped backup
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE_DIR/$DATE"
mkdir -p "$BACKUP_DIR"

# Create Vault snapshot
log "Creating Vault snapshot"
if command -v vault >/dev/null && vault status >/dev/null 2>&1; then
    vault operator raft snapshot save "$BACKUP_DIR/vault-snapshot.snap" || {
        # Fallback to file-based backup
        log "Snapshot failed, using file-based backup"
        tar -czf "$BACKUP_DIR/vault-data.tar.gz" -C /vault data config 2>/dev/null || true
    }
else
    # File-based backup for development
    log "Using file-based backup"
    tar -czf "$BACKUP_DIR/vault-data.tar.gz" -C /vault data config 2>/dev/null || true
fi

# Create comprehensive backup archive
log "Creating backup archive"
tar -czf "$BACKUP_DIR/vault-complete-backup.tar.gz" -C "$BACKUP_DIR" . 2>/dev/null

# Encrypt backup
log "Encrypting backup"
gpg --trust-model always --encrypt --armor \
    --recipient "$GPG_RECIPIENT" \
    --output "$BACKUP_DIR/vault-backup-$DATE.gpg" \
    "$BACKUP_DIR/vault-complete-backup.tar.gz"

# Generate checksum
sha256sum "$BACKUP_DIR/vault-backup-$DATE.gpg" > "$BACKUP_DIR/vault-backup-$DATE.sha256"

# Secure cleanup
log "Cleaning up temporary files"
find "$BACKUP_DIR" -name "*.tar.gz" -o -name "*.snap" | xargs -r shred -vfz -n 3
rm -f "$BACKUP_DIR"/*.tar.gz "$BACKUP_DIR"/*.snap

# Set secure permissions
chmod 600 "$BACKUP_DIR"/*.gpg "$BACKUP_DIR"/*.sha256
chown vault-backup:vault-backup-operators "$BACKUP_DIR"/*.gpg "$BACKUP_DIR"/*.sha256

# Cleanup old backups
log "Cleaning up old backups"
find "$BACKUP_BASE_DIR" -type d -name "20*" -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true

# Verify backup
log "Verifying backup"
if gpg --verify "$BACKUP_DIR/vault-backup-$DATE.gpg" >/dev/null 2>&1; then
    log "✓ Backup completed successfully: vault-backup-$DATE.gpg"
    echo "SUCCESS: Backup completed at $(date)"
else
    log "❌ Backup verification failed"
    exit 1
fi
EOF

# Create backup verification script
echo "🔍 Creating verification script..."
sudo tee "$SCRIPTS_DIR/verify-vault-backup.sh" > /dev/null << 'EOF'
#!/bin/bash
# Verify Vault Backup Integrity

BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup-file.gpg>"
    exit 1
fi

echo "Verifying backup: $(basename $BACKUP_FILE)"

# Check file exists and is readable
if [ ! -r "$BACKUP_FILE" ]; then
    echo "❌ Backup file not readable"
    exit 1
fi

# Verify GPG signature
if gpg --verify "$BACKUP_FILE" >/dev/null 2>&1; then
    echo "✓ GPG signature valid"
else
    echo "❌ GPG signature invalid"
    exit 1
fi

# Test decryption without extracting
TEMP_FILE=$(mktemp)
trap "rm -f $TEMP_FILE" EXIT

if gpg --decrypt "$BACKUP_FILE" > "$TEMP_FILE" 2>/dev/null; then
    echo "✓ Decryption successful"
    
    # Check if it's a valid tar archive
    if tar -tzf "$TEMP_FILE" >/dev/null 2>&1; then
        echo "✓ Archive integrity verified"
        FILE_COUNT=$(tar -tzf "$TEMP_FILE" | wc -l)
        echo "  Archive contains $FILE_COUNT files"
    else
        echo "❌ Archive corrupted"
        exit 1
    fi
else
    echo "❌ Decryption failed"
    exit 1
fi

echo "✅ Backup verification successful"
EOF

# Create simple restore script
echo "🔄 Creating restore script..."
sudo tee "$SCRIPTS_DIR/restore-vault-backup.sh" > /dev/null << 'EOF'
#!/bin/bash
# Simple Vault Restore Script

BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup-file.gpg>"
    exit 1
fi

echo "⚠️  CRITICAL: This will restore Vault from backup"
echo "Current Vault data will be backed up to /vault/data.backup.$(date +%Y%m%d_%H%M%S)"
read -p "Type 'RESTORE' to confirm: " CONFIRM

if [ "$CONFIRM" != "RESTORE" ]; then
    echo "Restore cancelled"
    exit 1
fi

# Create recovery directory
RECOVERY_DIR="/tmp/vault-recovery-$(date +%s)"
mkdir -p "$RECOVERY_DIR"
trap "rm -rf $RECOVERY_DIR" EXIT

echo "Decrypting backup..."
gpg --decrypt "$BACKUP_FILE" > "$RECOVERY_DIR/backup.tar.gz"

echo "Extracting backup..."
tar -xzf "$RECOVERY_DIR/backup.tar.gz" -C "$RECOVERY_DIR"

echo "Stopping Vault..."
systemctl stop vault 2>/dev/null || docker-compose -f /vault/docker-compose.yml stop 2>/dev/null || true

echo "Backing up current data..."
if [ -d "/vault/data" ]; then
    mv "/vault/data" "/vault/data.backup.$(date +%Y%m%d_%H%M%S)"
fi

echo "Restoring from backup..."
if [ -f "$RECOVERY_DIR/vault-snapshot.snap" ]; then
    # Restore from snapshot
    mkdir -p /vault/data
    vault operator raft snapshot restore "$RECOVERY_DIR/vault-snapshot.snap" || {
        echo "Snapshot restore failed, trying file restore..."
        tar -xzf "$RECOVERY_DIR/vault-data.tar.gz" -C /vault/ 2>/dev/null || true
    }
else
    # Restore from file backup
    tar -xzf "$RECOVERY_DIR/vault-data.tar.gz" -C /vault/ 2>/dev/null || true
fi

echo "Setting permissions..."
chown -R vault:vault /vault/ 2>/dev/null || true

echo "Starting Vault..."
systemctl start vault 2>/dev/null || docker-compose -f /vault/docker-compose.yml up -d 2>/dev/null || true

echo "✅ Restore completed. Please verify Vault status and unseal if necessary."
EOF

# Make scripts executable
sudo chmod +x "$SCRIPTS_DIR"/vault-*.sh "$SCRIPTS_DIR"/verify-*.sh "$SCRIPTS_DIR"/restore-*.sh

# Set up sudo permissions
echo "🔐 Setting up sudo permissions..."
sudo tee /etc/sudoers.d/vault-backup > /dev/null << EOF
# Vault backup permissions
%vault-backup-operators ALL=(vault) NOPASSWD: $SCRIPTS_DIR/vault-secure-backup.sh
%vault-backup-operators ALL=(vault) NOPASSWD: $SCRIPTS_DIR/verify-vault-backup.sh
%vault-backup-operators ALL=(root) NOPASSWD: $SCRIPTS_DIR/restore-vault-backup.sh
%vault-backup-operators ALL=(root) NOPASSWD: /bin/systemctl stop vault
%vault-backup-operators ALL=(root) NOPASSWD: /bin/systemctl start vault
EOF

# Create cron job for automated backups
echo "⏰ Setting up automated backups..."
sudo -u "$BACKUP_USER" bash << 'EOF'
# Add daily backup at 2 AM
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/vault-secure-backup.sh") | crontab -

# Add weekly verification on Sunday at 3 AM
LATEST_BACKUP_CMD='find /secure/vault-backups -name "vault-backup-*.gpg" -mtime -7 | head -1'
(crontab -l 2>/dev/null; echo "0 3 * * 0 /usr/local/bin/verify-vault-backup.sh \$($LATEST_BACKUP_CMD)") | crontab -
EOF

# Create log rotation
echo "📋 Setting up log rotation..."
sudo tee /etc/logrotate.d/vault-backup > /dev/null << 'EOF'
/var/log/vault/backup.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 0640 vault-backup vault-backup-operators
}
EOF

# Test the setup
echo "🧪 Testing backup setup..."
if sudo -u "$BACKUP_USER" "$SCRIPTS_DIR/vault-secure-backup.sh"; then
    echo "✅ Test backup successful!"
    
    # Find the test backup and verify it
    LATEST_BACKUP=$(find "$BACKUP_BASE_DIR" -name "vault-backup-*.gpg" -mtime -1 | head -1)
    if [ -n "$LATEST_BACKUP" ]; then
        echo "🔍 Verifying test backup..."
        if sudo -u "$BACKUP_USER" "$SCRIPTS_DIR/verify-vault-backup.sh" "$LATEST_BACKUP"; then
            echo "✅ Backup verification successful!"
        else
            echo "❌ Backup verification failed"
        fi
    fi
else
    echo "❌ Test backup failed - check configuration"
fi

echo ""
echo "🎉 Secure Vault Backup Setup Complete!"
echo "======================================"
echo ""
echo "📁 Backup Directory: $BACKUP_BASE_DIR"
echo "📝 Scripts Location: $SCRIPTS_DIR"
echo "📋 Log File: /var/log/vault/backup.log"
echo ""
echo "🔧 Available Commands:"
echo "  sudo -u $BACKUP_USER $SCRIPTS_DIR/vault-secure-backup.sh"
echo "  sudo -u $BACKUP_USER $SCRIPTS_DIR/verify-vault-backup.sh <backup-file>"
echo "  sudo $SCRIPTS_DIR/restore-vault-backup.sh <backup-file>"
echo ""
echo "⏰ Automated Schedule:"
echo "  Daily backup: 2:00 AM"
echo "  Weekly verification: Sunday 3:00 AM"
echo ""
echo "🔑 GPG Public Key: /home/<USER>/vault-backup-public.key"
echo ""
echo "⚠️  IMPORTANT: Store the GPG private key securely!"
echo "   Export with: sudo -u $BACKUP_USER gpg --armor --export-secret-keys <EMAIL>"
echo ""
