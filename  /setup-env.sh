#!/bin/bash

# YendorCats Environment Variables Setup Script
# This script helps set up secure environment variables for the application

echo "🔐 YendorCats Environment Variables Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
    fi
    
    eval "$var_name='$input'"
}

# Function to prompt for sensitive input (hidden)
prompt_sensitive() {
    local prompt="$1"
    local var_name="$2"
    
    read -s -p "$prompt: " input
    echo ""
    eval "$var_name='$input'"
}

echo -e "${BLUE}Setting up S3/Backblaze B2 credentials...${NC}"
echo ""

# S3 Credentials
prompt_with_default "Enter your Backblaze B2 Key ID" "004d0cd685eb5360000000001" "S3_ACCESS_KEY"
prompt_sensitive "Enter your Backblaze B2 Application Key" "S3_SECRET_KEY"

echo ""
echo -e "${BLUE}Setting up default admin credentials...${NC}"
echo ""

# Admin Credentials
prompt_with_default "Default admin username" "admin" "ADMIN_USERNAME"
prompt_with_default "Default admin email" "<EMAIL>" "ADMIN_EMAIL"
prompt_sensitive "Default admin password (min 8 characters)" "ADMIN_PASSWORD"

# Validate password length
while [ ${#ADMIN_PASSWORD} -lt 8 ]; do
    echo -e "${RED}Password must be at least 8 characters long${NC}"
    prompt_sensitive "Default admin password (min 8 characters)" "ADMIN_PASSWORD"
done

echo ""
echo -e "${BLUE}Setting up JWT configuration...${NC}"
echo ""

# Generate random JWT secret if not provided
JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
prompt_with_default "JWT Secret (auto-generated)" "$JWT_SECRET" "JWT_SECRET"

# Create .env file
ENV_FILE=".env"
echo -e "${YELLOW}Creating $ENV_FILE file...${NC}"

cat > "$ENV_FILE" << EOF
# YendorCats Environment Variables
# Generated on $(date)

# S3/Backblaze B2 Configuration
YENDOR_S3_ACCESS_KEY=$S3_ACCESS_KEY
YENDOR_S3_SECRET_KEY=$S3_SECRET_KEY

# Default Admin User Configuration
YENDOR_DEFAULT_ADMIN_USERNAME=$ADMIN_USERNAME
YENDOR_DEFAULT_ADMIN_EMAIL=$ADMIN_EMAIL
YENDOR_DEFAULT_ADMIN_PASSWORD=$ADMIN_PASSWORD

# JWT Configuration
YENDOR_JWT_SECRET=$JWT_SECRET

# Database Configuration (optional - uses in-memory by default)
# YENDOR_DB_CONNECTION_STRING=Server=localhost;Database=YendorCats;User=root;Password=password;

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=http://localhost:5002
EOF

# Create .env.example file (without sensitive data)
echo -e "${YELLOW}Creating .env.example file...${NC}"

cat > ".env.example" << EOF
# YendorCats Environment Variables Example
# Copy this file to .env and fill in your actual values

# S3/Backblaze B2 Configuration
YENDOR_S3_ACCESS_KEY=your_b2_key_id_here
YENDOR_S3_SECRET_KEY=your_b2_application_key_here

# Default Admin User Configuration
YENDOR_DEFAULT_ADMIN_USERNAME=admin
YENDOR_DEFAULT_ADMIN_EMAIL=<EMAIL>
YENDOR_DEFAULT_ADMIN_PASSWORD=your_secure_password_here

# JWT Configuration
YENDOR_JWT_SECRET=your_jwt_secret_here

# Database Configuration (optional)
# YENDOR_DB_CONNECTION_STRING=Server=localhost;Database=YendorCats;User=root;Password=password;

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=http://localhost:5002
EOF

# Set appropriate permissions
chmod 600 "$ENV_FILE"
chmod 644 ".env.example"

echo ""
echo -e "${GREEN}✅ Environment setup completed!${NC}"
echo ""
echo -e "${BLUE}Files created:${NC}"
echo "  📄 $ENV_FILE (secure - contains your credentials)"
echo "  📄 .env.example (template for others)"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Add .env to your .gitignore file (if not already added)"
echo "2. Load environment variables before running the application:"
echo "   ${YELLOW}source .env${NC} (bash/zsh)"
echo "   ${YELLOW}export \$(cat .env | xargs)${NC} (alternative)"
echo ""
echo "3. Or use dotenv with your application:"
echo "   ${YELLOW}dotnet run${NC} (will automatically load .env in development)"
echo ""
echo -e "${BLUE}Security reminders:${NC}"
echo "🔒 Never commit .env files to version control"
echo "🔒 Use different credentials for production"
echo "🔒 Rotate credentials regularly"
echo "🔒 Use environment-specific .env files (.env.production, .env.staging)"

# Add to .gitignore if it exists
if [ -f ".gitignore" ]; then
    if ! grep -q "^\.env$" .gitignore; then
        echo "" >> .gitignore
        echo "# Environment variables" >> .gitignore
        echo ".env" >> .gitignore
        echo ".env.local" >> .gitignore
        echo ".env.*.local" >> .gitignore
        echo -e "${GREEN}✅ Added .env to .gitignore${NC}"
    else
        echo -e "${YELLOW}ℹ️  .env already in .gitignore${NC}"
    fi
fi

echo ""
echo -e "${GREEN}🎉 Setup complete! Your credentials are now secure.${NC}"
