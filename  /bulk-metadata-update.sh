#!/bin/bash

# Bulk S3 Metadata Update Script for YendorCats
# This script adds IPTC-style metadata to existing S3 images

# Configuration
BUCKET_NAME="yendor"
ENDPOINT_URL="https://s3.us-west-004.backblazeb2.com"
PROFILE="backblaze"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🏷️  YendorCats S3 Metadata Bulk Update Tool${NC}"
echo "=============================================="

# Function to update metadata for a single file
update_file_metadata() {
    local s3_key="$1"
    local cat_name="$2"
    local age="$3"
    local date_taken="$4"
    local description="$5"
    
    echo -e "${YELLOW}📝 Updating: $s3_key${NC}"
    
    # Copy file to itself with new metadata (this updates metadata without re-uploading)
    aws s3 cp "s3://$BUCKET_NAME/$s3_key" "s3://$BUCKET_NAME/$s3_key" \
        --endpoint-url="$ENDPOINT_URL" \
        --profile="$PROFILE" \
        --metadata-directive REPLACE \
        --metadata "cat-name=$cat_name,age=$age,date-taken=$date_taken,description=$description,content-type=image/jpeg" \
        --content-type "image/jpeg"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Updated: $cat_name${NC}"
    else
        echo -e "${RED}❌ Failed: $s3_key${NC}"
    fi
}

# Function to update all images in a category folder
update_category_metadata() {
    local category="$1"
    
    echo -e "${BLUE}📂 Processing category: $category${NC}"
    
    # List all folders in the category
    aws s3 ls "s3://$BUCKET_NAME/$category/" \
        --endpoint-url="$ENDPOINT_URL" \
        --profile="$PROFILE" \
        --recursive | while read -r line; do
        
        # Extract file path
        file_path=$(echo "$line" | awk '{print $4}')
        
        # Skip .bzEmpty files
        if [[ "$file_path" == *".bzEmpty"* ]]; then
            continue
        fi
        
        # Extract cat name from path (e.g., studs/louie/image.jpg -> louie)
        cat_folder=$(echo "$file_path" | cut -d'/' -f2)
        
        # Skip if no cat folder
        if [ -z "$cat_folder" ] || [ "$cat_folder" == "$category" ]; then
            continue
        fi
        
        # Capitalize cat name
        cat_name=$(echo "$cat_folder" | sed 's/\b\w/\U&/g')
        
        # Set default metadata
        age="3.0"  # Default age
        date_taken=$(date -u +"%Y-%m-%dT%H:%M:%SZ")  # Current date as fallback
        description="Beautiful Maine Coon cat from Yendor Cattery"
        
        # Update metadata
        update_file_metadata "$file_path" "$cat_name" "$age" "$date_taken" "$description"
        
        # Small delay to avoid rate limiting
        sleep 0.5
    done
}

# Function to update specific cat with custom metadata
update_specific_cat() {
    local cat_name="$1"
    local age="$2"
    local description="$3"
    local category="$4"
    
    echo -e "${BLUE}🐱 Updating all images for: $cat_name${NC}"
    
    # Find all images for this cat
    aws s3 ls "s3://$BUCKET_NAME/$category/$cat_name/" \
        --endpoint-url="$ENDPOINT_URL" \
        --profile="$PROFILE" | while read -r line; do
        
        # Extract filename
        filename=$(echo "$line" | awk '{print $4}')
        
        # Skip .bzEmpty files
        if [[ "$filename" == *".bzEmpty"* ]]; then
            continue
        fi
        
        # Full S3 key
        s3_key="$category/$cat_name/$filename"
        
        # Use current date as fallback
        date_taken=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
        
        # Update metadata
        update_file_metadata "$s3_key" "$cat_name" "$age" "$date_taken" "$description"
        
        sleep 0.5
    done
}

# Main menu
echo ""
echo "Choose an option:"
echo "1. Update all images in a category (studs/queens/kittens)"
echo "2. Update specific cat with custom metadata"
echo "3. Update single file"
echo "4. List current metadata for a file"
echo ""
read -p "Enter choice (1-4): " choice

case $choice in
    1)
        echo "Available categories: studs, queens, kittens, gallery"
        read -p "Enter category: " category
        update_category_metadata "$category"
        ;;
    2)
        read -p "Enter cat name: " cat_name
        read -p "Enter age (e.g., 3.5): " age
        read -p "Enter description: " description
        read -p "Enter category (studs/queens/kittens): " category
        update_specific_cat "$cat_name" "$age" "$description" "$category"
        ;;
    3)
        read -p "Enter S3 key (e.g., studs/louie/image.jpg): " s3_key
        read -p "Enter cat name: " cat_name
        read -p "Enter age: " age
        read -p "Enter description: " description
        date_taken=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
        update_file_metadata "$s3_key" "$cat_name" "$age" "$date_taken" "$description"
        ;;
    4)
        read -p "Enter S3 key to check: " s3_key
        echo -e "${BLUE}📋 Current metadata for: $s3_key${NC}"
        aws s3api head-object \
            --bucket "$BUCKET_NAME" \
            --key "$s3_key" \
            --endpoint-url="$ENDPOINT_URL" \
            --profile="$PROFILE" \
            --query 'Metadata'
        ;;
    *)
        echo -e "${RED}Invalid choice${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}🎉 Metadata update completed!${NC}"
