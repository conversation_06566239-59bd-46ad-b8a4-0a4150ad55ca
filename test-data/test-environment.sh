#!/bin/bash
# Test script to verify the YendorCats testing environment

set -e

echo "====== YendorCats Testing Environment Validation ======"
echo "Starting services..."

# Start all services
docker-compose -f docker-compose.testing.yml up -d

# Function to check if all services are healthy
check_health() {
  echo "Checking service health..."
  UNHEALTHY=$(docker-compose -f docker-compose.testing.yml ps | grep -v "healthy" | grep -v "no health" | grep -v "NAME" | wc -l)
  
  if [ $UNHEALTHY -eq 0 ]; then
    echo "✅ All services are healthy!"
    return 0
  else
    echo "❌ Some services are not healthy yet."
    docker-compose -f docker-compose.testing.yml ps
    return 1
  fi
}

# Wait for services to be healthy (max 2 minutes)
echo "Waiting for services to be ready..."
ATTEMPTS=0
MAX_ATTEMPTS=24  # 24 * 5 seconds = 2 minutes

until check_health || [ $ATTEMPTS -eq $MAX_ATTEMPTS ]; do
  echo "Waiting for services to be healthy... (Attempt $ATTEMPTS/$MAX_ATTEMPTS)"
  sleep 5
  ATTEMPTS=$((ATTEMPTS + 1))
done

if [ $ATTEMPTS -eq $MAX_ATTEMPTS ]; then
  echo "❌ Services didn't become healthy within the timeout period."
  docker-compose -f docker-compose.testing.yml logs
  exit 1
fi

echo "====== Testing API Service ======"
# Test API health endpoint
if curl -s -f http://localhost:5100/health > /dev/null; then
  echo "✅ API health check successful"
else
  echo "❌ API health check failed"
  exit 1
fi

# Test API Swagger endpoint
if curl -s -f http://localhost:5100/swagger/index.html > /dev/null; then
  echo "✅ API Swagger documentation is accessible"
else
  echo "❌ API Swagger documentation is not accessible"
  exit 1
fi

echo "====== Testing MinIO Service ======"
# Check if MinIO is responding
if curl -s -f http://localhost:9000/minio/health/live > /dev/null; then
  echo "✅ MinIO health check successful"
else
  echo "❌ MinIO health check failed"
  exit 1
fi

# Verify the test bucket exists
if docker exec yendorcats-test-createbuckets /usr/bin/mc ls myminio/yendor-test > /dev/null; then
  echo "✅ MinIO test bucket exists"
else
  echo "❌ MinIO test bucket doesn't exist"
  exit 1
fi

echo "====== Testing Nginx Frontend Service ======"
# Check if Nginx is serving the frontend
if curl -s -f http://localhost:8080/ | grep -q "DOCTYPE html"; then
  echo "✅ Nginx is serving the frontend correctly"
else
  echo "❌ Nginx is not serving the frontend correctly"
  exit 1
fi

echo "====== Testing File Uploader Service ======"
# Check if file uploader service is responding
if curl -s -f http://localhost:5102/health > /dev/null; then
  echo "✅ File Uploader health check successful"
else
  echo "❌ File Uploader health check failed"
  exit 1
fi

echo "====== Testing Database ======"
# Check if the database has been initialized correctly
if docker exec yendorcats-test-api /bin/bash -c "sqlite3 /app/data/yendorcats-test.db 'SELECT count(*) FROM AdminUsers;'" | grep -q "[1-9]"; then
  echo "✅ Database contains admin users"
else
  echo "❌ Database doesn't contain admin users"
  exit 1
fi

echo "====== All Tests Passed! ======"
echo "The YendorCats testing environment is fully operational."

# Optional: Stop services if not needed anymore
# echo "Stopping services..."
# docker-compose -f docker-compose.testing.yml down

exit 0