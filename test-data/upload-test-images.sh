#!/bin/bash
# <PERSON><PERSON>t to upload test images to MinIO S3 storage for testing environment

set -e

echo "Starting upload of test images to MinIO..."

# MinIO connection details
MINIO_ENDPOINT="http://minio:9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
BUCKET_NAME="yendor-test"

# Configure AWS CLI for MinIO
export AWS_ACCESS_KEY_ID=$MINIO_ACCESS_KEY
export AWS_SECRET_ACCESS_KEY=$MINIO_SECRET_KEY
export AWS_DEFAULT_REGION=us-east-1

# Wait for <PERSON><PERSON> to be ready
echo "Waiting for Min<PERSON> to be ready..."
until curl -s -f -o /dev/null $MINIO_ENDPOINT/minio/health/live; do
  echo "MinIO not ready yet, waiting..."
  sleep 2
done

# Check if the bucket exists, create if it doesn't
if ! aws --endpoint-url=$MINIO_ENDPOINT s3 ls "s3://$BUCKET_NAME" &>/dev/null; then
  echo "Creating bucket: $BUCKET_NAME"
  aws --endpoint-url=$MINIO_ENDPOINT s3 mb "s3://$BUCKET_NAME"
  # Set bucket policy to allow public read access
  aws --endpoint-url=$MINIO_ENDPOINT s3api put-bucket-policy \
    --bucket "$BUCKET_NAME" \
    --policy "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetObject\"],\"Resource\":[\"arn:aws:s3:::$BUCKET_NAME/*\"]}]}"
fi

# Create directories for categories if they don't exist
CATEGORIES=("queens" "studs" "kittens" "gallery")
for CATEGORY in "${CATEGORIES[@]}"; do
  echo "Ensuring directory exists: $CATEGORY/"
  mkdir -p "test-images/$CATEGORY"
done

# Upload queen images
echo "Uploading queen images..."
find test-images/queens -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) -exec \
  aws --endpoint-url=$MINIO_ENDPOINT s3 cp {} "s3://$BUCKET_NAME/queens/" --acl public-read \;

# Upload stud images
echo "Uploading stud images..."
find test-images/studs -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) -exec \
  aws --endpoint-url=$MINIO_ENDPOINT s3 cp {} "s3://$BUCKET_NAME/studs/" --acl public-read \;

# Upload kitten images
echo "Uploading kitten images..."
find test-images/kittens -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) -exec \
  aws --endpoint-url=$MINIO_ENDPOINT s3 cp {} "s3://$BUCKET_NAME/kittens/" --acl public-read \;

# Upload gallery images
echo "Uploading gallery images..."
find test-images/gallery -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) -exec \
  aws --endpoint-url=$MINIO_ENDPOINT s3 cp {} "s3://$BUCKET_NAME/gallery/" --acl public-read \;

echo "All test images uploaded successfully to MinIO!"