#!/bin/bash
# Master script to prepare test images with proper metadata and upload to MinIO

set -e

echo "====== YendorCats Test Image Preparation ======"
echo "This script will process images with IPTC metadata and upload them to MinIO"

# Create directory structure if it doesn't exist
mkdir -p test-images/{queens,studs,kittens,gallery}

# Check if we have any test images
image_count=$(find test-images -type f | wc -l)
if [ "$image_count" -eq 0 ]; then
  echo "⚠️ No test images found in the test-images directory."
  echo "Please add some test images following the README.md guidelines before running this script."
  exit 1
fi

echo "Found $image_count test images."

# Step 1: Build Docker image for IPTC metadata processing
echo "Building Docker image for IPTC metadata processing..."
docker build -t yendorcats-iptc-processor -f test-data/Dockerfile.iptc-processor test-data/

# Step 2: Process images with IPTC metadata
echo "Processing images with IPTC metadata..."
docker run --rm -v "$(pwd)/test-images:/app/test-images" yendorcats-iptc-processor

# Step 3: Start the testing environment if not already running
if ! docker ps | grep -q "yendorcats-test-minio"; then
  echo "Starting testing environment..."
  docker-compose -f docker-compose.testing.yml up -d minio createbuckets
  
  # Wait for MinIO to be ready
  echo "Waiting for MinIO to be ready..."
  until docker exec yendorcats-test-minio curl -s -f -o /dev/null http://localhost:9000/minio/health/live; do
    echo "MinIO not ready yet, waiting..."
    sleep 2
  done
fi

# Step 4: Upload images with S3 metadata
echo "Uploading images with S3 metadata..."
chmod +x test-data/upload-test-images-with-metadata.sh
./test-data/upload-test-images-with-metadata.sh

echo "====== Test Image Preparation Complete ======"
echo "Images have been processed with both IPTC metadata and S3 object metadata."
echo "You can now test the application's image display functionality."
echo ""
echo "To verify the metadata on a specific image:"
echo "  docker exec yendorcats-test-minio mc cat myminio/yendor-test/queens/Loretta-3-080824-1.jpg | exiftool -"
echo ""
echo "To see the S3 object metadata:"
echo "  docker exec yendorcats-test-minio mc stat myminio/yendor-test/queens/Loretta-3-080824-1.jpg"