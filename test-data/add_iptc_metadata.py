#!/usr/bin/env python3
"""
Add IPTC metadata to test images for YendorCats testing.

This script adds the necessary IPTC metadata to test images:
- Person Shown (tag 0x0228) for cat name
- Model Age (tag 0x0229) for cat age
- Caption/Abstract for description

Required: pip install iptcinfo3 pillow
"""

import os
import sys
from iptcinfo3 import IPTCInfo
from PIL import Image
import io

# Define metadata for cats
CATS_METADATA = {
    # Queens
    "queens/Loretta-3-080824-1.jpg": {
        "name": "Loretta",
        "age": "3 years",
        "color": "Blue Silver Tabby",
        "description": "<PERSON><PERSON> is a beautiful blue silver tabby queen with excellent temperament."
    },
    "queens/Rosie-3-030324-1.png": {
        "name": "<PERSON>",
        "age": "3 years",
        "color": "Black Tortie",
        "description": "<PERSON> is our stunning black tortie queen. She has incredible eyes and a playful personality."
    },
    "queens/Sera-1.11-030324-1.png": {
        "name": "<PERSON><PERSON>",
        "age": "1 year 11 months",
        "color": "Black Silver Tabby",
        "description": "<PERSON><PERSON> is our gorgeous black silver tabby. She has a sweet temperament and loves attention."
    },
    
    # Studs
    "studs/Dennis-10.3-030324-1.jpg": {
        "name": "<PERSON>",
        "age": "10 years 3 months",
        "color": "Red Silver Tabby",
        "description": "Dennis is our magnificent red silver tabby stud. He has sired many beautiful kittens."
    },
    "studs/Rapper-16.2-030324-1.png": {
        "name": "Rapper",
        "age": "16 years 2 months",
        "color": "Brown Tabby",
        "description": "Rapper is our veteran brown tabby stud. He has incredible lineage and produces exceptional kittens."
    },
    
    # Kittens
    "kittens/Georgia-2.5-230325-5.jpeg": {
        "name": "Georgia",
        "age": "2 months 2 weeks",
        "color": "Blue Tortie",
        "description": "Georgia is a beautiful blue tortie kitten with amazing ear tufts. She's playful and affectionate."
    },
    "kittens/unknown-1.8-230325-2.jpg": {
        "name": "Unknown",
        "age": "1 month 3 weeks",
        "color": "Red Silver Tabby",
        "description": "This adorable red silver tabby kitten is still unnamed. He has a wonderful temperament and striking looks."
    }
}

def convert_png_to_jpg(png_path, jpg_path):
    """Convert PNG to JPG format since IPTC metadata requires JPEG format."""
    try:
        img = Image.open(png_path)
        # Convert to RGB mode if needed (in case of RGBA with transparency)
        if img.mode != 'RGB':
            img = img.convert('RGB')
        img.save(jpg_path, 'JPEG', quality=95)
        print(f"Converted {png_path} to {jpg_path}")
        return True
    except Exception as e:
        print(f"Error converting {png_path}: {e}")
        return False

def add_iptc_metadata(image_path, metadata):
    """Add IPTC metadata to an image file."""
    try:
        # Check if file exists
        if not os.path.exists(image_path):
            print(f"Warning: {image_path} does not exist. Skipping.")
            return False
        
        # Handle PNG files (IPTC requires JPEG format)
        is_png = image_path.lower().endswith('.png')
        temp_jpg_path = None
        
        if is_png:
            temp_jpg_path = image_path.rsplit('.', 1)[0] + '_temp.jpg'
            if not convert_png_to_jpg(image_path, temp_jpg_path):
                return False
            target_path = temp_jpg_path
        else:
            target_path = image_path
        
        # Add IPTC metadata
        info = IPTCInfo(target_path, force=True)
        
        # Set cat name in Person Shown field (IPTC tag 0x0228)
        if "name" in metadata:
            info['person shown'] = metadata["name"]
            
        # Set cat age in Model Age field (IPTC tag 0x0229)
        if "age" in metadata:
            info['model age'] = metadata["age"]
            
        # Set description in Caption/Abstract field
        if "description" in metadata:
            info['caption/abstract'] = metadata["description"]
            
        # Set keywords for searchability
        keywords = ["Maine Coon", "cat"]
        if "color" in metadata:
            keywords.append(metadata["color"])
        info['keywords'] = keywords
        
        # Save the changes
        info.save()
        print(f"Added IPTC metadata to {image_path}")
        
        # Clean up temporary file if needed
        if temp_jpg_path and os.path.exists(temp_jpg_path):
            os.remove(temp_jpg_path)
            
        return True
    except Exception as e:
        print(f"Error adding IPTC metadata to {image_path}: {e}")
        return False

def main():
    base_dir = "test-images"
    processed_count = 0
    failed_count = 0
    
    print("Adding IPTC metadata to test images...")
    
    for rel_path, metadata in CATS_METADATA.items():
        full_path = os.path.join(base_dir, rel_path)
        if add_iptc_metadata(full_path, metadata):
            processed_count += 1
        else:
            failed_count += 1
    
    print(f"Finished processing images. Successfully processed: {processed_count}, Failed: {failed_count}")

if __name__ == "__main__":
    main()