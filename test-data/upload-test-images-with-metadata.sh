#!/bin/bash
# <PERSON><PERSON><PERSON> to upload test images to MinIO S3 storage with proper metadata for testing

set -e

echo "Starting upload of test images to MinIO with metadata..."

# MinIO connection details
MINIO_ENDPOINT="http://minio:9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
BUCKET_NAME="yendor-test"

# Configure AWS CLI for MinIO
export AWS_ACCESS_KEY_ID=$MINIO_ACCESS_KEY
export AWS_SECRET_ACCESS_KEY=$MINIO_SECRET_KEY
export AWS_DEFAULT_REGION=us-east-1

# Wait for Min<PERSON> to be ready
echo "Waiting for Min<PERSON> to be ready..."
until curl -s -f -o /dev/null $MINIO_ENDPOINT/minio/health/live; do
  echo "MinIO not ready yet, waiting..."
  sleep 2
done

# Create bucket if it doesn't exist
if ! aws --endpoint-url=$MINIO_ENDPOINT s3 ls "s3://$BUCKET_NAME" &>/dev/null; then
  echo "Creating bucket: $BUCKET_NAME"
  aws --endpoint-url=$MINIO_ENDPOINT s3 mb "s3://$BUCKET_NAME"
  # Set bucket policy to allow public read access
  aws --endpoint-url=$MINIO_ENDPOINT s3api put-bucket-policy \
    --bucket "$BUCKET_NAME" \
    --policy "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetObject\"],\"Resource\":[\"arn:aws:s3:::$BUCKET_NAME/*\"]}]}"
fi

# Ensure directories exist
mkdir -p test-images/{queens,studs,kittens,gallery}

# Define metadata for queen images
declare -A queens_metadata=(
  ["Loretta-3-080824-1.jpg"]="Loretta|3 years|Blue Silver Tabby|Loretta is a beautiful blue silver tabby queen with excellent temperament."
  ["Rosie-3-030324-1.png"]="Rosie|3 years|Black Tortie|Rosie is our stunning black tortie queen. She has incredible eyes and a playful personality."
  ["Sera-1.11-030324-1.png"]="Sera|1 year 11 months|Black Silver Tabby|Sera is our gorgeous black silver tabby. She has a sweet temperament and loves attention."
)

# Define metadata for stud images
declare -A studs_metadata=(
  ["Dennis-10.3-030324-1.jpg"]="Dennis|10 years 3 months|Red Silver Tabby|Dennis is our magnificent red silver tabby stud. He has sired many beautiful kittens."
  ["Rapper-16.2-030324-1.png"]="Rapper|16 years 2 months|Brown Tabby|Rapper is our veteran brown tabby stud. He has incredible lineage and produces exceptional kittens."
)

# Define metadata for kitten images
declare -A kittens_metadata=(
  ["Georgia-2.5-230325-5.jpeg"]="Georgia|2 months 2 weeks|Blue Tortie|Georgia is a beautiful blue tortie kitten with amazing ear tufts. She's playful and affectionate."
  ["unknown-1.8-230325-2.jpg"]="Unknown|1 month 3 weeks|Red Silver Tabby|This adorable red silver tabby kitten is still unnamed. He has a wonderful temperament and striking looks."
)

# Function to upload file with metadata
upload_with_metadata() {
  local file="$1"
  local category="$2"
  local metadata_str="$3"
  
  # Parse metadata
  IFS='|' read -r name age color description <<< "$metadata_str"
  
  echo "Uploading $category image: $file"
  echo "  Name: $name"
  echo "  Age: $age"
  echo "  Color: $color"
  
  # Upload with metadata
  aws --endpoint-url=$MINIO_ENDPOINT s3api put-object \
    --bucket "$BUCKET_NAME" \
    --key "$category/$file" \
    --body "test-images/$category/$file" \
    --content-type "$(file --mime-type -b "test-images/$category/$file")" \
    --metadata "{\"x-amz-meta-cat-name\":\"$name\",\"x-amz-meta-cat-age\":\"$age\",\"x-amz-meta-cat-color\":\"$color\",\"x-amz-meta-cat-description\":\"$description\",\"x-amz-meta-cat-breed\":\"Maine Coon\"}" \
    --acl public-read
}

# Upload queen images with metadata
echo "Uploading queen images with metadata..."
for file in "${!queens_metadata[@]}"; do
  if [ -f "test-images/queens/$file" ]; then
    upload_with_metadata "$file" "queens" "${queens_metadata[$file]}"
  else
    echo "Warning: test-images/queens/$file does not exist. Skipping."
  fi
done

# Upload stud images with metadata
echo "Uploading stud images with metadata..."
for file in "${!studs_metadata[@]}"; do
  if [ -f "test-images/studs/$file" ]; then
    upload_with_metadata "$file" "studs" "${studs_metadata[$file]}"
  else
    echo "Warning: test-images/studs/$file does not exist. Skipping."
  fi
done

# Upload kitten images with metadata
echo "Uploading kitten images with metadata..."
for file in "${!kittens_metadata[@]}"; do
  if [ -f "test-images/kittens/$file" ]; then
    upload_with_metadata "$file" "kittens" "${kittens_metadata[$file]}"
  else
    echo "Warning: test-images/kittens/$file does not exist. Skipping."
  fi
done

# Upload gallery images (no specific cat metadata)
echo "Uploading gallery images..."
find test-images/gallery -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) -exec \
  aws --endpoint-url=$MINIO_ENDPOINT s3 cp {} "s3://$BUCKET_NAME/gallery/" --acl public-read \;

echo "All test images uploaded successfully to MinIO with proper metadata!"

# Verify metadata for a sample file
echo "Verifying metadata for a sample file..."
aws --endpoint-url=$MINIO_ENDPOINT s3api head-object \
  --bucket "$BUCKET_NAME" \
  --key "queens/Loretta-3-080824-1.jpg"

echo "Testing complete!"