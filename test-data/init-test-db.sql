-- YendorCats Test Database Initialization Script
-- This script creates and populates the SQLite database for testing

-- Users Table
CREATE TABLE IF NOT EXISTS Users (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    <PERSON>rname TEXT NOT NULL UNIQUE,
    Email TEXT NOT NULL UNIQUE,
    PasswordHash TEXT NOT NULL,
    Salt TEXT NOT NULL,
    Role TEXT NOT NULL DEFAULT 'User',
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    LastLogin TEXT,
    IsActive INTEGER NOT NULL DEFAULT 1
);

-- Cat Profiles Table
CREATE TABLE IF NOT EXISTS CatProfiles (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Type TEXT NOT NULL,  -- 'Queen', 'Stud', 'Kitten'
    Breed TEXT NOT NULL,
    Age TEXT,
    Color TEXT,
    Description TEXT,
    ImageUrl TEXT,
    Status TEXT NOT NULL,
    Featured INTEGER NOT NULL DEFAULT 0,
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    UpdatedAt TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Cat Relationships (for tracking parents)
CREATE TABLE IF NOT EXISTS CatRelationships (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    KittenId INTEGER NOT NULL,
    MotherId INTEGER,
    FatherId INTEGER,
    FOREIGN KEY (KittenId) REFERENCES CatProfiles(Id),
    FOREIGN KEY (MotherId) REFERENCES CatProfiles(Id),
    FOREIGN KEY (FatherId) REFERENCES CatProfiles(Id)
);

-- Photo Gallery Table
CREATE TABLE IF NOT EXISTS PhotoGallery (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Title TEXT,
    Description TEXT,
    ImageUrl TEXT NOT NULL,
    CatProfileId INTEGER,
    Featured INTEGER NOT NULL DEFAULT 0,
    UploadedAt TEXT NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY (CatProfileId) REFERENCES CatProfiles(Id)
);

-- Admin Users Table
CREATE TABLE IF NOT EXISTS AdminUsers (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL UNIQUE,
    Email TEXT NOT NULL UNIQUE,
    PasswordHash TEXT NOT NULL,
    Salt TEXT NOT NULL,
    Role TEXT NOT NULL DEFAULT 'Admin',
    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    LastLogin TEXT,
    IsActive INTEGER NOT NULL DEFAULT 1,
    RequirePasswordChange INTEGER NOT NULL DEFAULT 0
);

-- Insert Test Admin Users
INSERT INTO AdminUsers (Username, Email, PasswordHash, Salt, Role, RequirePasswordChange)
VALUES 
    ('testadmin', '<EMAIL>', 
     'hash_would_be_calculated_at_runtime', 'salt_would_be_calculated_at_runtime', 
     'Admin', 0),
    ('admin2', '<EMAIL>', 
     'hash_would_be_calculated_at_runtime', 'salt_would_be_calculated_at_runtime', 
     'Admin', 0);

-- Insert Test Cat Profiles - Queens
INSERT INTO CatProfiles (Name, Type, Breed, Age, Color, Description, ImageUrl, Status, Featured)
VALUES 
    ('Loretta', 'Queen', 'Maine Coon', '3 years', 'Blue Silver Tabby', 
     'Loretta is a beautiful blue silver tabby queen with excellent temperament. She produces lovely kittens with her signature sweet disposition.',
     'queens/Loretta-3-080824-1.jpg', 'Available', 1),
    ('Rosie', 'Queen', 'Maine Coon', '3 years', 'Black Tortie', 
     'Rosie is our stunning black tortie queen. She has incredible eyes and a playful personality.',
     'queens/Rosie-3-030324-1.png', 'Available', 1),
    ('Sera', 'Queen', 'Maine Coon', '1 year, 11 months', 'Black Silver Tabby', 
     'Sera is our gorgeous black silver tabby. She has a sweet temperament and loves attention.',
     'queens/Sera-1.11-030324-1.png', 'Reserved', 0);

-- Insert Test Cat Profiles - Studs
INSERT INTO CatProfiles (Name, Type, Breed, Age, Color, Description, ImageUrl, Status, Featured)
VALUES 
    ('Dennis', 'Stud', 'Maine Coon', '10 years, 3 months', 'Red Silver Tabby', 
     'Dennis is our magnificent red silver tabby stud. He has sired many beautiful kittens.',
     'studs/Dennis-10.3-030324-1.jpg', 'Available', 1),
    ('Rapper', 'Stud', 'Maine Coon', '16 years, 2 months', 'Brown Tabby', 
     'Rapper is our veteran brown tabby stud. He has incredible lineage and produces exceptional kittens.',
     'studs/Rapper-16.2-030324-1.png', 'Retired', 0);

-- Insert Test Cat Profiles - Kittens
INSERT INTO CatProfiles (Name, Type, Breed, Age, Color, Description, ImageUrl, Status, Featured)
VALUES 
    ('Georgia', 'Kitten', 'Maine Coon', '2 months, 2 weeks', 'Blue Tortie', 
     'Georgia is a beautiful blue tortie kitten with amazing ear tufts. She''s playful and affectionate.',
     'kittens/Georgia-2.5-230325-5.jpeg', 'Available', 1),
    ('Unknown', 'Kitten', 'Maine Coon', '1 month, 3 weeks', 'Red Silver Tabby', 
     'This adorable red silver tabby kitten is still unnamed. He has a wonderful temperament and striking looks.',
     'kittens/unknown-1.8-230325-2.jpg', 'Reserved', 0);

-- Set up kitten-parent relationships
INSERT INTO CatRelationships (KittenId, MotherId, FatherId)
VALUES 
    (6, 1, 4),  -- Georgia is child of Loretta and Dennis
    (7, 2, 4);  -- Unknown kitten is child of Rosie and Dennis

-- Insert Photo Gallery Images
INSERT INTO PhotoGallery (Title, Description, ImageUrl, Featured)
VALUES 
    ('Gallery Image 1', 'Beautiful Maine Coon cat', 'gallery/gallery-1.jpg', 1),
    ('Gallery Image 2', 'Maine Coon kitten playing', 'gallery/gallery-2.jpg', 1),
    ('Gallery Image 3', 'Adult Maine Coon', 'gallery/gallery-3.jpg', 1),
    ('Gallery Image 4', 'Maine Coon queen with kittens', 'gallery/gallery-4.jpg', 0),
    ('Gallery Image 5', 'Maine Coon stud', 'gallery/gallery-5.jpg', 0);

-- Link some gallery photos to cat profiles
UPDATE PhotoGallery SET CatProfileId = 1 WHERE Id = 4;  -- Link to Loretta
UPDATE PhotoGallery SET CatProfileId = 4 WHERE Id = 5;  -- Link to Dennis