FROM python:3.9-slim

WORKDIR /app

# Install dependencies
RUN pip install iptcinfo3 pillow

# Copy the metadata script
COPY add_iptc_metadata.py /app/

# Create directories for images
RUN mkdir -p /app/test-images/queens /app/test-images/studs /app/test-images/kittens /app/test-images/gallery

# Set the script as executable
RUN chmod +x /app/add_iptc_metadata.py

ENTRYPOINT ["python", "/app/add_iptc_metadata.py"]