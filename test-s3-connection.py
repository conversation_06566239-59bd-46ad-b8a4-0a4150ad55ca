#!/usr/bin/env python3
"""
Simple test script to verify Backblaze B2 connection using your credentials
"""

import requests
import json
import base64
import hashlib
import hmac
from datetime import datetime
import urllib.parse

# Your Backblaze B2 credentials (from the config file)
ACCESS_KEY = "004d0cd685eb5360000000001"
SECRET_KEY = "K004oHEJSNWiYGzxb084PXBd/7VycvE"
ENDPOINT = "https://s3.us-west-004.backblazeb2.com"
BUCKET_NAME = "yendorcats-images-dev"

def create_aws_signature(method, url, headers, payload=""):
    """Create AWS4 signature for Backblaze B2"""
    
    # Parse URL
    parsed_url = urllib.parse.urlparse(url)
    host = parsed_url.netloc
    path = parsed_url.path or "/"
    query = parsed_url.query
    
    # Create canonical request
    canonical_headers = f"host:{host}\n"
    signed_headers = "host"
    
    if query:
        canonical_query_string = "&".join(sorted(query.split("&")))
    else:
        canonical_query_string = ""
    
    canonical_request = f"{method}\n{path}\n{canonical_query_string}\n{canonical_headers}\n{signed_headers}\n{hashlib.sha256(payload.encode()).hexdigest()}"
    
    # Create string to sign
    timestamp = datetime.utcnow().strftime("%Y%m%dT%H%M%SZ")
    date = timestamp[:8]
    region = "us-west-004"
    service = "s3"
    
    credential_scope = f"{date}/{region}/{service}/aws4_request"
    string_to_sign = f"AWS4-HMAC-SHA256\n{timestamp}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}"
    
    # Calculate signature
    def sign(key, msg):
        return hmac.new(key, msg.encode(), hashlib.sha256).digest()
    
    signing_key = sign(f"AWS4{SECRET_KEY}".encode(), date)
    signing_key = sign(signing_key, region)
    signing_key = sign(signing_key, service)
    signing_key = sign(signing_key, "aws4_request")
    
    signature = hmac.new(signing_key, string_to_sign.encode(), hashlib.sha256).hexdigest()
    
    # Create authorization header
    authorization = f"AWS4-HMAC-SHA256 Credential={ACCESS_KEY}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"
    
    return {
        "Authorization": authorization,
        "X-Amz-Date": timestamp,
        "Host": host
    }

def test_connection():
    """Test the Backblaze B2 connection"""
    
    print("🧪 Testing Backblaze B2 Connection")
    print("=" * 50)
    print(f"Endpoint: {ENDPOINT}")
    print(f"Access Key: {ACCESS_KEY[:10]}...")
    print(f"Bucket: {BUCKET_NAME}")
    print()
    
    try:
        # Test 1: List buckets
        print("📋 Test 1: List buckets")
        url = ENDPOINT
        headers = create_aws_signature("GET", url)
        
        response = requests.get(url, headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ Successfully connected to Backblaze B2!")
        else:
            print(f"❌ Failed to connect. Status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False
    
    try:
        # Test 2: Check if bucket exists
        print("\n🪣 Test 2: Check bucket access")
        bucket_url = f"{ENDPOINT}/{BUCKET_NAME}"
        headers = create_aws_signature("HEAD", bucket_url)
        
        response = requests.head(bucket_url, headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ Bucket '{BUCKET_NAME}' is accessible!")
        elif response.status_code == 404:
            print(f"❌ Bucket '{BUCKET_NAME}' not found!")
            return False
        else:
            print(f"⚠️  Bucket check returned status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Bucket check failed: {e}")
        return False
    
    try:
        # Test 3: List objects in bucket
        print("\n📁 Test 3: List objects in bucket")
        list_url = f"{ENDPOINT}/{BUCKET_NAME}?list-type=2&max-keys=5"
        headers = create_aws_signature("GET", list_url)
        
        response = requests.get(list_url, headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Successfully listed bucket contents!")
            print(f"Response preview: {response.text[:300]}...")
        else:
            print(f"⚠️  List objects returned status: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ List objects failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Connection test completed!")
    return True

if __name__ == "__main__":
    test_connection()
