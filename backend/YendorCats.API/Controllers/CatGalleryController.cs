using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using YendorCats.API.Models;
using YendorCats.API.Services;
using YendorCats.API.Data;
using Microsoft.EntityFrameworkCore;

namespace YendorCats.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CatGalleryController : ControllerBase
    {
        private readonly ILogger<CatGalleryController> _logger;
        private readonly IS3StorageService _s3StorageService;
        private readonly AppDbContext _context;
        private readonly string[] _validCategories = { "studs", "queens", "kittens", "gallery" };

        public CatGalleryController(
            ILogger<CatGalleryController> logger,
            IS3StorageService s3StorageService,
            AppDbContext context)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
            _context = context;
        }

        [HttpGet("category/{category}")]
        public async Task<ActionResult<IEnumerable<CatGalleryImage>>> GetCategoryImages(
            string category,
            [FromQuery] string orderBy = "date",
            [FromQuery] bool descending = true)
        {
            if (!_validCategories.Contains(category.ToLower()))
            {
                return BadRequest($"Invalid category. Valid categories are: {string.Join(", ", _validCategories)}");
            }

            try
            {
                var catImages = await ScanS3ForImagesAsync(category);

                IOrderedEnumerable<CatGalleryImage> orderedImages;
                switch (orderBy.ToLower())
                {
                    case "name":
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.CatName)
                            : catImages.OrderBy(img => img.CatName);
                        break;
                    case "age":
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.Age)
                            : catImages.OrderBy(img => img.Age);
                        break;
                    default:
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.DateTaken).ThenBy(img => img.OrderNumber)
                            : catImages.OrderBy(img => img.DateTaken).ThenBy(img => img.OrderNumber);
                        break;
                }

                return orderedImages.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat gallery images for category {Category}", category);
                return StatusCode(500, "An error occurred while retrieving cat gallery images");
            }
        }

        private async Task<List<CatGalleryImage>> ScanS3ForImagesAsync(string category)
        {
            var result = new List<CatGalleryImage>();

            // First, try to get images from database (uploaded with metadata)
            var dbImages = await _context.CatGalleryImages
                .Where(img => img.Category.ToLower() == category.ToLower())
                .ToListAsync();

            if (dbImages.Any())
            {
                _logger.LogInformation("Found {Count} images in database for category {Category}", dbImages.Count, category);
                result.AddRange(dbImages);
            }

            // Also scan S3 for any images not in database (legacy or direct uploads)
            var prefix = $"{category}/";
            var s3Files = await _s3StorageService.ListFilesAsync(prefix);
            var dbImageUrls = dbImages.Select(img => img.ImageUrl).ToHashSet();

            foreach (var s3Object in s3Files)
            {
                var imageUrl = $"https://f004.backblazeb2.com/file/yendor/{s3Object.Key}";

                // Skip if already in database
                if (dbImageUrls.Contains(imageUrl))
                    continue;

                var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Object.Key);
                var catImage = CatGalleryImage.FromS3Metadata(metadata, imageUrl, category, null);

                // Try to extract cat name from folder structure as fallback
                if (string.IsNullOrEmpty(catImage.CatName))
                {
                    catImage.CatName = ExtractCatNameFromPath(s3Object.Key);
                }

                result.Add(catImage);
            }

            _logger.LogInformation("Total images found for category {Category}: {Count} (DB: {DbCount}, S3: {S3Count})",
                category, result.Count, dbImages.Count, result.Count - dbImages.Count);

            return result;
        }

        private string ExtractCatNameFromPath(string s3Key)
        {
            try
            {
                // S3 key format: category/catname/filename.jpg
                var parts = s3Key.Split('/');
                if (parts.Length >= 3)
                {
                    var catName = parts[1];
                    if (!string.IsNullOrEmpty(catName) && catName != ".bzEmpty")
                    {
                        // Capitalize first letter
                        return char.ToUpper(catName[0]) + catName.Substring(1);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract cat name from S3 key: {Key}", s3Key);
            }

            return "Maine Coon Cat";
        }
    }
}
