using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using YendorCats.API.Models;
using YendorCats.API.Services;
using System.Text.Json;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for managing cat gallery images with metadata extracted from S3 metadata or filenames
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class CatGalleryController : ControllerBase
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly ILogger<CatGalleryController> _logger;
        private readonly IS3StorageService _s3StorageService;
        private readonly string[] _validExtensions = { ".jpg", ".jpeg", ".png", ".gif" };
        private readonly string[] _validCategories = { "studs", "queens", "kittens", "gallery" };

        /// <summary>
        /// Constructor for CatGalleryController
        /// </summary>
        public CatGalleryController(
            IWebHostEnvironment webHostEnvironment,
            ILogger<CatGalleryController> logger,
            IS3StorageService s3StorageService)
        {
            _webHostEnvironment = webHostEnvironment;
            _logger = logger;
            _s3StorageService = s3StorageService;
        }

        /// <summary>
        /// Gets all cat gallery images from all categories or a specific category
        /// </summary>
        /// <param name="category">Optional category filter (studs, queens, kittens, gallery)</param>
        /// <returns>A list of cat gallery images with metadata</returns>
        [HttpGet]
        public ActionResult<IEnumerable<CatGalleryImage>> GetCatImages([FromQuery] string? category = null)
        {
            try
            {
                var result = new List<CatGalleryImage>();
                var categoriesToScan = string.IsNullOrEmpty(category)
                    ? _validCategories
                    : new[] { category };

                foreach (var cat in categoriesToScan)
                {
                    if (!_validCategories.Contains(cat))
                    {
                        continue; // Skip invalid categories
                    }

                    var catImages = ScanDirectoryForImages(cat);
                    result.AddRange(catImages);
                }

                // Sort by date taken (newest first), then by order number
                return result
                    .OrderByDescending(img => img.DateTaken)
                    .ThenBy(img => img.OrderNumber)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat gallery images");
                return StatusCode(500, "An error occurred while retrieving cat gallery images");
            }
        }

        /// <summary>
        /// Gets cat gallery images for a specific category, ordered by the specified criteria
        /// </summary>
        /// <param name="category">Category to scan (studs, queens, kittens, gallery)</param>
        /// <param name="orderBy">Order by field (date, name, age)</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>A list of sorted cat gallery images</returns>
        [HttpGet("category/{category}")]
        public ActionResult<IEnumerable<CatGalleryImage>> GetCategoryImages(
            string category,
            [FromQuery] string orderBy = "date",
            [FromQuery] bool descending = true)
        {
            if (!_validCategories.Contains(category))
            {
                return BadRequest($"Invalid category. Valid categories are: {string.Join(", ", _validCategories)}");
            }

            try
            {
                var catImages = ScanDirectoryForImages(category);

                // Apply ordering based on the orderBy parameter
                IOrderedEnumerable<CatGalleryImage> orderedImages;

                switch (orderBy.ToLower())
                {
                    case "name":
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.CatName)
                            : catImages.OrderBy(img => img.CatName);
                        break;
                    case "age":
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.Age)
                            : catImages.OrderBy(img => img.Age);
                        break;
                    case "date":
                    default:
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.DateTaken).ThenBy(img => img.OrderNumber)
                            : catImages.OrderBy(img => img.DateTaken).ThenBy(img => img.OrderNumber);
                        break;
                }

                return orderedImages.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat gallery images for category {Category}", category);
                return StatusCode(500, "An error occurred while retrieving cat gallery images");
            }
        }

        /// <summary>
        /// Gets detailed metadata for a specific image
        /// </summary>
        /// <param name="category">The category of the image</param>
        /// <param name="fileName">The file name of the image</param>
        /// <returns>Detailed metadata for the image</returns>
        [HttpGet("metadata/{category}/{fileName}")]
        public async Task<ActionResult<CatImageMetadata>> GetImageMetadata(string category, string fileName)
        {
            if (!_validCategories.Contains(category))
            {
                return BadRequest($"Invalid category. Valid categories are: {string.Join(", ", _validCategories)}");
            }

            try
            {
                // Construct the S3 key
                var s3Key = $"resources/{category}/{fileName}";

                // Get metadata from S3
                var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Key);

                if (metadata.Count == 0)
                {
                    return NotFound($"No metadata found for image {fileName} in category {category}");
                }

                // Convert to CatImageMetadata
                var catImageMetadata = CatImageMetadata.FromS3Metadata(metadata);

                return Ok(catImageMetadata);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving metadata for image {FileName} in category {Category}", fileName, category);
                return StatusCode(500, "An error occurred while retrieving image metadata");
            }
        }

        /// <summary>
        /// Search for cat images based on metadata criteria
        /// </summary>
        /// <param name="category">Optional category filter</param>
        /// <param name="gender">Optional gender filter (M/F)</param>
        /// <param name="breed">Optional breed filter</param>
        /// <param name="bloodline">Optional bloodline filter</param>
        /// <param name="hairColor">Optional hair color filter</param>
        /// <param name="tags">Optional tags filter (comma-separated)</param>
        /// <param name="minAge">Optional minimum age filter</param>
        /// <param name="maxAge">Optional maximum age filter</param>
        /// <param name="orderBy">Field to order by (date, name, age)</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>A filtered list of cat gallery images</returns>
        [HttpGet("search")]
        public ActionResult<IEnumerable<CatGalleryImage>> SearchImages(
            [FromQuery] string? category = null,
            [FromQuery] string? gender = null,
            [FromQuery] string? breed = null,
            [FromQuery] string? bloodline = null,
            [FromQuery] string? hairColor = null,
            [FromQuery] string? tags = null,
            [FromQuery] float? minAge = null,
            [FromQuery] float? maxAge = null,
            [FromQuery] string orderBy = "date",
            [FromQuery] bool descending = true)
        {
            try
            {
                // Get all images or filter by category
                var allImages = new List<CatGalleryImage>();
                var categoriesToScan = string.IsNullOrEmpty(category)
                    ? _validCategories
                    : new[] { category };

                foreach (var cat in categoriesToScan)
                {
                    if (!_validCategories.Contains(cat))
                    {
                        continue; // Skip invalid categories
                    }

                    allImages.AddRange(ScanDirectoryForImages(cat));
                }

                // Apply filters
                var filteredImages = allImages.AsEnumerable();

                // Filter by gender
                if (!string.IsNullOrEmpty(gender))
                {
                    filteredImages = filteredImages.Where(img =>
                        !string.IsNullOrEmpty(img.Gender) &&
                        img.Gender.Equals(gender, StringComparison.OrdinalIgnoreCase));
                }

                // Filter by breed
                if (!string.IsNullOrEmpty(breed))
                {
                    filteredImages = filteredImages.Where(img =>
                        !string.IsNullOrEmpty(img.Breed) &&
                        img.Breed.Equals(breed, StringComparison.OrdinalIgnoreCase));
                }

                // Filter by bloodline
                if (!string.IsNullOrEmpty(bloodline))
                {
                    filteredImages = filteredImages.Where(img =>
                        !string.IsNullOrEmpty(img.Bloodline) &&
                        img.Bloodline.Equals(bloodline, StringComparison.OrdinalIgnoreCase));
                }

                // Filter by hair color
                if (!string.IsNullOrEmpty(hairColor))
                {
                    filteredImages = filteredImages.Where(img =>
                        !string.IsNullOrEmpty(img.Color) &&
                        img.Color.Contains(hairColor, StringComparison.OrdinalIgnoreCase));
                }

                // Filter by tags
                if (!string.IsNullOrEmpty(tags))
                {
                    var tagList = tags.Split(',').Select(t => t.Trim().ToLowerInvariant()).ToList();
                    filteredImages = filteredImages.Where(img =>
                        !string.IsNullOrEmpty(img.Tags) &&
                        tagList.Any(tag => img.Tags.ToLowerInvariant().Contains(tag)));
                }

                // Filter by age range
                if (minAge.HasValue)
                {
                    filteredImages = filteredImages.Where(img => img.Age >= minAge.Value);
                }

                if (maxAge.HasValue)
                {
                    filteredImages = filteredImages.Where(img => img.Age <= maxAge.Value);
                }

                // Apply ordering
                IOrderedEnumerable<CatGalleryImage> orderedImages;
                switch (orderBy.ToLower())
                {
                    case "name":
                        orderedImages = descending
                            ? filteredImages.OrderByDescending(img => img.CatName)
                            : filteredImages.OrderBy(img => img.CatName);
                        break;
                    case "age":
                        orderedImages = descending
                            ? filteredImages.OrderByDescending(img => img.Age)
                            : filteredImages.OrderBy(img => img.Age);
                        break;
                    case "date":
                    default:
                        orderedImages = descending
                            ? filteredImages.OrderByDescending(img => img.DateTaken).ThenBy(img => img.OrderNumber)
                            : filteredImages.OrderBy(img => img.DateTaken).ThenBy(img => img.OrderNumber);
                        break;
                }

                return orderedImages.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching cat gallery images");
                return StatusCode(500, "An error occurred while searching cat gallery images");
            }
        }

        /// <summary>
        /// Gets all available metadata fields for filtering
        /// </summary>
        /// <returns>A list of metadata fields that can be used for filtering</returns>
        [HttpGet("metadata/fields")]
        public ActionResult<Dictionary<string, List<string>>> GetMetadataFields()
        {
            try
            {
                // Create a dictionary of metadata fields and their possible values
                var metadataFields = new Dictionary<string, List<string>>
                {
                    { "category", _validCategories.ToList() },
                    { "gender", new List<string> { "M", "F" } }
                };

                // Scan all images to collect unique values for other fields
                var allImages = new List<CatGalleryImage>();
                foreach (var category in _validCategories)
                {
                    allImages.AddRange(ScanDirectoryForImages(category));
                }

                // Extract unique values for various fields
                if (allImages.Any())
                {
                    // Collect breed values
                    var breeds = allImages
                        .Where(img => !string.IsNullOrEmpty(img.Breed))
                        .Select(img => img.Breed)
                        .Distinct()
                        .OrderBy(b => b)
                        .ToList();

                    if (breeds.Any())
                    {
                        metadataFields["breed"] = breeds!;
                    }

                    // Collect bloodline values
                    var bloodlines = allImages
                        .Where(img => !string.IsNullOrEmpty(img.Bloodline))
                        .Select(img => img.Bloodline)
                        .Distinct()
                        .OrderBy(b => b)
                        .ToList();

                    if (bloodlines.Any())
                    {
                        metadataFields["bloodline"] = bloodlines!;
                    }

                    // Collect hair color values
                    var hairColors = allImages
                        .Where(img => !string.IsNullOrEmpty(img.Color))
                        .Select(img => img.Color)
                        .Distinct()
                        .OrderBy(c => c)
                        .ToList();

                    if (hairColors.Any())
                    {
                        metadataFields["hair_color"] = hairColors!;
                    }

                    // Collect all tags
                    var allTags = new HashSet<string>();
                    foreach (var img in allImages.Where(img => !string.IsNullOrEmpty(img.Tags)))
                    {
                        var tags = img.Tags?.Split(',').Select(t => t.Trim()).Where(t => !string.IsNullOrEmpty(t));
                        if (tags != null)
                        {
                            foreach (var tag in tags)
                            {
                                allTags.Add(tag);
                            }
                        }
                    }

                    if (allTags.Any())
                    {
                        metadataFields["tags"] = allTags.OrderBy(t => t).ToList();
                    }
                }

                return Ok(metadataFields);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving metadata fields");
                return StatusCode(500, "An error occurred while retrieving metadata fields");
            }
        }

        /// <summary>
        /// Scan a directory for cat gallery images and extract metadata from S3 or filenames
        /// </summary>
        private async Task<List<CatGalleryImage>> ScanDirectoryForImagesAsync(string category)
        {
            var result = new List<CatGalleryImage>();
            var imagesPath = Path.Combine(_webHostEnvironment.WebRootPath, "resources", category);

            _logger.LogInformation("Scanning directory: {ImagesPath}", imagesPath);

            if (!Directory.Exists(imagesPath))
            {
                _logger.LogWarning("Directory not found: {ImagesPath}", imagesPath);
                return result; // Return empty list if directory doesn't exist
            }

            var files = Directory.GetFiles(imagesPath)
                .Where(file => _validExtensions.Contains(Path.GetExtension(file).ToLower()))
                .ToList();

            _logger.LogInformation("Found {FileCount} files with valid extensions in {Category}", files.Count, category);

            foreach (var file in files)
            {
                var fileName = Path.GetFileName(file);
                var s3Key = $"resources/{category}/{fileName}";
                var relativePath = $"/{s3Key}";

                _logger.LogDebug("Processing file: {FileName}", fileName);

                try
                {
                    // First try to get metadata from S3
                    var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Key);

                    if (metadata.Count > 0)
                    {
                        _logger.LogDebug("Found S3 metadata for file: {FileName}, MetadataCount: {MetadataCount}",
                            fileName, metadata.Count);

                        // Try to parse from filename as a fallback for missing fields
                        var fallbackFromFilename = CatGalleryImage.FromFilename(file, relativePath, category);

                        // Create image from S3 metadata with filename parsing as fallback
                        var catImage = CatGalleryImage.FromS3Metadata(metadata, relativePath, category, fallbackFromFilename);
                        result.Add(catImage);

                        _logger.LogInformation("Successfully created image from S3 metadata: {FileName}", fileName);
                    }
                    else
                    {
                        // No S3 metadata found, try to parse from filename
                        var catImage = CatGalleryImage.FromFilename(file, relativePath, category);

                        if (catImage != null)
                        {
                            _logger.LogInformation("Successfully parsed metadata from filename: {FileName}", fileName);
                            result.Add(catImage);
                        }
                        else
                        {
                            // Fallback for files that don't match the expected format
                            _logger.LogWarning("Using fallback for file: {FileName}", fileName);
                            var fallbackImage = CreateFallbackImage(file, relativePath, category);
                            result.Add(fallbackImage);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing file: {FileName}", fileName);

                    // Try to parse from filename as a fallback
                    var catImage = CatGalleryImage.FromFilename(file, relativePath, category);

                    if (catImage != null)
                    {
                        _logger.LogInformation("Successfully parsed metadata from filename after S3 error: {FileName}", fileName);
                        result.Add(catImage);
                    }
                    else
                    {
                        // Last resort fallback
                        _logger.LogWarning("Using fallback after error for file: {FileName}", fileName);
                        var fallbackImage = CreateFallbackImage(file, relativePath, category);
                        result.Add(fallbackImage);
                    }
                }
            }

            _logger.LogInformation("Returning {ImageCount} images for category: {Category}", result.Count, category);
            return result;
        }

        /// <summary>
        /// Scan a directory for cat gallery images and extract metadata from S3 or filenames
        /// </summary>
        private List<CatGalleryImage> ScanDirectoryForImages(string category)
        {
            // Call the async method and wait for the result
            return ScanDirectoryForImagesAsync(category).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Create a fallback image entry for files that don't match the naming convention
        /// </summary>
        private CatGalleryImage CreateFallbackImage(string filePath, string relativePath, string category)
        {
            // Extract a simple name from the filename
            string filename = Path.GetFileNameWithoutExtension(filePath);

            // Try to find a name part (before first dash or use whole filename)
            string catName = filename.Contains('-') ?
                filename.Substring(0, filename.IndexOf('-')) :
                filename;

            // Capitalize the first letter
            catName = char.ToUpper(catName[0]) + catName.Substring(1);

            return new CatGalleryImage
            {
                Id = Guid.NewGuid().ToString(),
                CatName = catName,
                Age = 0, // Default age
                DateTaken = System.IO.File.GetCreationTime(filePath), // Use file creation time
                OrderNumber = 0, // Default order
                ImageUrl = relativePath,
                Category = category
            };
        }
    }
}