using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for user management operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<UsersController> _logger;

        /// <summary>
        /// Constructor for the UsersController
        /// </summary>
        /// <param name="userService">The user service for user operations</param>
        /// <param name="logger">Logger for controller operations</param>
        public UsersController(IUserService userService, ILogger<UsersController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// Get all users (Admin only)
        /// </summary>
        /// <returns>List of all users</returns>
        [HttpGet]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllUsers()
        {
            _logger.LogInformation("Getting all users");
            var users = await _userService.GetAllUsersAsync();
            return Ok(users);
        }

        /// <summary>
        /// Get a user by ID (Admin only)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User details</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetUser(int id)
        {
            _logger.LogInformation("Getting user by ID: {UserId}", id);
            var user = await _userService.GetUserByIdAsync(id);
            
            if (user == null)
            {
                _logger.LogWarning("User with ID {UserId} not found", id);
                return NotFound();
            }
            
            return Ok(user);
        }

        /// <summary>
        /// Get the current logged-in user
        /// </summary>
        /// <returns>Current user details</returns>
        [HttpGet("me")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetCurrentUser()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId) || !int.TryParse(userId, out int id))
            {
                _logger.LogWarning("Failed to get current user - invalid or missing user ID in claims");
                return Unauthorized();
            }

            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                _logger.LogWarning("Current user with ID {UserId} not found in database", id);
                return NotFound();
            }

            return Ok(user);
        }

        /// <summary>
        /// Create a new user (Admin only)
        /// </summary>
        /// <param name="user">User data</param>
        /// <param name="password">Password for the new user</param>
        /// <returns>Created user</returns>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreateUser([FromBody] User user, [FromQuery] string password)
        {
            if (string.IsNullOrEmpty(password))
            {
                _logger.LogWarning("Attempt to create user without password");
                return BadRequest("Password is required");
            }

            try
            {
                _logger.LogInformation("Creating new user with username: {Username}", user.Username);
                var createdUser = await _userService.AddUserAsync(user, password);
                return CreatedAtAction(nameof(GetUser), new { id = createdUser.Id }, createdUser);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("Failed to create user: {ErrorMessage}", ex.Message);
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Update an existing user (Admin or self)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="user">Updated user data</param>
        /// <returns>No content if successful</returns>
        [HttpPut("{id}")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateUser(int id, [FromBody] User user)
        {
            // Ensure ID in route matches body
            if (id != user.Id)
            {
                _logger.LogWarning("User ID mismatch in update request");
                return BadRequest("User ID mismatch");
            }

            // Check authorization - only admins can update other users
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var isAdmin = User.IsInRole("Admin");
            
            if (!isAdmin && currentUserId != id.ToString())
            {
                _logger.LogWarning("User {CurrentUserId} attempted to update user {TargetUserId}", currentUserId, id);
                return Forbid();
            }

            try
            {
                _logger.LogInformation("Updating user with ID: {UserId}", id);
                await _userService.UpdateUserAsync(user);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("User not found: {ErrorMessage}", ex.Message);
                return NotFound(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("Invalid operation: {ErrorMessage}", ex.Message);
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Delete a user (Admin only)
        /// </summary>
        /// <param name="id">User ID to delete</param>
        /// <returns>No content if successful</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteUser(int id)
        {
            try
            {
                _logger.LogInformation("Deleting user with ID: {UserId}", id);
                await _userService.DeleteUserAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("User not found: {ErrorMessage}", ex.Message);
                return NotFound(ex.Message);
            }
        }

        /// <summary>
        /// Change user password (Admin or self)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="currentPassword">Current password</param>
        /// <param name="newPassword">New password</param>
        /// <returns>Ok if successful, BadRequest if password is incorrect</returns>
        [HttpPost("{id}/change-password")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ChangePassword(
            int id, 
            [FromQuery] string currentPassword, 
            [FromQuery] string newPassword)
        {
            if (string.IsNullOrEmpty(currentPassword) || string.IsNullOrEmpty(newPassword))
            {
                return BadRequest("Current password and new password are required");
            }

            // Check authorization - only admins can update other users
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var isAdmin = User.IsInRole("Admin");
            
            if (!isAdmin && currentUserId != id.ToString())
            {
                _logger.LogWarning("User {CurrentUserId} attempted to change password for user {TargetUserId}", currentUserId, id);
                return Forbid();
            }

            try
            {
                _logger.LogInformation("Changing password for user with ID: {UserId}", id);
                var result = await _userService.ChangePasswordAsync(id, currentPassword, newPassword);
                
                if (result)
                {
                    return Ok("Password changed successfully");
                }
                else
                {
                    _logger.LogWarning("Failed to change password for user {UserId} - incorrect current password", id);
                    return BadRequest("Current password is incorrect");
                }
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("User not found: {ErrorMessage}", ex.Message);
                return NotFound(ex.Message);
            }
        }

        /// <summary>
        /// Check if a username is available
        /// </summary>
        /// <param name="username">Username to check</param>
        /// <returns>True if available, false if taken</returns>
        [HttpGet("check-username/{username}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckUsernameAvailability(string username)
        {
            _logger.LogInformation("Checking availability of username: {Username}", username);
            var isAvailable = await _userService.IsUsernameAvailableAsync(username);
            return Ok(new { isAvailable });
        }

        /// <summary>
        /// Check if an email is available
        /// </summary>
        /// <param name="email">Email to check</param>
        /// <returns>True if available, false if taken</returns>
        [HttpGet("check-email/{email}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckEmailAvailability(string email)
        {
            _logger.LogInformation("Checking availability of email: {Email}", email);
            var isAvailable = await _userService.IsEmailAvailableAsync(email);
            return Ok(new { isAvailable });
        }
    }
} 