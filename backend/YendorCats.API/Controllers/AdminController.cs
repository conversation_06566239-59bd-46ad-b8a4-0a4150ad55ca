using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for administrative operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class AdminController : ControllerBase
    {
        private readonly ILogger<AdminController> _logger;
        private readonly IS3StorageService _s3StorageService;

        /// <summary>
        /// Constructor for the admin controller
        /// </summary>
        /// <param name="logger">Logger for logging operations</param>
        /// <param name="s3StorageService">S3 storage service</param>
        public AdminController(
            ILogger<AdminController> logger,
            IS3StorageService s3StorageService)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
        }

        /// <summary>
        /// Configure CORS for the S3 bucket
        /// </summary>
        /// <returns>Result of the operation</returns>
        [HttpPost("s3/configure-cors")]
        public async Task<IActionResult> ConfigureS3Cors()
        {
            _logger.LogInformation("Configuring CORS for S3 bucket");
            
            try
            {
                await _s3StorageService.ConfigureCorsAsync();
                return Ok(new { message = "S3 CORS configuration successful" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error configuring CORS for S3 bucket");
                return StatusCode(500, new { message = "Error configuring CORS for S3 bucket", error = ex.Message });
            }
        }
    }
}
