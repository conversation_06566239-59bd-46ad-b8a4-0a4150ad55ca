using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Admin authentication controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AdminAuthController : ControllerBase
    {
        private readonly IAdminAuthService _adminAuthService;
        private readonly ILogger<AdminAuthController> _logger;

        public AdminAuthController(
            IAdminAuthService adminAuthService,
            ILogger<AdminAuthController> logger)
        {
            _adminAuthService = adminAuthService;
            _logger = logger;
        }

        /// <summary>
        /// Admin login endpoint
        /// </summary>
        /// <param name="request">Login credentials</param>
        /// <returns>Login response with JWT token</returns>
        [HttpPost("login")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Login([FromBody] AdminLoginRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var response = await _adminAuthService.AuthenticateAsync(request.Username, request.Password);

                if (!response.Success)
                {
                    return Unauthorized(new { message = response.Message });
                }

                // Set HTTP-only cookie for additional security
                var cookieOptions = new CookieOptions
                {
                    HttpOnly = true,
                    Secure = Request.IsHttps,
                    SameSite = SameSiteMode.Strict,
                    Expires = response.ExpiresAt
                };

                Response.Cookies.Append("admin_token", response.Token ?? "", cookieOptions);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during admin login");
                return StatusCode(500, new { message = "An error occurred during login" });
            }
        }

        /// <summary>
        /// Admin logout endpoint
        /// </summary>
        /// <returns>Success response</returns>
        [HttpPost("logout")]
        [Authorize(Roles = "SuperAdmin,Admin,Editor")]
        public IActionResult Logout()
        {
            // Clear the authentication cookie
            Response.Cookies.Delete("admin_token");

            return Ok(new { message = "Logged out successfully" });
        }

        /// <summary>
        /// Get current admin user info
        /// </summary>
        /// <returns>Current admin user information</returns>
        [HttpGet("me")]
        [Authorize(Roles = "SuperAdmin,Admin,Editor")]
        public async Task<IActionResult> GetCurrentUser()
        {
            try
            {
                var token = Request.Cookies["admin_token"] ?? 
                           Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                if (string.IsNullOrEmpty(token))
                {
                    return Unauthorized(new { message = "No token provided" });
                }

                var admin = await _adminAuthService.ValidateTokenAsync(token);

                if (admin == null)
                {
                    return Unauthorized(new { message = "Invalid token" });
                }

                return Ok(new AdminUserInfo
                {
                    Id = admin.Id,
                    Username = admin.Username,
                    Email = admin.Email,
                    Role = admin.Role,
                    LastLoginAt = admin.LastLoginAt
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current admin user");
                return StatusCode(500, new { message = "An error occurred" });
            }
        }

        /// <summary>
        /// Create new admin user (SuperAdmin only)
        /// </summary>
        /// <param name="request">Admin creation request</param>
        /// <returns>Created admin user info</returns>
        [HttpPost("create")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> CreateAdmin([FromBody] CreateAdminRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var admin = await _adminAuthService.CreateAdminAsync(
                    request.Username, 
                    request.Email, 
                    request.Password, 
                    request.Role);

                return Ok(new AdminUserInfo
                {
                    Id = admin.Id,
                    Username = admin.Username,
                    Email = admin.Email,
                    Role = admin.Role
                });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating admin user");
                return StatusCode(500, new { message = "An error occurred while creating admin user" });
            }
        }

        /// <summary>
        /// Initialize default admin (development only)
        /// </summary>
        /// <returns>Success response</returns>
        [HttpPost("init")]
        public async Task<IActionResult> InitializeDefaultAdmin()
        {
            try
            {
                // Only allow in development environment
                if (!HttpContext.RequestServices.GetRequiredService<IWebHostEnvironment>().IsDevelopment())
                {
                    return NotFound();
                }

                var created = await _adminAuthService.InitializeDefaultAdminAsync();

                if (created)
                {
                    return Ok(new { message = "Default admin user created successfully" });
                }
                else
                {
                    return Ok(new { message = "Admin users already exist" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing default admin");
                return StatusCode(500, new { message = "An error occurred" });
            }
        }
    }

    /// <summary>
    /// Create admin request model
    /// </summary>
    public class CreateAdminRequest
    {
        /// <summary>
        /// Username for new admin
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Email for new admin
        /// </summary>
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Password for new admin
        /// </summary>
        [Required]
        [StringLength(100, MinimumLength = 8)]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Role for new admin
        /// </summary>
        [Required]
        public string Role { get; set; } = AdminRoles.Admin;
    }
}
