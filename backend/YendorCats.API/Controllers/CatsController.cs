using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for cat-related operations
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CatsController : ControllerBase
    {
        private readonly ICatService _catService;
        private readonly ILogger<CatsController> _logger;

        /// <summary>
        /// Constructor for the cats controller
        /// </summary>
        /// <param name="catService">Service for cat operations</param>
        /// <param name="logger">Logger for the controller</param>
        public CatsController(ICatService catService, ILogger<CatsController> logger)
        {
            _catService = catService;
            _logger = logger;
        }

        /// <summary>
        /// Get all cats
        /// </summary>
        /// <param name="includeImages">Whether to include images in the response</param>
        /// <param name="includeInactive">Whether to include inactive cats</param>
        /// <returns>Collection of all cats</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Cat>>> GetAllCats(
            [FromQuery] bool includeImages = false,
            [FromQuery] bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Getting all cats. IncludeImages: {IncludeImages}, IncludeInactive: {IncludeInactive}", 
                    includeImages, includeInactive);
                var cats = await _catService.GetAllCatsAsync(includeImages, includeInactive);
                return Ok(cats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all cats");
                return StatusCode(StatusCodes.Status500InternalServerError, "Error retrieving data from the database");
            }
        }

        /// <summary>
        /// Get a cat by ID
        /// </summary>
        /// <param name="id">ID of the cat to retrieve</param>
        /// <param name="includeImages">Whether to include images in the response</param>
        /// <returns>Cat with the specified ID</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Cat>> GetCatById(int id, [FromQuery] bool includeImages = true)
        {
            try
            {
                _logger.LogInformation("Getting cat with ID: {CatId}. IncludeImages: {IncludeImages}", id, includeImages);
                var cat = await _catService.GetCatByIdAsync(id, includeImages);
                
                if (cat == null)
                {
                    _logger.LogWarning("Cat with ID {CatId} not found", id);
                    return NotFound();
                }
                
                return Ok(cat);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cat with ID: {CatId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "Error retrieving data from the database");
            }
        }

        /// <summary>
        /// Add a new cat
        /// </summary>
        /// <param name="cat">Cat to add</param>
        /// <returns>Newly created cat</returns>
        [HttpPost]
        [Authorize(Roles = "Admin,Editor")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<Cat>> AddCat(Cat cat)
        {
            try
            {
                if (cat == null)
                {
                    return BadRequest("Cat data cannot be null");
                }
                
                _logger.LogInformation("Adding new cat: {CatName}", cat.Name);
                var addedCat = await _catService.AddCatAsync(cat);
                
                return CreatedAtAction(nameof(GetCatById), new { id = addedCat.Id }, addedCat);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding cat: {CatName}", cat?.Name);
                return StatusCode(StatusCodes.Status500InternalServerError, "Error adding cat to the database");
            }
        }

        /// <summary>
        /// Update an existing cat
        /// </summary>
        /// <param name="id">ID of the cat to update</param>
        /// <param name="cat">Updated cat data</param>
        /// <returns>No content if successful</returns>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Editor")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateCat(int id, Cat cat)
        {
            try
            {
                if (cat == null || id != cat.Id)
                {
                    return BadRequest("Invalid cat data or ID mismatch");
                }
                
                _logger.LogInformation("Updating cat with ID: {CatId}", id);
                
                try
                {
                    await _catService.UpdateCatAsync(cat);
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning("Cat with ID {CatId} not found for update", id);
                    return NotFound();
                }
                
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating cat with ID: {CatId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "Error updating cat in the database");
            }
        }

        /// <summary>
        /// Delete a cat
        /// </summary>
        /// <param name="id">ID of the cat to delete</param>
        /// <returns>No content if successful</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteCat(int id)
        {
            try
            {
                _logger.LogInformation("Deleting cat with ID: {CatId}", id);
                
                try
                {
                    await _catService.DeleteCatAsync(id);
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning("Cat with ID {CatId} not found for deletion", id);
                    return NotFound();
                }
                
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting cat with ID: {CatId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "Error deleting cat from the database");
            }
        }

        /// <summary>
        /// Get cats by availability status
        /// </summary>
        /// <param name="available">Availability status to filter by</param>
        /// <param name="includeImages">Whether to include images in the response</param>
        /// <returns>Filtered collection of cats</returns>
        [HttpGet("available/{available}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Cat>>> GetCatsByAvailability(bool available, [FromQuery] bool includeImages = false)
        {
            try
            {
                _logger.LogInformation("Getting cats by availability: {Available}. IncludeImages: {IncludeImages}", available, includeImages);
                var cats = await _catService.GetCatsByAvailabilityAsync(available, includeImages);
                return Ok(cats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cats by availability: {Available}", available);
                return StatusCode(StatusCodes.Status500InternalServerError, "Error retrieving data from the database");
            }
        }

        /// <summary>
        /// Get cats by breed
        /// </summary>
        /// <param name="breed">Breed to filter by</param>
        /// <param name="includeImages">Whether to include images in the response</param>
        /// <param name="includeInactive">Whether to include inactive cats</param>
        /// <returns>Filtered collection of cats</returns>
        [HttpGet("breed/{breed}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Cat>>> GetCatsByBreed(
            string breed, 
            [FromQuery] bool includeImages = false,
            [FromQuery] bool includeInactive = false)
        {
            try
            {
                _logger.LogInformation("Getting cats by breed: {Breed}. IncludeImages: {IncludeImages}, IncludeInactive: {IncludeInactive}", 
                    breed, includeImages, includeInactive);
                var cats = await _catService.GetCatsByBreedAsync(breed, includeImages, includeInactive);
                return Ok(cats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cats by breed: {Breed}", breed);
                return StatusCode(StatusCodes.Status500InternalServerError, "Error retrieving data from the database");
            }
        }

        /// <summary>
        /// Set cat availability status
        /// </summary>
        /// <param name="id">ID of the cat</param>
        /// <param name="isAvailable">New availability status</param>
        /// <returns>No content if successful</returns>
        [HttpPut("{id}/availability")]
        [Authorize(Roles = "Admin,Editor")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SetCatAvailability(int id, [FromQuery] bool isAvailable)
        {
            try
            {
                _logger.LogInformation("Setting availability for cat with ID: {CatId} to {IsAvailable}", id, isAvailable);
                
                try
                {
                    await _catService.SetAvailabilityAsync(id, isAvailable);
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning("Cat with ID {CatId} not found for availability update", id);
                    return NotFound();
                }
                
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting availability for cat with ID: {CatId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "Error updating cat in the database");
            }
        }

        /// <summary>
        /// Set cat active status
        /// </summary>
        /// <param name="id">ID of the cat</param>
        /// <param name="isActive">New active status</param>
        /// <returns>No content if successful</returns>
        [HttpPut("{id}/active")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SetCatActiveStatus(int id, [FromQuery] bool isActive)
        {
            try
            {
                _logger.LogInformation("Setting active status for cat with ID: {CatId} to {IsActive}", id, isActive);
                
                try
                {
                    await _catService.SetActiveStatusAsync(id, isActive);
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning("Cat with ID {CatId} not found for active status update", id);
                    return NotFound();
                }
                
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting active status for cat with ID: {CatId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "Error updating cat in the database");
            }
        }

        /// <summary>
        /// Get all available breeds
        /// </summary>
        /// <returns>Collection of all breeds</returns>
        [HttpGet("breeds")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<string>>> GetAllBreeds()
        {
            try
            {
                _logger.LogInformation("Getting all unique cat breeds");
                var breeds = await _catService.GetAllBreedsAsync();
                return Ok(breeds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all breeds");
                return StatusCode(StatusCodes.Status500InternalServerError, "Error retrieving data from the database");
            }
        }
    }
}