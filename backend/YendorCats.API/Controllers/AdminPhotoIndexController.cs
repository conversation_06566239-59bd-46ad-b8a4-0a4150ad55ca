using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Attributes;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Admin controller for managing the photo index
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize("SuperAdmin", "Admin")]
    public class AdminPhotoIndexController : ControllerBase
    {
        private readonly IPhotoIndexService _photoIndexService;
        private readonly ILogger<AdminPhotoIndexController> _logger;

        public AdminPhotoIndexController(
            IPhotoIndexService photoIndexService,
            ILogger<AdminPhotoIndexController> logger)
        {
            _photoIndexService = photoIndexService;
            _logger = logger;
        }

        /// <summary>
        /// Refresh the photo index from S3 (requires admin authentication and S3 credentials)
        /// </summary>
        /// <returns>Refresh result</returns>
        [HttpPost("refresh")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RefreshIndex()
        {
            try
            {
                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} requesting photo index refresh", adminUser?.Username);

                var success = await _photoIndexService.RefreshIndexAsync();

                if (success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = "Photo index refreshed successfully",
                        refreshedBy = adminUser?.Username,
                        refreshedAt = DateTime.UtcNow
                    });
                }
                else
                {
                    return StatusCode(500, new
                    {
                        success = false,
                        message = "Failed to refresh photo index. Check logs for details."
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing photo index");
                return StatusCode(500, new
                {
                    success = false,
                    message = "An error occurred while refreshing the photo index"
                });
            }
        }

        /// <summary>
        /// Get photo index status and statistics
        /// </summary>
        /// <returns>Index status information</returns>
        [HttpGet("status")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetIndexStatus()
        {
            try
            {
                // Get some basic stats by trying to load featured photos
                var featuredPhotos = await _photoIndexService.GetFeaturedPhotosAsync(1);
                var studsPhotos = await _photoIndexService.GetCategoryPhotosAsync("studs");
                var queensPhotos = await _photoIndexService.GetCategoryPhotosAsync("queens");
                var kittensPhotos = await _photoIndexService.GetCategoryPhotosAsync("kittens");
                var galleryPhotos = await _photoIndexService.GetCategoryPhotosAsync("gallery");

                var totalPhotos = studsPhotos.Count + queensPhotos.Count + kittensPhotos.Count + galleryPhotos.Count;

                // Get unique cat names
                var allPhotos = new List<Models.CatGalleryImage>();
                allPhotos.AddRange(studsPhotos);
                allPhotos.AddRange(queensPhotos);
                allPhotos.AddRange(kittensPhotos);
                allPhotos.AddRange(galleryPhotos);

                var uniqueCats = allPhotos.Select(p => p.CatName).Distinct().Count();

                return Ok(new
                {
                    indexExists = totalPhotos > 0,
                    totalPhotos = totalPhotos,
                    uniqueCats = uniqueCats,
                    categories = new
                    {
                        studs = studsPhotos.Count,
                        queens = queensPhotos.Count,
                        kittens = kittensPhotos.Count,
                        gallery = galleryPhotos.Count
                    },
                    lastChecked = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photo index status");
                return StatusCode(500, new
                {
                    success = false,
                    message = "An error occurred while checking index status"
                });
            }
        }

        /// <summary>
        /// Add a photo to the index manually
        /// </summary>
        /// <param name="request">Add photo request</param>
        /// <returns>Success response</returns>
        [HttpPost("add-photo")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> AddPhotoToIndex([FromBody] AddPhotoToIndexRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} adding photo to index: {Category}/{CatName}/{FileName}", 
                    adminUser?.Username, request.Category, request.CatName, request.FileName);

                var metadata = new Dictionary<string, string>();
                if (!string.IsNullOrEmpty(request.CatName)) metadata["cat-name"] = request.CatName;
                if (!string.IsNullOrEmpty(request.Age)) metadata["age"] = request.Age;
                if (!string.IsNullOrEmpty(request.Description)) metadata["description"] = request.Description;
                if (!string.IsNullOrEmpty(request.Breed)) metadata["breed"] = request.Breed;
                if (!string.IsNullOrEmpty(request.Gender)) metadata["gender"] = request.Gender;
                if (!string.IsNullOrEmpty(request.Color)) metadata["color"] = request.Color;

                var success = await _photoIndexService.AddPhotoToIndexAsync(
                    request.Category, 
                    request.CatName, 
                    request.FileName, 
                    metadata);

                if (success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = "Photo added to index successfully",
                        addedBy = adminUser?.Username,
                        addedAt = DateTime.UtcNow
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Photo already exists in index or failed to add"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding photo to index");
                return StatusCode(500, new
                {
                    success = false,
                    message = "An error occurred while adding photo to index"
                });
            }
        }

        /// <summary>
        /// Remove a photo from the index
        /// </summary>
        /// <param name="request">Remove photo request</param>
        /// <returns>Success response</returns>
        [HttpPost("remove-photo")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> RemovePhotoFromIndex([FromBody] RemovePhotoFromIndexRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} removing photo from index: {Category}/{CatName}/{FileName}", 
                    adminUser?.Username, request.Category, request.CatName, request.FileName);

                var success = await _photoIndexService.RemovePhotoFromIndexAsync(
                    request.Category, 
                    request.CatName, 
                    request.FileName);

                if (success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = "Photo removed from index successfully",
                        removedBy = adminUser?.Username,
                        removedAt = DateTime.UtcNow
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Photo not found in index or failed to remove"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing photo from index");
                return StatusCode(500, new
                {
                    success = false,
                    message = "An error occurred while removing photo from index"
                });
            }
        }
    }

    /// <summary>
    /// Request model for adding photo to index
    /// </summary>
    public class AddPhotoToIndexRequest
    {
        public string Category { get; set; } = string.Empty;
        public string CatName { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string? Age { get; set; }
        public string? Description { get; set; }
        public string? Breed { get; set; }
        public string? Gender { get; set; }
        public string? Color { get; set; }
    }

    /// <summary>
    /// Request model for removing photo from index
    /// </summary>
    public class RemovePhotoFromIndexRequest
    {
        public string Category { get; set; } = string.Empty;
        public string CatName { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
    }
}
