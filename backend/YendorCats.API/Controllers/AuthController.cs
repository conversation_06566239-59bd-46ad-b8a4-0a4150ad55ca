using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for authentication operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly IUserService _userService;
        private readonly ILogger<AuthController> _logger;

        /// <summary>
        /// Constructor for AuthController
        /// </summary>
        /// <param name="authService">The authentication service</param>
        /// <param name="userService">The user service</param>
        /// <param name="logger">Logger for controller operations</param>
        public AuthController(
            IAuthService authService,
            IUserService userService,
            ILogger<AuthController> logger)
        {
            _authService = authService;
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// Login with username and password
        /// </summary>
        /// <param name="loginModel">Login credentials</param>
        /// <returns>JWT token and refresh token</returns>
        [HttpPost("login")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Login([FromBody] LoginModel loginModel)
        {
            try
            {
                _logger.LogInformation("Login attempt for user: {Username}", loginModel.Username);
                var (token, refreshToken) = await _authService.AuthenticateAsync(loginModel.Username, loginModel.Password);
                
                return Ok(new 
                { 
                    token,
                    refreshToken,
                    message = "Login successful"
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning("Login failed: {ErrorMessage}", ex.Message);
                return Unauthorized(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Register a new user
        /// </summary>
        /// <param name="registerModel">Registration information</param>
        /// <returns>Success message and user ID</returns>
        [HttpPost("register")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Register([FromBody] RegisterModel registerModel)
        {
            try
            {
                _logger.LogInformation("Registration attempt for username: {Username}", registerModel.Username);
                
                // Check if username or email is already taken
                if (!await _userService.IsUsernameAvailableAsync(registerModel.Username))
                {
                    _logger.LogWarning("Registration failed: Username {Username} is already taken", registerModel.Username);
                    return BadRequest(new { message = "Username is already taken" });
                }
                
                if (!await _userService.IsEmailAvailableAsync(registerModel.Email))
                {
                    _logger.LogWarning("Registration failed: Email {Email} is already registered", registerModel.Email);
                    return BadRequest(new { message = "Email is already registered" });
                }
                
                // Create new user
                var user = new User
                {
                    Username = registerModel.Username,
                    Email = registerModel.Email,
                    FirstName = registerModel.FirstName,
                    LastName = registerModel.LastName,
                    Role = "Viewer" // Default role for new users
                };
                
                var createdUser = await _userService.AddUserAsync(user, registerModel.Password);
                
                _logger.LogInformation("User registered successfully: {Username}, ID: {UserId}", 
                    registerModel.Username, createdUser.Id);
                
                return CreatedAtAction(nameof(Login), new { id = createdUser.Id }, 
                    new { message = "Registration successful", userId = createdUser.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration");
                return BadRequest(new { message = "Registration failed: " + ex.Message });
            }
        }

        /// <summary>
        /// Refresh an access token using a refresh token
        /// </summary>
        /// <param name="refreshModel">The refresh token</param>
        /// <returns>New JWT token and refresh token</returns>
        [HttpPost("refresh-token")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenModel refreshModel)
        {
            try
            {
                _logger.LogInformation("Processing refresh token request");
                var (newToken, newRefreshToken) = await _authService.RefreshTokenAsync(refreshModel.RefreshToken);
                
                return Ok(new 
                { 
                    token = newToken,
                    refreshToken = newRefreshToken
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning("Refresh token failed: {ErrorMessage}", ex.Message);
                return Unauthorized(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Revoke a refresh token (logout)
        /// </summary>
        /// <returns>Success message</returns>
        [Authorize]
        [HttpPost("logout")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var token = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                var userId = _authService.GetUserIdFromToken(token);
                
                _logger.LogInformation("Logout request for user ID: {UserId}", userId);
                await _authService.RevokeRefreshTokenAsync(userId);
                
                return Ok(new { message = "Logged out successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return BadRequest(new { message = "Logout failed: " + ex.Message });
            }
        }

        /// <summary>
        /// Validate a token
        /// </summary>
        /// <param name="tokenModel">The token to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        [HttpPost("validate-token")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public IActionResult ValidateToken([FromBody] ValidateTokenModel tokenModel)
        {
            _logger.LogInformation("Token validation request");
            var isValid = _authService.ValidateToken(tokenModel.Token);
            return Ok(new { isValid });
        }
    }

    /// <summary>
    /// Model for login requests
    /// </summary>
    public class LoginModel
    {
        /// <summary>
        /// Username for login
        /// </summary>
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// Password for login
        /// </summary>
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// Model for user registration
    /// </summary>
    public class RegisterModel
    {
        /// <summary>
        /// Username for the new account
        /// </summary>
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// Email address
        /// </summary>
        public string Email { get; set; } = string.Empty;
        
        /// <summary>
        /// First name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;
        
        /// <summary>
        /// Last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;
        
        /// <summary>
        /// Password for the new account
        /// </summary>
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// Model for refresh token requests
    /// </summary>
    public class RefreshTokenModel
    {
        /// <summary>
        /// The refresh token
        /// </summary>
        public string RefreshToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// Model for token validation requests
    /// </summary>
    public class ValidateTokenModel
    {
        /// <summary>
        /// The token to validate
        /// </summary>
        public string Token { get; set; } = string.Empty;
    }
} 