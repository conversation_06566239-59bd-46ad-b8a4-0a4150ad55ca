using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using YendorCats.API.Attributes;
using YendorCats.API.Data;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Admin user management controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize]
    public class AdminUserManagementController : ControllerBase
    {
        private readonly IAdminAuthService _adminAuthService;
        private readonly AppDbContext _context;
        private readonly ILogger<AdminUserManagementController> _logger;

        public AdminUserManagementController(
            IAdminAuthService adminAuthService,
            AppDbContext context,
            ILogger<AdminUserManagementController> logger)
        {
            _adminAuthService = adminAuthService;
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Change current user's password
        /// </summary>
        /// <param name="request">Password change request</param>
        /// <returns>Success response</returns>
        [HttpPost("change-password")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var currentAdmin = HttpContext.GetAdminUser();
                if (currentAdmin == null)
                {
                    return Unauthorized(new { message = "Admin user not found" });
                }

                // Verify current password
                if (!_adminAuthService.VerifyPassword(request.CurrentPassword, currentAdmin.PasswordHash, currentAdmin.PasswordSalt))
                {
                    return BadRequest(new { message = "Current password is incorrect" });
                }

                // Hash new password
                var (newHashedPassword, newSalt) = _adminAuthService.HashPassword(request.NewPassword);

                // Update password in database
                currentAdmin.PasswordHash = newHashedPassword;
                currentAdmin.PasswordSalt = newSalt;
                currentAdmin.FailedLoginAttempts = 0; // Reset failed attempts
                currentAdmin.LockedUntil = null; // Remove any lockout

                await _context.SaveChangesAsync();

                _logger.LogInformation("Admin {Username} changed their password", currentAdmin.Username);

                return Ok(new
                {
                    success = true,
                    message = "Password changed successfully",
                    changedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for admin user");
                return StatusCode(500, new { message = "An error occurred while changing password" });
            }
        }

        /// <summary>
        /// Update current user's profile information
        /// </summary>
        /// <param name="request">Profile update request</param>
        /// <returns>Updated user info</returns>
        [HttpPut("update-profile")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var currentAdmin = HttpContext.GetAdminUser();
                if (currentAdmin == null)
                {
                    return Unauthorized(new { message = "Admin user not found" });
                }

                // Check if email is already taken by another user
                var existingUser = await _context.AdminUsers
                    .FirstOrDefaultAsync(u => u.Email == request.Email && u.Id != currentAdmin.Id);

                if (existingUser != null)
                {
                    return BadRequest(new { message = "Email is already in use by another admin" });
                }

                // Update profile information
                currentAdmin.Email = request.Email;
                // Note: Username changes might require additional validation

                await _context.SaveChangesAsync();

                _logger.LogInformation("Admin {Username} updated their profile", currentAdmin.Username);

                return Ok(new AdminUserInfo
                {
                    Id = currentAdmin.Id,
                    Username = currentAdmin.Username,
                    Email = currentAdmin.Email,
                    Role = currentAdmin.Role,
                    LastLoginAt = currentAdmin.LastLoginAt
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating profile for admin user");
                return StatusCode(500, new { message = "An error occurred while updating profile" });
            }
        }

        /// <summary>
        /// List all admin users (SuperAdmin only)
        /// </summary>
        /// <returns>List of admin users</returns>
        [HttpGet("list")]
        [AdminAuthorize("SuperAdmin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ListAdminUsers()
        {
            try
            {
                var adminUsers = await _context.AdminUsers
                    .Select(u => new AdminUserInfo
                    {
                        Id = u.Id,
                        Username = u.Username,
                        Email = u.Email,
                        Role = u.Role,
                        LastLoginAt = u.LastLoginAt
                    })
                    .OrderBy(u => u.Username)
                    .ToListAsync();

                return Ok(new
                {
                    users = adminUsers,
                    count = adminUsers.Count,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing admin users");
                return StatusCode(500, new { message = "An error occurred while retrieving admin users" });
            }
        }

        /// <summary>
        /// Create new admin user (SuperAdmin only)
        /// </summary>
        /// <param name="request">Create admin request</param>
        /// <returns>Created admin user info</returns>
        [HttpPost("create")]
        [AdminAuthorize("SuperAdmin")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreateAdminUser([FromBody] CreateAdminUserRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var currentAdmin = HttpContext.GetAdminUser();
                
                var newAdmin = await _adminAuthService.CreateAdminAsync(
                    request.Username,
                    request.Email,
                    request.Password,
                    request.Role);

                _logger.LogInformation("SuperAdmin {Username} created new admin user: {NewUsername} with role {Role}",
                    currentAdmin?.Username, request.Username, request.Role);

                return CreatedAtAction(nameof(GetAdminUser), new { id = newAdmin.Id }, new AdminUserInfo
                {
                    Id = newAdmin.Id,
                    Username = newAdmin.Username,
                    Email = newAdmin.Email,
                    Role = newAdmin.Role
                });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating admin user");
                return StatusCode(500, new { message = "An error occurred while creating admin user" });
            }
        }

        /// <summary>
        /// Get admin user by ID (SuperAdmin only)
        /// </summary>
        /// <param name="id">Admin user ID</param>
        /// <returns>Admin user info</returns>
        [HttpGet("{id}")]
        [AdminAuthorize("SuperAdmin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAdminUser(int id)
        {
            try
            {
                var adminUser = await _context.AdminUsers.FindAsync(id);

                if (adminUser == null)
                {
                    return NotFound(new { message = "Admin user not found" });
                }

                return Ok(new AdminUserInfo
                {
                    Id = adminUser.Id,
                    Username = adminUser.Username,
                    Email = adminUser.Email,
                    Role = adminUser.Role,
                    LastLoginAt = adminUser.LastLoginAt
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving admin user {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving admin user" });
            }
        }

        /// <summary>
        /// Update admin user role (SuperAdmin only)
        /// </summary>
        /// <param name="id">Admin user ID</param>
        /// <param name="request">Role update request</param>
        /// <returns>Updated admin user info</returns>
        [HttpPut("{id}/role")]
        [AdminAuthorize("SuperAdmin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateAdminRole(int id, [FromBody] UpdateRoleRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = await _context.AdminUsers.FindAsync(id);
                if (adminUser == null)
                {
                    return NotFound(new { message = "Admin user not found" });
                }

                var currentAdmin = HttpContext.GetAdminUser();
                
                // Prevent SuperAdmin from demoting themselves
                if (adminUser.Id == currentAdmin?.Id && request.Role != AdminRoles.SuperAdmin)
                {
                    return BadRequest(new { message = "Cannot change your own SuperAdmin role" });
                }

                adminUser.Role = request.Role;
                await _context.SaveChangesAsync();

                _logger.LogInformation("SuperAdmin {Username} changed role of {TargetUsername} to {NewRole}",
                    currentAdmin?.Username, adminUser.Username, request.Role);

                return Ok(new AdminUserInfo
                {
                    Id = adminUser.Id,
                    Username = adminUser.Username,
                    Email = adminUser.Email,
                    Role = adminUser.Role,
                    LastLoginAt = adminUser.LastLoginAt
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating admin role for user {Id}", id);
                return StatusCode(500, new { message = "An error occurred while updating admin role" });
            }
        }

        /// <summary>
        /// Deactivate admin user (SuperAdmin only)
        /// </summary>
        /// <param name="id">Admin user ID</param>
        /// <returns>Success response</returns>
        [HttpPost("{id}/deactivate")]
        [AdminAuthorize("SuperAdmin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DeactivateAdminUser(int id)
        {
            try
            {
                var adminUser = await _context.AdminUsers.FindAsync(id);
                if (adminUser == null)
                {
                    return NotFound(new { message = "Admin user not found" });
                }

                var currentAdmin = HttpContext.GetAdminUser();
                
                // Prevent SuperAdmin from deactivating themselves
                if (adminUser.Id == currentAdmin?.Id)
                {
                    return BadRequest(new { message = "Cannot deactivate your own account" });
                }

                adminUser.IsActive = false;
                await _context.SaveChangesAsync();

                _logger.LogInformation("SuperAdmin {Username} deactivated admin user: {TargetUsername}",
                    currentAdmin?.Username, adminUser.Username);

                return Ok(new
                {
                    success = true,
                    message = "Admin user deactivated successfully",
                    deactivatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating admin user {Id}", id);
                return StatusCode(500, new { message = "An error occurred while deactivating admin user" });
            }
        }
    }

    /// <summary>
    /// Change password request model
    /// </summary>
    public class ChangePasswordRequest
    {
        [Required]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 8)]
        public string NewPassword { get; set; } = string.Empty;

        [Required]
        [Compare("NewPassword")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// Update profile request model
    /// </summary>
    public class UpdateProfileRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
    }

    /// <summary>
    /// Create admin user request model
    /// </summary>
    public class CreateAdminUserRequest
    {
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 8)]
        public string Password { get; set; } = string.Empty;

        [Required]
        public string Role { get; set; } = AdminRoles.Admin;
    }

    /// <summary>
    /// Update role request model
    /// </summary>
    public class UpdateRoleRequest
    {
        [Required]
        public string Role { get; set; } = string.Empty;
    }
}
