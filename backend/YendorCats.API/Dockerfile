# Stage 1: Build the application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy the solution file and restore dependencies
COPY yendorcats.sln .
COPY backend/YendorCats.API/YendorCats.API.csproj backend/YendorCats.API/
RUN dotnet restore yendorcats.sln

# Copy the rest of the application code
COPY . .
WORKDIR /src/backend/YendorCats.API
RUN dotnet publish -c Release -o /app/publish

# Stage 2: Create migrations and build the database
FROM build AS database-builder
WORKDIR /src/backend/YendorCats.API

# Install EF tools and create database
RUN dotnet tool install --global dotnet-ef && \
    export PATH="$PATH:$HOME/.dotnet/tools" && \
    mkdir -p /app/data && \
    export ConnectionStrings__SqliteConnection="Data Source=/app/data/yendorcats.db" && \
    dotnet ef database update --no-build && \
    chmod 644 /app/data/yendorcats.db || echo "Database creation failed, will create at runtime"

# Stage 3: Create the runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Create data directory and set permissions
RUN mkdir -p /app/data && chmod 777 /app/data

# Copy the published application
COPY --from=build /app/publish .

# Copy the pre-built database (if it exists)
COPY --from=database-builder /app/data/yendorcats.db /app/data/yendorcats.db.prebuilt

# Copy database scripts
COPY --from=build /src/backend/YendorCats.API/Data/init-db.sh /app/

# Add entrypoint script to handle database initialization
RUN echo '#!/bin/bash \n\
if [ ! -f /app/data/yendorcats.db ] && [ -f /app/data/yendorcats.db.prebuilt ]; then \n\
  echo "Copying pre-built database..." \n\
  cp /app/data/yendorcats.db.prebuilt /app/data/yendorcats.db \n\
  chmod 644 /app/data/yendorcats.db \n\
fi \n\
exec dotnet YendorCats.API.dll \n\
' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh /app/init-db.sh

# Set up volumes for data persistence
VOLUME ["/app/data"]

ENTRYPOINT ["/app/entrypoint.sh"]
