FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["YendorCats.API.csproj", "./"]
RUN dotnet restore "YendorCats.API.csproj"
COPY . .
RUN dotnet build "YendorCats.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "YendorCats.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create directories for volumes
RUN mkdir -p /app/Data
RUN mkdir -p /app/Logs

# Set permissions
RUN chmod -R 755 /app

ENTRYPOINT ["dotnet", "YendorCats.API.dll"]
