namespace YendorCats.API.Configuration
{
    /// <summary>
    /// Configuration settings for JWT authentication
    /// </summary>
    public class JwtSettings
    {
        /// <summary>
        /// Secret key used to sign the JW<PERSON> token
        /// </summary>
        public string Secret { get; set; } = string.Empty;
        
        /// <summary>
        /// Issuer of the JWT token
        /// </summary>
        public string Issuer { get; set; } = string.Empty;
        
        /// <summary>
        /// Audience of the JWT token
        /// </summary>
        public string Audience { get; set; } = string.Empty;
        
        /// <summary>
        /// Token expiry time in minutes
        /// </summary>
        public int ExpiryMinutes { get; set; } = 60;
        
        /// <summary>
        /// Refresh token expiry time in days
        /// </summary>
        public int RefreshTokenExpiryDays { get; set; } = 7;
    }
} 