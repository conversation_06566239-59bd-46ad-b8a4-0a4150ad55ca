using System.IO;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace YendorCats.API.Middleware
{
    /// <summary>
    /// Middleware to watch for file changes in the frontend directory and copy them to wwwroot.
    /// </summary>
    public static class FileWatcherMiddleware
    {
        /// <summary>
        /// Configures a file system watcher to monitor changes in the frontend directory
        /// and automatically copy updated files to wwwroot.
        /// </summary>
        /// <param name="app">The application builder</param>
        /// <param name="env">The web hosting environment</param>
        /// <param name="logger">The logger</param>
        /// <returns>The application builder</returns>
        public static IApplicationBuilder UseFileWatcher(
            this IApplicationBuilder app, 
            IWebHostEnvironment env,
            ILogger<FileSystemWatcher> logger)
        {
            // Only use in development environment
            if (!env.IsDevelopment())
            {
                return app;
            }
            
            // Source and destination directories
            var frontendPath = Path.GetFullPath(Path.Combine(env.ContentRootPath, "../../frontend"));
            var wwwrootPath = env.WebRootPath;
            
            if (!Directory.Exists(frontendPath))
            {
                logger.LogWarning("Frontend directory not found at {Path}", frontendPath);
                return app;
            }
            
            logger.LogInformation("Setting up file watcher for frontend directory: {Path}", frontendPath);
            
            // Set up the file system watcher
            var watcher = new FileSystemWatcher(frontendPath)
            {
                IncludeSubdirectories = true,
                EnableRaisingEvents = true,
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName | NotifyFilters.DirectoryName
            };
            
            // Handle file creation and modification events
            watcher.Created += (sender, e) => CopyFileToWwwroot(e.FullPath, frontendPath, wwwrootPath, logger);
            watcher.Changed += (sender, e) => CopyFileToWwwroot(e.FullPath, frontendPath, wwwrootPath, logger);
            watcher.Renamed += (sender, e) => CopyFileToWwwroot(e.FullPath, frontendPath, wwwrootPath, logger);
            
            // Handle file deletion events
            watcher.Deleted += (sender, e) => 
            {
                try
                {
                    var relativePath = Path.GetRelativePath(frontendPath, e.FullPath);
                    var destinationPath = Path.Combine(wwwrootPath, relativePath);
                    
                    if (File.Exists(destinationPath))
                    {
                        File.Delete(destinationPath);
                        logger.LogInformation("Deleted file: {Path}", destinationPath);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error deleting file {Path}", e.FullPath);
                }
            };
            
            logger.LogInformation("File watcher middleware initialized");
            return app;
        }
        
        /// <summary>
        /// Copies a file from the frontend directory to wwwroot.
        /// </summary>
        private static void CopyFileToWwwroot(string sourcePath, string frontendPath, string wwwrootPath, ILogger logger)
        {
            try
            {
                // Skip directories
                if (Directory.Exists(sourcePath))
                {
                    return;
                }
                
                // Get the relative path to maintain the same structure
                var relativePath = Path.GetRelativePath(frontendPath, sourcePath);
                var destinationPath = Path.Combine(wwwrootPath, relativePath);
                
                // Create the directory if it doesn't exist
                var destinationDir = Path.GetDirectoryName(destinationPath);
                if (!Directory.Exists(destinationDir))
                {
                    Directory.CreateDirectory(destinationDir);
                }
                
                // Copy the file
                File.Copy(sourcePath, destinationPath, true);
                logger.LogInformation("Updated file: {Path}", destinationPath);
                
                // Trigger live reload to refresh browsers
                LiveReloadMiddlewareExtensions.NotifyLiveReloadChange();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error copying file {Path}", sourcePath);
            }
        }
    }
} 