using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;

namespace YendorCats.API.Middleware
{
    /// <summary>
    /// Middleware to handle live reload functionality for frontend development.
    /// </summary>
    public class LiveReloadMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IHostEnvironment _environment;
        private static string _currentVersion = DateTime.UtcNow.Ticks.ToString();

        /// <summary>
        /// Initializes a new instance of the <see cref="LiveReloadMiddleware"/> class.
        /// </summary>
        /// <param name="next">The next middleware in the pipeline.</param>
        /// <param name="environment">The hosting environment.</param>
        public LiveReloadMiddleware(RequestDelegate next, IHostEnvironment environment)
        {
            _next = next;
            _environment = environment;
        }

        /// <summary>
        /// Invokes the middleware.
        /// </summary>
        /// <param name="context">The HTTP context.</param>
        public async Task InvokeAsync(HttpContext context)
        {
            // Only process in development environment
            if (!_environment.IsDevelopment())
            {
                await _next(context);
                return;
            }

            // Check if this is a request for the live reload check file
            if (context.Request.Path.StartsWithSegments("/live-reload-check.js"))
            {
                await HandleLiveReloadCheckAsync(context);
                return;
            }

            await _next(context);
        }

        /// <summary>
        /// Updates the version number to trigger a browser refresh.
        /// </summary>
        public static void NotifyChange()
        {
            _currentVersion = DateTime.UtcNow.Ticks.ToString();
        }

        private async Task HandleLiveReloadCheckAsync(HttpContext context)
        {
            // Generate the JavaScript with the current version
            var js = $"const VERSION = '{_currentVersion}';";
            
            context.Response.ContentType = "application/javascript";
            context.Response.Headers.Add("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
            
            await context.Response.WriteAsync(js, Encoding.UTF8);
        }
    }

    /// <summary>
    /// Extension methods for the LiveReloadMiddleware.
    /// </summary>
    public static class LiveReloadMiddlewareExtensions
    {
        /// <summary>
        /// Adds the live reload middleware to the application pipeline.
        /// </summary>
        /// <param name="app">The application builder.</param>
        /// <returns>The application builder.</returns>
        public static IApplicationBuilder UseLiveReload(this IApplicationBuilder app)
        {
            return app.UseMiddleware<LiveReloadMiddleware>();
        }

        /// <summary>
        /// Notifies the live reload middleware that a change has occurred.
        /// </summary>
        public static void NotifyLiveReloadChange()
        {
            LiveReloadMiddleware.NotifyChange();
        }
    }
} 