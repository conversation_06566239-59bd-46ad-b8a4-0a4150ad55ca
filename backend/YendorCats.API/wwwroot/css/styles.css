/*
 * Yendor Cats - Exotic Cat Breeder Website
 * Main Stylesheet
 * 
 * This stylesheet contains all the styles for the Yendor Cats website.
 * It uses a mobile-first approach with media queries for larger screens.
 */

/* --------------------------------------------------
 * CSS Variables for consistent theming and easy updates
 * -------------------------------------------------- */
:root {
    /* Primary Colors */
    --primary-color: #fae34b;       /* Rich purple - primary brand color */
    --secondary-color: #f2a365;     /* Warm orange - accent color */
    --tertiary-color: #30475e;      /* Deep blue-gray - supporting color */
    
    /* Neutral Colors */
    --light-color: #c70000;         /* Light gray/off-white for backgrounds */
    --dark-color: #222831;          /* Near black for text */
    --gray-color: #bb000095;          /* Medium gray for secondary text */
    --border-color: #e0e0e0;        /* Light gray for borders */
    
    /* Status Colors */
    --success-color: #4caf50;       /* Green for success messages */
    --warning-color: #ff9800;       /* Orange for warnings */
    --error-color: #f44336;         /* Red for errors */
    
    /* Typography */
    --heading-font: 'Playfair Display', serif;
    --body-font: 'Raleway', sans-serif;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 5rem;
    
    /* Borders */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Container widths */
    --container-sm: 540px;
    --container-md: 720px;
    --container-lg: 960px;
    --container-xl: 1140px;
}

/* --------------------------------------------------
 * CSS Reset & Base Styles
 * -------------------------------------------------- */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--body-font);
    color: var(--dark-color);
    line-height: 1.6;
    background-color: var(--light-color);
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    font-weight: 700;
    color: var(--dark-color);
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--secondary-color);
}

ul, ol {
    list-style-position: inside;
    margin-bottom: var(--spacing-md);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

button, input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    color: inherit;
}

/* Prevent inconsistent button styling across browsers */
button {
    background: none;
    border: none;
    cursor: pointer;
}

/* Common utility classes */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section-padding {
    padding: var(--spacing-xl) 0;
}

.bg-light {
    background-color: #f8f8f8;
}

.bg-dark {
    background-color: var(--dark-color);
    color: var(--light-color);
}

.text-center {
    text-align: center;
}

.shadow-img {
    box-shadow: var(--shadow-md);
    border-radius: var(--border-radius-md);
}

/* Loader animation for async content */
.loader {
    width: 50px;
    height: 50px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    margin: var(--spacing-lg) auto;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    100% {
        transform: rotate(360deg);
    }
}

/* Section headers - consistent styling for all sections */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    position: relative;
    display: inline-block;
    padding-bottom: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 25%;
    width: 50%;
    height: 3px;
    background-color: var(--secondary-color);
}

.section-header p {
    color: var(--gray-color);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Buttons */
.btn-primary, .btn-secondary {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: transparent;
    color: var(--primary-color);
}

.btn-secondary {
    background-color: transparent;
    color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-color);
    color: white;
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 100;
}

.back-to-top.visible {
    opacity: 0.8;
    visibility: visible;
}

.back-to-top:hover {
    opacity: 1;
}

/* --------------------------------------------------
 * Header & Navigation
 * -------------------------------------------------- */
.header {
    background-color: white;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all var(--transition-medium);
}

.header.scrolled {
    box-shadow: var(--shadow-md);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo h1 {
    font-size: 1.8rem;
    margin-bottom: 0;
    color: var(--dark-color);
}

.logo h1 span {
    color: var(--primary-color);
}

.main-nav {
    display: flex;
    align-items: center;
}

.nav-list {
    display: none;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-list.active {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    text-align: center;
    z-index: 1000;
}

.nav-list li {
    margin: 0.5rem 0;
}

.nav-list li a {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--dark-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.nav-list li a:hover,
.nav-list li a.active {
    color: var(--primary-color);
}

.nav-list li a.btn-primary {
    color: white;
    margin-left: 0;
    margin-top: 0.5rem;
}

.nav-list li a.btn-primary:hover {
    color: var(--primary-color);
}

.mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    width: 30px;
    height: 30px;
    position: relative;
    cursor: pointer;
    z-index: 1001;
}

.mobile-menu-toggle span {
    display: block;
    width: 100%;
    height: 2px;
    background-color: var(--dark-color);
    margin: 6px 0;
    transition: all var(--transition-medium);
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(7px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* --------------------------------------------------
 * Hero Section
 * -------------------------------------------------- */
.hero {
    position: relative;
    height: 80vh;
    min-height: 500px;
    background-image: url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    display: flex;
    align-items: center;
    text-align: center;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    color: white;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-lg);
    color: rgba(255, 255, 255, 0.9);
}

.hero-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    justify-content: center;
}

.hero-buttons .btn-primary,
.hero-buttons .btn-secondary {
    min-width: 180px;
    margin: 0 auto;
}

/* --------------------------------------------------
 * About Section
 * -------------------------------------------------- */
.about-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.about-image {
    flex: 1;
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.about-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.about-image:hover img {
    transform: scale(1.05);
}

.about-text {
    flex: 1;
}

.about-text p:last-of-type {
    margin-bottom: var(--spacing-lg);
}

.about-text .btn-primary {
    display: inline-block;
    margin-top: var(--spacing-sm);
}

/* --------------------------------------------------
 * Breeds Section
 * -------------------------------------------------- */
.breeds-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

.breed-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
}

.breed-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.breed-image {
    height: 250px;
    overflow: hidden;
}

.breed-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.breed-card:hover .breed-image img {
    transform: scale(1.05);
}

.breed-content {
    padding: var(--spacing-lg);
}

.breed-content h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
}

.breed-content p {
    color: var(--gray-color);
    margin-bottom: var(--spacing-md);
}

.breed-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--gray-color);
    font-size: 0.9rem;
}

.breed-meta span {
    display: flex;
    align-items: center;
}

.breed-meta i {
    margin-right: 5px;
    color: var(--primary-color);
}

.breed-link {
    display: inline-block;
    margin-top: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
}

.breed-link::after {
    content: '→';
    margin-left: 5px;
    transition: transform var(--transition-fast);
}

.breed-link:hover::after {
    transform: translateX(5px);
}

/* --------------------------------------------------
 * Gallery Section
 * -------------------------------------------------- */
.gallery-filter {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    gap: 10px;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    height: 250px;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-item-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: var(--spacing-md);
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    color: white;
    opacity: 0;
    transition: opacity var(--transition-medium);
}

.gallery-item:hover .gallery-item-overlay {
    opacity: 1;
}

.gallery-item-overlay h4 {
    margin-bottom: 5px;
    color: white;
}

.gallery-item-overlay p {
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* Gallery Modal */
.gallery-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    overflow: auto;
}

.modal-close {
    color: white;
    position: absolute;
    top: 15px;
    right: 25px;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 2001;
}

.modal-content {
    display: block;
    max-width: 90%;
    max-height: 80vh;
    margin: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: var(--border-radius-md);
}

.modal-caption {
    color: white;
    text-align: center;
    padding: 10px 0;
    width: 80%;
    margin: 0 auto;
    position: absolute;
    bottom: 20px;
    left: 10%;
}

.modal-prev,
.modal-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 30px;
    font-weight: bold;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background var(--transition-fast);
}

.modal-prev:hover,
.modal-next:hover {
    background: rgba(0, 0, 0, 0.8);
}

.modal-prev {
    left: 20px;
}

.modal-next {
    right: 20px;
}

/* --------------------------------------------------
 * Available Cats Section
 * -------------------------------------------------- */
.filter-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.search-box input,
.filter-box select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: white;
}

.search-box input:focus,
.filter-box select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.cats-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

.cat-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
}

.cat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.cat-image {
    height: 250px;
    position: relative;
    overflow: hidden;
}

.cat-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.cat-card:hover .cat-image img {
    transform: scale(1.05);
}

.cat-status {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    z-index: 10;
}

.status-available {
    background-color: var(--success-color);
}

.status-reserved {
    background-color: var(--warning-color);
}

.status-sold {
    background-color: var(--error-color);
}

.cat-content {
    padding: var(--spacing-lg);
}

.cat-content h3 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
}

.cat-price {
    color: var(--primary-color);
    font-weight: 700;
}

.cat-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: var(--spacing-md);
}

.cat-detail {
    display: flex;
    align-items: center;
    color: var(--gray-color);
    font-size: 0.9rem;
}

.cat-detail i {
    margin-right: 5px;
    color: var(--primary-color);
}

.cat-content p {
    color: var(--gray-color);
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cat-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cat-footer .btn-primary {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-xl);
}

.pagination-item {
    display: inline-block;
    margin: 0 5px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    color: var(--dark-color);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.pagination-item:hover,
.pagination-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* --------------------------------------------------
 * Media Queries
 * -------------------------------------------------- */
@media (min-width: 1024px) {
    .nav-list {
        display: flex;
        align-items: center;
    }
    
    .nav-list li {
        margin: 0 0.75rem;
    }
    
    .nav-list li a.btn-primary {
        margin-left: 0.75rem;
        margin-top: 0;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
    
    .hero h2 {
        font-size: 3.5rem;
    }
    
    .hero p {
        font-size: 1.5rem;
    }
    
    .hero-buttons {
        flex-direction: row;
        justify-content: center;
    }
    
    .about-content {
        flex-direction: row;
    }
    
    .breeds-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-controls {
        flex-direction: row;
        justify-content: space-between;
    }
    
    .search-box {
        flex: 1;
        max-width: 300px;
    }
    
    .filter-box {
        width: 200px;
    }
    
    .cats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .breeds-container {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .cats-container {
        grid-template-columns: repeat(3, 1fr);
    }
} 