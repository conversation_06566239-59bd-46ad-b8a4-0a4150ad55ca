/**
 * Cat Carousel Module for Yendor Cats
 * Handles loading cat images from the API with metadata from filenames
 * and populating carousels with the images
 */

/**
 * CatImageLoader class for fetching cat images and populating carousels
 */
class CatImageLoader {
    /**
     * Initialize the image loader
     * @param {string} apiBaseUrl - The base URL for the API
     */
    constructor(apiBaseUrl = '/api') {
        this.apiBaseUrl = apiBaseUrl;
        this.imageCache = {}; // Cache for loaded images by category
        this.descriptions = {}; // Will store cat descriptions
        this.thumbGalleryPopup = document.querySelector('.thumbnail-gallery-popup');
        this.thumbGalleryTitle = this.thumbGalleryPopup?.querySelector('.thumbnail-gallery-title');
        this.thumbGalleryGrid = this.thumbGalleryPopup?.querySelector('.thumbnail-grid');
        
        // Load descriptions when initialized
        this.loadDescriptions();
        
        // Initialize thumbnail gallery popup
        this.initThumbnailGallery();
    }

    /**
     * Initialize the thumbnail gallery popup
     */
    initThumbnailGallery() {
        if (!this.thumbGalleryPopup) return;
        
        // Setup close button
        const closeButton = this.thumbGalleryPopup.querySelector('.thumbnail-gallery-close');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                this.closeThumbnailGallery();
            });
        }
        
        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.thumbGalleryPopup.classList.contains('active')) {
                this.closeThumbnailGallery();
            }
        });
        
        // Setup view all buttons
        document.querySelectorAll('.view-all-button').forEach(button => {
            const category = button.dataset.category;
            
            button.addEventListener('click', () => {
                this.openThumbnailGallery(category);
            });
        });
    }
    
    /**
     * Open the thumbnail gallery popup for a specific category
     * @param {string} category - The category to display (studs, queens, kittens, gallery)
     */
    async openThumbnailGallery(category) {
        if (!this.thumbGalleryPopup || !this.thumbGalleryGrid) return;
        
        // Set title based on category
        if (this.thumbGalleryTitle) {
            let title = 'All Photos';
            
            switch (category) {
                case 'studs':
                    title = 'All Studs Photos';
                    break;
                case 'queens':
                    title = 'All Queens Photos';
                    break;
                case 'kittens':
                    title = 'All Kittens Photos';
                    break;
                case 'gallery':
                    title = 'Our Cats Gallery';
                    break;
            }
            
            this.thumbGalleryTitle.textContent = title;
        }
        
        // Show loading
        this.thumbGalleryGrid.innerHTML = `
            <div class="loading-placeholder">
                <div class="spinner"></div>
                <p>Loading images...</p>
            </div>
        `;
        
        // Show the popup
        this.thumbGalleryPopup.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent scrolling
        
        try {
            // Fetch images for the category
            const images = await this.fetchCatImages(category, 'date', true);
            
            // Clear the grid
            this.thumbGalleryGrid.innerHTML = '';
            
            if (images.length === 0) {
                this.thumbGalleryGrid.innerHTML = `
                    <div class="empty-message">
                        <p>No images found for this category.</p>
                    </div>
                `;
                return;
            }
            
            // Create thumbnail items
            images.forEach(image => {
                const thumbnail = document.createElement('div');
                thumbnail.className = 'thumbnail-item';
                
                // Get a short description (first sentence)
                const fullDescription = this.getCatDescription(category, image.imageUrl);
                const shortDescription = fullDescription.split(/[.!?]/)[0] + '.';
                
                thumbnail.innerHTML = `
                    <img src="${image.imageUrl}" alt="${image.catName}">
                    <div class="thumbnail-info">
                        <h4>${image.catName}</h4>
                        <p>${this.formatAge(image.age)}</p>
                        <p class="thumbnail-description">${shortDescription}</p>
                    </div>
                `;
                
                this.thumbGalleryGrid.appendChild(thumbnail);
                
                // Add click event to open lightbox
                thumbnail.addEventListener('click', () => {
                    this.openThumbnailLightbox(images, images.indexOf(image));
                });
            });
        } catch (error) {
            console.error('Error loading thumbnail gallery:', error);
            this.thumbGalleryGrid.innerHTML = `
                <div class="error-message">
                    <p>Error loading images. Please try again later.</p>
                </div>
            `;
        }
    }
    
    /**
     * Close the thumbnail gallery popup
     */
    closeThumbnailGallery() {
        if (!this.thumbGalleryPopup) return;
        
        this.thumbGalleryPopup.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling
    }
    
    /**
     * Open lightbox from the thumbnail gallery
     * @param {Array} images - Array of all images
     * @param {number} startIndex - Index of the image to show first
     */
    openThumbnailLightbox(images, startIndex = 0) {
        // Close the thumbnail gallery popup
        this.closeThumbnailGallery();
        
        // Create lightbox if it doesn't exist yet
        if (!document.querySelector('.gallery-lightbox')) {
            this.createLightbox();
        }
        
        // Get lightbox elements
        const lightbox = document.querySelector('.gallery-lightbox');
        const lightboxImage = lightbox.querySelector('.lightbox-image');
        const lightboxCaption = lightbox.querySelector('.lightbox-caption');
        
        let currentIndex = startIndex;
        
        // Get the category from the section title
        const category = this.thumbGalleryTitle.textContent.toLowerCase().includes('studs') ? 'studs' : 
                       this.thumbGalleryTitle.textContent.toLowerCase().includes('queens') ? 'queens' :
                       this.thumbGalleryTitle.textContent.toLowerCase().includes('kittens') ? 'kittens' : 'gallery';
        
        // Open lightbox with selected image
        lightboxImage.src = images[currentIndex].imageUrl;
        
        // Add description to the caption
        const description = this.getCatDescription(category, images[currentIndex].imageUrl);
        lightboxCaption.innerHTML = `
            <h3>${images[currentIndex].catName}</h3>
            <p>${this.formatAge(images[currentIndex].age)}</p>
            <p class="cat-description">${description}</p>
        `;
        
        lightbox.classList.add('active');
        document.body.style.overflow = 'hidden'; // Keep preventing scrolling
        
        // Get navigation buttons
        const prevButton = lightbox.querySelector('.lightbox-nav-button.prev');
        const nextButton = lightbox.querySelector('.lightbox-nav-button.next');
        const closeButton = lightbox.querySelector('.lightbox-close');
        
        // Update the lightbox image and caption
        const updateLightboxImage = () => {
            lightboxImage.src = images[currentIndex].imageUrl;
            const description = this.getCatDescription(category, images[currentIndex].imageUrl);
            lightboxCaption.innerHTML = `
                <h3>${images[currentIndex].catName}</h3>
                <p>${this.formatAge(images[currentIndex].age)}</p>
                <p class="cat-description">${description}</p>
            `;
        };
        
        // Navigate to previous image
        const prevImage = () => {
            currentIndex = (currentIndex - 1 + images.length) % images.length;
            updateLightboxImage();
        };
        
        // Navigate to next image
        const nextImage = () => {
            currentIndex = (currentIndex + 1) % images.length;
            updateLightboxImage();
        };
        
        // Set up event listeners
        prevButton.onclick = prevImage;
        nextButton.onclick = nextImage;
        
        // Close lightbox
        closeButton.onclick = () => {
            lightbox.classList.remove('active');
        };
        
        // Close lightbox when clicking outside the image
        lightbox.onclick = (e) => {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
            }
        };
        
        // Keyboard navigation
        const keyHandler = (e) => {
            if (!lightbox.classList.contains('active')) return;
            
            if (e.key === 'Escape') {
                lightbox.classList.remove('active');
            } else if (e.key === 'ArrowLeft') {
                prevImage();
            } else if (e.key === 'ArrowRight') {
                nextImage();
            }
        };
        
        // Remove existing handlers and add new one
        document.removeEventListener('keydown', keyHandler);
        document.addEventListener('keydown', keyHandler);
    }

    /**
     * Format the cat's age in a user-friendly way
     * @param {number} age - The cat's age in years (where decimal part represents months directly)
     * @returns {string} - Formatted age string
     */
    formatAge(age) {
        if (age < 1) {
            // Convert to months for kittens less than 1 year old
            const months = Math.round(age * 12);
            return `${months} month${months !== 1 ? 's' : ''}`;
        }
        
        // For ages with decimal places - interpret decimal directly as months
        // e.g., 1.8 means 1 year and 8 months (decimal part is the month value directly)
        if (age % 1 !== 0) {
            const years = Math.floor(age);
            
            // Extract months directly from decimal part (e.g., 1.8 → 8 months)
            // First convert to string, split on decimal, take the decimal part, and convert back to number
            const decimalPart = age.toString().split('.')[1];
            let months = 0;
            
            // Handle different decimal formats (e.g., 1.8, 1.08)
            if (decimalPart.length === 1) {
                months = parseInt(decimalPart);
            } else {
                // For longer decimals, just take the first digit
                months = parseInt(decimalPart.charAt(0));
            }
            
            console.log(`Formatting age ${age}: ${years} years, ${months} months (decimal part: ${decimalPart})`);
            
            if (months === 0) {
                return `${years} year${years !== 1 ? 's' : ''}`;
            }
            
            return `${years} year${years !== 1 ? 's' : ''} ${months} month${months !== 1 ? 's' : ''}`;
        }
        
        // For whole number ages
        return `${age} year${age !== 1 ? 's' : ''}`;
    }

    /**
     * Format a date in a user-friendly way
     * @param {Date} date - The date to format
     * @returns {string} - Formatted date string
     */
    formatDate(date) {
        return new Date(date).toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
        });
    }

    /**
     * Fetch cat images from the API for a specific category
     * @param {string} category - The category to fetch (studs, queens, kittens, gallery)
     * @param {string} orderBy - Field to order by (date, name, age)
     * @param {boolean} descending - Whether to sort in descending order
     * @returns {Promise<Array>} - Promise resolving to an array of cat images
     */
    async fetchCatImages(category, orderBy = 'date', descending = true) {
        // Check cache first
        const cacheKey = `${category}-${orderBy}-${descending}`;
        if (this.imageCache[cacheKey]) {
            return this.imageCache[cacheKey];
        }
        
        try {
            const url = `${this.apiBaseUrl}/CatGallery/category/${category}?orderBy=${orderBy}&descending=${descending}`;
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`Failed to fetch cat images: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // Cache the results
            this.imageCache[cacheKey] = data;
            
            return data;
        } catch (error) {
            console.error('Error fetching cat images:', error);
            return [];
        }
    }

    /**
     * Populate a carousel with cat images
     * @param {string} carouselSelector - CSS selector for the carousel element
     * @param {string} category - Category of images to load
     * @param {string} orderBy - Field to order by (date, name, age)
     * @param {boolean} descending - Whether to sort in descending order
     */
    async populateCatCarousel(carouselSelector, category, orderBy = 'date', descending = true) {
        const carousel = document.querySelector(carouselSelector);
        if (!carousel) {
            console.error(`Carousel not found: ${carouselSelector}`);
            return;
        }
        
        // Clear existing content
        carousel.innerHTML = '';
        
        // Add loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'carousel-loading';
        loadingIndicator.innerHTML = '<div class="spinner"></div><p>Loading cats...</p>';
        carousel.appendChild(loadingIndicator);
        
        try {
            const catImages = await this.fetchCatImages(category, orderBy, descending);
            
            // Remove loading indicator
            carousel.removeChild(loadingIndicator);
            
            if (catImages.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'carousel-empty';
                emptyMessage.textContent = 'No cat images found for this category.';
                carousel.appendChild(emptyMessage);
                return;
            }
            
            // Images array for lightbox
            const images = [];
            const captions = [];
            
            // Create cards for each cat image
            catImages.forEach(image => {
                const card = document.createElement('div');
                card.className = 'cat-card';
                
                card.innerHTML = `
                    <div class="cat-image">
                        <img src="${image.imageUrl}" alt="${image.catName}">
                    </div>
                    <div class="cat-info">
                        <h3>${image.catName}</h3>
                        <p>Age: ${this.formatAge(image.age)}</p>
                        <p>Photo: ${this.formatDate(image.dateTaken)}</p>
                    </div>
                `;
                
                carousel.appendChild(card);
                
                // Store image and caption for lightbox
                const img = card.querySelector('img');
                const caption = `
                    <h3>${image.catName}</h3>
                    <p>Age: ${this.formatAge(image.age)}</p>
                    <p>Photo: ${this.formatDate(image.dateTaken)}</p>
                `;
                
                images.push(img);
                captions.push(caption);
                
                // Add click event to image to open lightbox
                img.style.cursor = 'pointer';
                img.addEventListener('click', () => {
                    this.openCategoryLightbox(img, images, captions, category);
                });
            });
        } catch (error) {
            console.error('Error populating cat carousel:', error);
            
            // Remove loading indicator
            if (carousel.contains(loadingIndicator)) {
                carousel.removeChild(loadingIndicator);
            }
            
            // Show error message
            const errorMessage = document.createElement('div');
            errorMessage.className = 'carousel-error';
            errorMessage.textContent = 'Could not load cat images. Please try again later.';
            carousel.appendChild(errorMessage);
        }
    }
    
    /**
     * Open lightbox for category images
     * @param {HTMLElement} clickedImg - The image that was clicked
     * @param {Array} images - Array of all images
     * @param {Array} captions - Array of captions for the images
     * @param {string} category - The category of the images (studs, queens, kittens)
     */
    openCategoryLightbox(clickedImg, images, captions, category = 'gallery') {
        // Create lightbox if it doesn't exist yet
        if (!document.querySelector('.gallery-lightbox')) {
            this.createLightbox();
        }
        
        // Get lightbox elements
        const lightbox = document.querySelector('.gallery-lightbox');
        const lightboxImage = lightbox.querySelector('.lightbox-image');
        const lightboxCaption = lightbox.querySelector('.lightbox-caption');
        
        // Find index of clicked image
        const currentIndex = images.indexOf(clickedImg);
        let currentLightboxIndex = currentIndex;
        
        // Determine the category based on the image path if not provided
        if (!category) {
            const imgSrc = clickedImg.src;
            if (imgSrc.includes('studs')) category = 'studs';
            else if (imgSrc.includes('queens')) category = 'queens';
            else if (imgSrc.includes('kittens')) category = 'kittens';
            else category = 'gallery';
        }
        
        // Open lightbox with clicked image
        lightboxImage.src = clickedImg.src;
        
        // Get the description
        const description = this.getCatDescription(category, clickedImg.src);
        
        // Add the description to the caption
        const captionHTML = captions[currentIndex];
        lightboxCaption.innerHTML = captionHTML + `<p class="cat-description">${description}</p>`;
        
        lightbox.classList.add('active');
        
        // Disable page scrolling
        document.body.style.overflow = 'hidden';
        
        // Add navigation
        const prevButton = lightbox.querySelector('.lightbox-nav-button.prev');
        const nextButton = lightbox.querySelector('.lightbox-nav-button.next');
        const closeButton = lightbox.querySelector('.lightbox-close');
        
        // Update navigation handlers for this set of images
        const goToImage = (index) => {
            currentLightboxIndex = index;
            lightboxImage.src = images[index].src;
            
            // Get description for the current image
            const description = this.getCatDescription(category, images[index].src);
            
            // Add the description to the caption
            const captionHTML = captions[index];
            lightboxCaption.innerHTML = captionHTML + `<p class="cat-description">${description}</p>`;
        };
        
        // Navigate to previous image
        const prevImage = () => {
            currentLightboxIndex = (currentLightboxIndex - 1 + images.length) % images.length;
            goToImage(currentLightboxIndex);
        };
        
        // Navigate to next image
        const nextImage = () => {
            currentLightboxIndex = (currentLightboxIndex + 1) % images.length;
            goToImage(currentLightboxIndex);
        };
        
        // Update event listeners
        prevButton.onclick = prevImage;
        nextButton.onclick = nextImage;
        
        // Close lightbox
        closeButton.onclick = () => {
            lightbox.classList.remove('active');
            document.body.style.overflow = '';
        };
        
        // Close lightbox when clicking outside the image
        lightbox.onclick = (e) => {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        };
        
        // Keyboard navigation
        const keyHandler = (e) => {
            if (!lightbox.classList.contains('active')) return;
            
            if (e.key === 'Escape') {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            } else if (e.key === 'ArrowLeft' && lightbox.classList.contains('active')) {
                currentLightboxIndex = (currentLightboxIndex - 1 + images.length) % images.length;
                lightboxImage.src = images[currentLightboxIndex].src;
                lightboxCaption.innerHTML = captions[currentLightboxIndex] + `<p class="cat-description">${this.getCatDescription(category, images[currentLightboxIndex].src)}</p>`;
            } else if (e.key === 'ArrowRight' && lightbox.classList.contains('active')) {
                currentLightboxIndex = (currentLightboxIndex + 1) % images.length;
                lightboxImage.src = images[currentLightboxIndex].src;
                lightboxCaption.innerHTML = captions[currentLightboxIndex] + `<p class="cat-description">${this.getCatDescription(category, images[currentLightboxIndex].src)}</p>`;
            }
        };
        
        // Remove existing handlers and add new one
        document.removeEventListener('keydown', keyHandler);
        document.addEventListener('keydown', keyHandler);
    }

    /**
     * Populate the gallery carousel with cat images
     * @param {string} carouselSelector - CSS selector for the gallery carousel
     * @param {string} category - Category of images to load (typically 'gallery')
     * @returns {Promise} - A promise that resolves when the population is complete
     */
    async populateGalleryCarousel(carouselSelector, category = 'gallery') {
        console.log(`Populating gallery carousel: ${carouselSelector} with category: ${category}`);
        
        return new Promise(async (resolve, reject) => {
            try {
                // Find the carousel element
                const carousel = document.querySelector(carouselSelector);
                if (!carousel) {
                    console.error(`Gallery carousel not found: ${carouselSelector}`);
                    reject(new Error(`Gallery carousel not found: ${carouselSelector}`));
                    return;
                }
                
                // Find the carousel container
                const container = carousel.querySelector('.carousel-container');
                if (!container) {
                    console.error('Carousel container not found');
                    reject(new Error('Carousel container not found'));
                    return;
                }
                
                // Clear existing slides
                container.innerHTML = '';
                console.log('✓ Cleared existing slides');
                
                // Fetch images from API
                console.log(`Fetching cat images for category: ${category}`);
                const catImages = await this.fetchCatImages(category, 'date', true);
                console.log(`✓ Received ${catImages.length} images`);
                
                // Handle no images case
                if (catImages.length === 0) {
                    console.log('No images found, adding empty message');
                    const emptySlide = document.createElement('div');
                    emptySlide.className = 'carousel-slide';
                    emptySlide.innerHTML = '<div class="empty-message">No images found</div>';
                    container.appendChild(emptySlide);
                    resolve();
                    return;
                }
                
                // Create slides for each image
                console.log(`Creating ${catImages.length} slides`);
                catImages.forEach((image, index) => {
                    // Create a new slide for each image
                    const slide = document.createElement('div');
                    slide.className = 'carousel-slide';
                    slide.dataset.index = index;
                    
                    // Add a special class to the first slide
                    const infoVisibleClass = index === 0 ? 'info-visible' : '';
                    
                    // Create the slide content
                    slide.innerHTML = `
                        <img src="${image.imageUrl}" alt="${image.catName}">
                        <div class="slide-info ${infoVisibleClass}">
                            <h3>${image.catName}</h3>
                            <p>${this.formatAge(image.age)}</p>
                        </div>
                    `;
                    
                    // Add the slide to the container
                    container.appendChild(slide);
                });
                
                console.log(`✓ Added ${catImages.length} slides to container`);
                
                // Create manual navigation instead of using Carousel class
                this.setupGalleryNavigation(carousel, catImages.length);
                
                // Resolve the promise
                resolve();
            } catch (error) {
                console.error('Error populating gallery carousel:', error);
                reject(error);
            }
        });
    }
    
    /**
     * Setup custom navigation for the gallery carousel
     * @param {HTMLElement} carousel - The carousel element
     * @param {number} totalSlides - Total number of slides
     */
    setupGalleryNavigation(carousel, totalSlides) {
        if (totalSlides <= 1) {
            console.log('Only one slide, no need for navigation');
            return;
        }
        
        console.log(`Setting up gallery navigation for ${totalSlides} slides`);
        
        // Remove any existing navigation
        const existingButtons = carousel.querySelectorAll('.carousel-button');
        existingButtons.forEach(button => button.remove());
        
        const existingIndicators = carousel.querySelector('.carousel-indicators');
        if (existingIndicators) {
            existingIndicators.remove();
        }
        
        const existingCounter = carousel.querySelector('.carousel-slide-counter');
        if (existingCounter) {
            existingCounter.remove();
        }
        
        // Create prev and next buttons
        const prevButton = document.createElement('button');
        prevButton.className = 'carousel-button prev';
        prevButton.setAttribute('aria-label', 'Previous slide');
        prevButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
        `;
        
        const nextButton = document.createElement('button');
        nextButton.className = 'carousel-button next';
        nextButton.setAttribute('aria-label', 'Next slide');
        nextButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
        `;
        
        // Add slide counter
        const slideCounter = document.createElement('div');
        slideCounter.className = 'carousel-slide-counter';
        slideCounter.innerHTML = `<span>1</span>/<span>${totalSlides}</span>`;
        
        // Add indicators
        const indicators = document.createElement('div');
        indicators.className = 'carousel-indicators';
        
        for (let i = 0; i < totalSlides; i++) {
            const indicator = document.createElement('button');
            indicator.className = i === 0 ? 'carousel-indicator active' : 'carousel-indicator';
            indicator.setAttribute('aria-label', `Go to slide ${i + 1}`);
            indicator.dataset.index = i;
            indicators.appendChild(indicator);
        }
        
        // Add elements to the carousel
        carousel.appendChild(prevButton);
        carousel.appendChild(nextButton);
        carousel.appendChild(slideCounter);
        carousel.appendChild(indicators);
        
        // Get the container and slides
        const container = carousel.querySelector('.carousel-container');
        const slides = carousel.querySelectorAll('.carousel-slide');
        
        // Set current index
        let currentIndex = 0;
        let isTransitioning = false;
        
        // Setup event listeners for navigation
        prevButton.addEventListener('click', () => {
            if (isTransitioning) return;
            
            currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
            goToSlide(currentIndex);
        });
        
        nextButton.addEventListener('click', () => {
            if (isTransitioning) return;
            
            currentIndex = (currentIndex + 1) % totalSlides;
            goToSlide(currentIndex);
        });
        
        // Add click listeners to indicators
        const indicatorButtons = indicators.querySelectorAll('.carousel-indicator');
        indicatorButtons.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                if (isTransitioning) return;
                
                currentIndex = index;
                goToSlide(currentIndex);
            });
        });
        
        // Function to go to a specific slide
        function goToSlide(index) {
            if (isTransitioning) return;
            
            isTransitioning = true;
            
            // Update current index
            currentIndex = index;
            
            // Calculate position
            const carouselWidth = carousel.offsetWidth;
            const position = -index * carouselWidth;
            
            // Apply transform
            container.style.transition = 'transform 0.5s ease';
            container.style.transform = `translateX(${position}px)`;
            
            // Update indicators
            indicatorButtons.forEach((indicator, i) => {
                if (i === index) {
                    indicator.classList.add('active');
                } else {
                    indicator.classList.remove('active');
                }
            });
            
            // Update counter
            slideCounter.innerHTML = `<span>${index + 1}</span>/<span>${totalSlides}</span>`;
            
            // Reset transition state
            setTimeout(() => {
                isTransitioning = false;
            }, 500);
        }
        
        // Add swipe support for mobile
        let touchStartX = 0;
        let touchEndX = 0;
        
        carousel.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
        }, { passive: true });
        
        carousel.addEventListener('touchmove', (e) => {
            touchEndX = e.touches[0].clientX;
        }, { passive: true });
        
        carousel.addEventListener('touchend', () => {
            const difference = touchStartX - touchEndX;
            const threshold = 50;
            
            if (difference > threshold) {
                // Swipe left, go to next slide
                currentIndex = (currentIndex + 1) % totalSlides;
                goToSlide(currentIndex);
            } else if (difference < -threshold) {
                // Swipe right, go to previous slide
                currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
                goToSlide(currentIndex);
            }
        });
        
        // Set up window resize handler
        window.addEventListener('resize', () => {
            // Recalculate position on resize without animation
            container.style.transition = 'none';
            const carouselWidth = carousel.offsetWidth;
            const position = -currentIndex * carouselWidth;
            container.style.transform = `translateX(${position}px)`;
            
            // Re-enable transition after a short delay
            setTimeout(() => {
                container.style.transition = 'transform 0.5s ease';
            }, 50);
        });
        
        // Initial slide setup
        container.style.display = 'flex';
        container.style.width = '100%';
        goToSlide(0);
        
        // Setup lightbox for images
        this.setupLightbox(carousel, slides);
        
        console.log('✓ Gallery navigation setup complete');
    }
    
    /**
     * Setup lightbox/popup for gallery images
     * @param {HTMLElement} carousel - The carousel element
     * @param {NodeList} slides - The carousel slides
     */
    setupLightbox(carousel, slides) {
        console.log('Setting up lightbox for gallery images');
        
        // Create lightbox if it doesn't exist yet
        if (!document.querySelector('.gallery-lightbox')) {
            this.createLightbox();
        }
        
        // Get lightbox elements
        const lightbox = document.querySelector('.gallery-lightbox');
        const lightboxImage = lightbox.querySelector('.lightbox-image');
        const lightboxCaption = lightbox.querySelector('.lightbox-caption');
        const closeButton = lightbox.querySelector('.lightbox-close');
        const prevButton = lightbox.querySelector('.lightbox-nav-button.prev');
        const nextButton = lightbox.querySelector('.lightbox-nav-button.next');
        
        // Get all images in the slides
        const images = [];
        const captions = [];
        
        slides.forEach(slide => {
            const img = slide.querySelector('img');
            const caption = slide.querySelector('.slide-info');
            
            if (img) {
                images.push(img);
                captions.push(caption ? caption.innerHTML : '');
                
                // Add click event to image to open lightbox
                img.style.cursor = 'pointer';
                img.addEventListener('click', () => {
                    const index = Array.from(slides).indexOf(slide);
                    openLightbox(index);
                });
            }
        });
        
        let currentLightboxIndex = 0;
        
        // Function to open lightbox with specific image
        function openLightbox(index) {
            if (index < 0 || index >= images.length) return;
            
            currentLightboxIndex = index;
            lightboxImage.src = images[index].src;
            lightboxCaption.innerHTML = captions[index];
            lightbox.classList.add('active');
            
            // Disable page scrolling
            document.body.style.overflow = 'hidden';
        }
        
        // Close lightbox
        closeButton.addEventListener('click', () => {
            lightbox.classList.remove('active');
            document.body.style.overflow = '';
        });
        
        // Navigate to previous image
        prevButton.addEventListener('click', () => {
            currentLightboxIndex = (currentLightboxIndex - 1 + images.length) % images.length;
            lightboxImage.src = images[currentLightboxIndex].src;
            lightboxCaption.innerHTML = captions[currentLightboxIndex];
        });
        
        // Navigate to next image
        nextButton.addEventListener('click', () => {
            currentLightboxIndex = (currentLightboxIndex + 1) % images.length;
            lightboxImage.src = images[currentLightboxIndex].src;
            lightboxCaption.innerHTML = captions[currentLightboxIndex];
        });
        
        // Close lightbox on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            } else if (e.key === 'ArrowLeft' && lightbox.classList.contains('active')) {
                currentLightboxIndex = (currentLightboxIndex - 1 + images.length) % images.length;
                lightboxImage.src = images[currentLightboxIndex].src;
                lightboxCaption.innerHTML = captions[currentLightboxIndex];
            } else if (e.key === 'ArrowRight' && lightbox.classList.contains('active')) {
                currentLightboxIndex = (currentLightboxIndex + 1) % images.length;
                lightboxImage.src = images[currentLightboxIndex].src;
                lightboxCaption.innerHTML = captions[currentLightboxIndex];
            }
        });
        
        // Close lightbox when clicking outside the image
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
        
        console.log('✓ Lightbox setup complete for gallery');
    }
    
    /**
     * Create lightbox/popup for images
     */
    createLightbox() {
        console.log('Creating lightbox');
        
        const lightbox = document.createElement('div');
        lightbox.className = 'gallery-lightbox';
        
        lightbox.innerHTML = `
            <div class="lightbox-content">
                <img class="lightbox-image" src="" alt="Enlarged view">
                <div class="lightbox-caption"></div>
                <button class="lightbox-close" aria-label="Close lightbox">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="lightbox-nav">
                <button class="lightbox-nav-button prev" aria-label="Previous image">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                </button>
                <button class="lightbox-nav-button next" aria-label="Next image">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(lightbox);
    }

    /**
     * Load cat descriptions from JSON file
     */
    async loadDescriptions() {
        try {
            console.log('Loading cat descriptions...');
            const response = await fetch('/resources/cat-descriptions.json');
            if (!response.ok) {
                throw new Error(`Failed to load descriptions: ${response.status} ${response.statusText}`);
            }
            this.descriptions = await response.json();
            console.log('✓ Cat descriptions loaded successfully');
        } catch (error) {
            console.error('Error loading cat descriptions:', error);
            // Initialize with empty object so methods don't fail
            this.descriptions = { gallery: {}, studs: {}, queens: {}, kittens: {} };
        }
    }
    
    /**
     * Get description for a cat image
     * @param {string} category - Category of the cat (gallery, studs, queens, kittens)
     * @param {string} filename - Filename of the image
     * @returns {string} - Description of the cat or empty string if not found
     */
    getCatDescription(category, filename) {
        // Extract just the filename if a full path is provided
        const baseFilename = filename.split('/').pop();
        
        // Extract just the cat name from the filename (everything before the first dash)
        const catName = baseFilename.split('-')[0];
        
        if (this.descriptions && 
            this.descriptions[category] && 
            this.descriptions[category][catName]) {
            return this.descriptions[category][catName].description;
        }
        
        // Return a generic description if none found
        return `This Maine Coon ${category === 'kittens' ? 'kitten' : 'cat'} is part of our ${category} family.`;
    }
}

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('CatImageLoader: Initializing...');
    const catImageLoader = new CatImageLoader();
    
    // Function to initialize a carousel with retry
    const initCarousel = async (selector, category) => {
        console.log(`Initializing carousel: ${selector} with category: ${category}`);
        try {
            await catImageLoader.populateGalleryCarousel(selector, category);
            console.log(`Successfully initialized carousel: ${selector}`);
        } catch (error) {
            console.error(`Failed to initialize carousel ${selector}:`, error);
            // Retry once after a delay
            setTimeout(async () => {
                console.log(`Retrying initialization of carousel: ${selector}`);
                try {
                    await catImageLoader.populateGalleryCarousel(selector, category);
                    console.log(`Successfully initialized carousel on retry: ${selector}`);
                } catch (retryError) {
                    console.error(`Failed to initialize carousel ${selector} even after retry:`, retryError);
                }
            }, 1000);
        }
    };
    
    // Populate carousels one by one
    setTimeout(() => initCarousel('.carousel#gallery-carousel', 'gallery'), 0);
    setTimeout(() => catImageLoader.populateCatCarousel('.cat-carousel#studs-carousel', 'studs'), 500);
    setTimeout(() => catImageLoader.populateCatCarousel('.cat-carousel#queens-carousel', 'queens'), 1000);
    setTimeout(() => catImageLoader.populateCatCarousel('.cat-carousel#kittens-carousel', 'kittens'), 1500);
    
    // Add event listeners for sort and filter buttons
    document.querySelectorAll('.sort-button').forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            const orderBy = this.dataset.orderBy;
            const descending = this.dataset.descending === 'true';
            const carouselId = this.dataset.target;
            
            // Update active state on buttons
            const buttonGroup = this.closest('.sort-buttons');
            if (buttonGroup) {
                buttonGroup.querySelectorAll('.sort-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                this.classList.add('active');
            }
            
            // Repopulate the carousel
            catImageLoader.populateCatCarousel(`#${carouselId}`, category, orderBy, descending);
        });
    });
    
    console.log('CatImageLoader: All carousel initializations queued');
});

// Export for use in other scripts
if (typeof module !== 'undefined') {
    module.exports = { CatImageLoader };
} 