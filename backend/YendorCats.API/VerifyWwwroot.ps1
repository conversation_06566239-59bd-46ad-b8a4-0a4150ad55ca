# Script to verify that frontend files have been copied to wwwroot correctly
Write-Host "Checking if frontend files were copied to wwwroot..."

# Check if wwwroot exists
if (-not (Test-Path -Path "wwwroot")) {
    Write-Host "ERROR: wwwroot directory does not exist!" -ForegroundColor Red
    exit 1
}

# Check if index.html exists
if (-not (Test-Path -Path "wwwroot/index.html")) {
    Write-Host "ERROR: index.html was not copied to wwwroot!" -ForegroundColor Red
    exit 1
}

# Check if CSS directory exists
if (-not (Test-Path -Path "wwwroot/css")) {
    Write-Host "ERROR: CSS directory was not copied to wwwroot!" -ForegroundColor Red
    exit 1
}

# Check if JS directory exists
if (-not (Test-Path -Path "wwwroot/js")) {
    Write-Host "ERROR: JS directory was not copied to wwwroot!" -ForegroundColor Red
    exit 1
}

Write-Host "SUCCESS: Frontend files were copied to wwwroot successfully!" -ForegroundColor Green
exit 0 