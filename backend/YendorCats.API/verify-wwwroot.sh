#!/bin/bash

# Script to verify that frontend files have been copied to wwwroot correctly
echo "Checking if frontend files were copied to wwwroot..."

# Check if wwwroot exists
if [ ! -d "wwwroot" ]; then
    echo "ERROR: wwwroot directory does not exist!"
    exit 1
fi

# Check if index.html exists
if [ ! -f "wwwroot/index.html" ]; then
    echo "ERROR: index.html was not copied to wwwroot!"
    exit 1
fi

# Check if CSS directory exists
if [ ! -d "wwwroot/css" ]; then
    echo "ERROR: CSS directory was not copied to wwwroot!"
    exit 1
fi

# Check if JS directory exists
if [ ! -d "wwwroot/js" ]; then
    echo "ERROR: JS directory was not copied to wwwroot!"
    exit 1
fi

echo "SUCCESS: Frontend files were copied to wwwroot successfully!"
exit 0 