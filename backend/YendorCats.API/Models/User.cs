using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Represents a user in the system
    /// </summary>
    public class User
    {
        /// <summary>
        /// The unique identifier for the user
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// The username used for authentication
        /// </summary>
        [Required]
        [StringLength(50, MinimumLength = 3)]
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// The user's email address
        /// </summary>
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;
        
        /// <summary>
        /// The user's first name
        /// </summary>
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;
        
        /// <summary>
        /// The user's last name
        /// </summary>
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;
        
        /// <summary>
        /// The hashed password (never returned in API responses)
        /// </summary>
        [Required]
        [JsonIgnore]
        public string PasswordHash { get; set; } = string.Empty;
        
        /// <summary>
        /// Random salt used for password hashing (never returned in API responses)
        /// </summary>
        [Required]
        [JsonIgnore]
        public string PasswordSalt { get; set; } = string.Empty;
        
        /// <summary>
        /// The user's role (Admin, Editor, Viewer)
        /// </summary>
        [Required]
        public string Role { get; set; } = "Viewer";
        
        /// <summary>
        /// Whether the user account is active
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// The date and time when the user was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// The date and time when the user last logged in
        /// </summary>
        public DateTime? LastLoginAt { get; set; }
        
        /// <summary>
        /// The refresh token for authentication
        /// </summary>
        [JsonIgnore]
        public string? RefreshToken { get; set; }
        
        /// <summary>
        /// The expiry date for the refresh token
        /// </summary>
        [JsonIgnore]
        public DateTime? RefreshTokenExpiryTime { get; set; }
        
        /// <summary>
        /// Gets the full name of the user
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();
    }
} 