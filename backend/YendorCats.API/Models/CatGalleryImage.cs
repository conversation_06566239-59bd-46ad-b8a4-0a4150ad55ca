using System;
using System.Collections.Generic;
using System.IO;
using System.ComponentModel.DataAnnotations.Schema;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Represents a cat image with metadata extracted from S3 metadata or filename.
    /// Filename format: [cat's name]-[age]-[date(DDMMYY)]-[order].jpg
    /// Example: Georgia-2.6-230325-1.jpg
    /// </summary>
    public class CatGalleryImage
    {
        /// <summary>
        /// The unique identifier for the image
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// The cat's name extracted from metadata or filename
        /// </summary>
        public string CatName { get; set; } = string.Empty;

        /// <summary>
        /// The cat's age in years extracted from metadata or filename
        /// </summary>
        public float Age { get; set; }

        /// <summary>
        /// The date the image was taken
        /// </summary>
        public DateTime DateTaken { get; set; }

        /// <summary>
        /// The ordering number for images taken on the same date
        /// </summary>
        public int OrderNumber { get; set; }

        /// <summary>
        /// The URL path to the image
        /// </summary>
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// The category of the cat (e.g., "studs", "queens", "kittens")
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// The description of the cat
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// The color/pattern of the cat
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// The gender of the cat (M/F)
        /// </summary>
        public string? Gender { get; set; }

        /// <summary>
        /// Any special traits or characteristics
        /// </summary>
        public string? Traits { get; set; }

        /// <summary>
        /// The parent/mother of the cat
        /// </summary>
        public string? Mother { get; set; }

        /// <summary>
        /// The parent/father of the cat
        /// </summary>
        public string? Father { get; set; }

        /// <summary>
        /// The bloodline of the cat
        /// </summary>
        public string? Bloodline { get; set; }

        /// <summary>
        /// The breed of the cat
        /// </summary>
        public string? Breed { get; set; }

        /// <summary>
        /// Description of the cat's personality
        /// </summary>
        public string? Personality { get; set; }

        /// <summary>
        /// Tags for filtering and searching
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// The date the image was uploaded
        /// </summary>
        public DateTime DateUploaded { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// The file format/extension
        /// </summary>
        public string FileFormat { get; set; } = string.Empty;

        /// <summary>
        /// The size of the file in bytes
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// The width of the image in pixels
        /// </summary>
        public int? Width { get; set; }

        /// <summary>
        /// The height of the image in pixels
        /// </summary>
        public int? Height { get; set; }

        /// <summary>
        /// Additional metadata as key-value pairs (not stored in database)
        /// </summary>
        [NotMapped]
        public Dictionary<string, string> AdditionalMetadata { get; set; } = new();

        /// <summary>
        /// Parse a filename to extract cat image metadata
        /// </summary>
        /// <param name="filePath">The full path to the image file</param>
        /// <param name="relativePath">The relative URL path to access the image</param>
        /// <param name="category">The category folder the image belongs to</param>
        /// <returns>A CatGalleryImage object with extracted metadata, or null if parsing fails</returns>
        public static CatGalleryImage? FromFilename(string filePath, string relativePath, string category)
        {
            try
            {
                string filename = Path.GetFileNameWithoutExtension(filePath);
                string[] parts = filename.Split('-');

                if (parts.Length < 4)
                {
                    // Not enough parts for our expected format
                    return null;
                }

                string catName = parts[0];

                if (!float.TryParse(parts[1], out float age))
                {
                    // Age parsing failed
                    return null;
                }

                if (!DateTime.TryParseExact(parts[2], "ddMMyy",
                    System.Globalization.CultureInfo.InvariantCulture,
                    System.Globalization.DateTimeStyles.None,
                    out DateTime dateTaken))
                {
                    // Date parsing failed
                    return null;
                }

                if (!int.TryParse(parts[3], out int orderNumber))
                {
                    // Order number parsing failed
                    return null;
                }

                return new CatGalleryImage
                {
                    Id = Guid.NewGuid().ToString(),
                    CatName = catName,
                    Age = age,
                    DateTaken = dateTaken,
                    OrderNumber = orderNumber,
                    ImageUrl = relativePath,
                    Category = category
                };
            }
            catch
            {
                // Any parsing exception
                return null;
            }
        }

        /// <summary>
        /// Create a CatGalleryImage from S3 metadata
        /// </summary>
        /// <param name="metadata">The S3 object metadata</param>
        /// <param name="relativePath">The relative URL path to access the image</param>
        /// <param name="category">The category folder the image belongs to</param>
        /// <param name="fallbackImage">Optional fallback image if metadata parsing fails</param>
        /// <returns>A CatGalleryImage object with metadata from S3</returns>
        public static CatGalleryImage FromS3Metadata(
            Dictionary<string, string> metadata,
            string relativePath,
            string category,
            CatGalleryImage? fallbackImage = null)
        {
            // Create a new image with default values or from fallback
            var image = fallbackImage ?? new CatGalleryImage
            {
                Id = Guid.NewGuid().ToString(),
                ImageUrl = relativePath,
                Category = category,
                DateTaken = DateTime.Now,
                OrderNumber = 1
            };

            // Try to extract standard metadata fields
            if (metadata.TryGetValue("name", out string? name) && !string.IsNullOrEmpty(name))
            {
                image.CatName = name;
            }

            if (metadata.TryGetValue("age", out string? ageStr) &&
                float.TryParse(ageStr, out float age))
            {
                image.Age = age;
            }

            if (metadata.TryGetValue("description", out string? description))
            {
                image.Description = description;
            }

            if (metadata.TryGetValue("color", out string? color) ||
                metadata.TryGetValue("hair_color", out color))
            {
                image.Color = color;
            }

            if (metadata.TryGetValue("gender", out string? gender))
            {
                image.Gender = gender;
            }

            if (metadata.TryGetValue("traits", out string? traits))
            {
                image.Traits = traits;
            }

            if (metadata.TryGetValue("mother", out string? mother))
            {
                image.Mother = mother;
            }

            if (metadata.TryGetValue("father", out string? father))
            {
                image.Father = father;
            }

            if (metadata.TryGetValue("category", out string? metadataCategory) &&
                !string.IsNullOrEmpty(metadataCategory))
            {
                image.Category = metadataCategory;
            }

            // Extract new metadata fields
            if (metadata.TryGetValue("bloodline", out string? bloodline))
            {
                image.Bloodline = bloodline;
            }

            if (metadata.TryGetValue("breed", out string? breed))
            {
                image.Breed = breed;
            }

            if (metadata.TryGetValue("personality", out string? personality))
            {
                image.Personality = personality;
            }

            if (metadata.TryGetValue("tags", out string? tags))
            {
                image.Tags = tags;
            }

            // Parse date fields
            if (metadata.TryGetValue("date_uploaded", out string? dateUploadedStr) &&
                DateTime.TryParse(dateUploadedStr, out DateTime dateUploaded))
            {
                image.DateUploaded = dateUploaded;
            }

            if (metadata.TryGetValue("date_taken", out string? dateTakenStr) &&
                DateTime.TryParse(dateTakenStr, out DateTime dateTaken))
            {
                image.DateTaken = dateTaken;
            }

            // Parse file metadata
            if (metadata.TryGetValue("file_format", out string? fileFormat))
            {
                image.FileFormat = fileFormat;
            }
            else
            {
                // Extract from file extension if not in metadata
                image.FileFormat = Path.GetExtension(relativePath).TrimStart('.').ToLowerInvariant();
            }

            if (metadata.TryGetValue("file_size", out string? fileSizeStr) &&
                long.TryParse(fileSizeStr, out long fileSize))
            {
                image.FileSize = fileSize;
            }

            if (metadata.TryGetValue("width", out string? widthStr) &&
                int.TryParse(widthStr, out int width))
            {
                image.Width = width;
            }

            if (metadata.TryGetValue("height", out string? heightStr) &&
                int.TryParse(heightStr, out int height))
            {
                image.Height = height;
            }

            // Add any additional metadata not covered by specific properties
            foreach (var kvp in metadata)
            {
                string key = kvp.Key.ToLowerInvariant();
                if (key != "name" && key != "age" && key != "description" &&
                    key != "color" && key != "hair_color" && key != "gender" && key != "traits" &&
                    key != "mother" && key != "father" && key != "category" &&
                    key != "bloodline" && key != "breed" && key != "personality" &&
                    key != "tags" && key != "date_uploaded" && key != "date_taken" &&
                    key != "file_format" && key != "file_size" && key != "width" &&
                    key != "height")
                {
                    image.AdditionalMetadata[kvp.Key] = kvp.Value;
                }
            }

            return image;
        }
    }
}