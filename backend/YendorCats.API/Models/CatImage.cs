using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Represents an image associated with a cat
    /// </summary>
    public class CatImage
    {
        /// <summary>
        /// The unique identifier for the image
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// The ID of the cat this image belongs to
        /// </summary>
        public int CatId { get; set; }
        
        /// <summary>
        /// The file name of the image
        /// </summary>
        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;
        
        /// <summary>
        /// The file path where the image is stored
        /// </summary>
        [Required]
        [StringLength(1000)]
        public string FilePath { get; set; } = string.Empty;
        
        /// <summary>
        /// The content type (MIME type) of the image
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ContentType { get; set; } = string.Empty;
        
        /// <summary>
        /// The caption or description for the image
        /// </summary>
        [StringLength(500)]
        public string? Caption { get; set; }
        
        /// <summary>
        /// Indicates if this is the primary/main image for the cat
        /// </summary>
        public bool IsPrimary { get; set; }
        
        /// <summary>
        /// The order/sequence number for display purposes
        /// </summary>
        public int DisplayOrder { get; set; }
        
        /// <summary>
        /// The date the image was uploaded
        /// </summary>
        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// The size of the image file in bytes
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// The width of the image in pixels
        /// </summary>
        public int? Width { get; set; }
        
        /// <summary>
        /// The height of the image in pixels
        /// </summary>
        public int? Height { get; set; }
        
        /// <summary>
        /// Navigation property to the associated cat
        /// </summary>
        [JsonIgnore]
        public Cat? Cat { get; set; }
    }
} 