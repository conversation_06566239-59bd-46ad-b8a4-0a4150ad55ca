public class AppSecrets
{
    // Database
    public string DbConnectionString { get; set; } = string.Empty;

    // JWT
    public string JwtSecret { get; set; } = string.Empty;
    public string JwtIssuer { get; set; } = string.Empty;
    public string JwtAudience { get; set; } = string.Empty;
    public int JwtExpiryMinutes { get; set; } = 60;
    public int RefreshExpiryDays { get; set; } = 7;

    // AWS S3
    public string S3AccessKey { get; set; } = string.Empty;
    public string S3SecretKey { get; set; } = string.Empty;
    public string S3SessionToken { get; set; } = string.Empty; // Optional, for temporary credentials

    // Add other secrets as needed
    public string Api<PERSON>ey { get; set; } = string.Empty;
}
