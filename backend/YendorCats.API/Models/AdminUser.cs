using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Admin user model for authentication
    /// </summary>
    public class AdminUser
    {
        /// <summary>
        /// Unique identifier for the admin user
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Admin username
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Admin email address
        /// </summary>
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Hashed password
        /// </summary>
        [Required]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// Password salt for additional security
        /// </summary>
        [Required]
        public string PasswordSalt { get; set; } = string.Empty;

        /// <summary>
        /// Admin role (SuperAdmin, Admin, Editor)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Role { get; set; } = "Admin";

        /// <summary>
        /// Whether the admin account is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// When the admin account was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Last login timestamp
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// Failed login attempts counter
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// Account lockout until timestamp
        /// </summary>
        public DateTime? LockedUntil { get; set; }
    }

    /// <summary>
    /// Admin login request model
    /// </summary>
    public class AdminLoginRequest
    {
        /// <summary>
        /// Username or email
        /// </summary>
        [Required]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Password
        /// </summary>
        [Required]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Remember me option
        /// </summary>
        public bool RememberMe { get; set; } = false;
    }

    /// <summary>
    /// Admin login response model
    /// </summary>
    public class AdminLoginResponse
    {
        /// <summary>
        /// Whether login was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// JWT token for authentication
        /// </summary>
        public string? Token { get; set; }

        /// <summary>
        /// Refresh token
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// Token expiration time
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Admin user information
        /// </summary>
        public AdminUserInfo? User { get; set; }

        /// <summary>
        /// Error message if login failed
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// Admin user information for responses
    /// </summary>
    public class AdminUserInfo
    {
        /// <summary>
        /// User ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User role
        /// </summary>
        public string Role { get; set; } = string.Empty;

        /// <summary>
        /// Last login time
        /// </summary>
        public DateTime? LastLoginAt { get; set; }
    }

    /// <summary>
    /// Admin roles enumeration
    /// </summary>
    public static class AdminRoles
    {
        public const string SuperAdmin = "SuperAdmin";
        public const string Admin = "Admin";
        public const string Editor = "Editor";
    }
}
