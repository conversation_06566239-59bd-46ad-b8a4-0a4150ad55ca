using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Represents a cat in the system
    /// </summary>
    public class Cat
    {
        /// <summary>
        /// The unique identifier for the cat
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// The name of the cat
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// The breed of the cat
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Breed { get; set; } = string.Empty;
        
        /// <summary>
        /// The date of birth of the cat
        /// </summary>
        [Required]
        public DateTime DateOfBirth { get; set; }
        
        /// <summary>
        /// The gender of the cat (M/F)
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Gender { get; set; } = string.Empty;
        
        /// <summary>
        /// The color of the cat
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Color { get; set; } = string.Empty;
        
        /// <summary>
        /// A description of the cat
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }
        
        /// <summary>
        /// The price of the cat
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal Price { get; set; }
        
        /// <summary>
        /// Whether the cat is available for purchase
        /// </summary>
        public bool IsAvailable { get; set; } = true;
        
        /// <summary>
        /// Whether the cat is active in the system
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// Any special markings or characteristics of the cat
        /// </summary>
        [StringLength(200)]
        public string? Markings { get; set; }
        
        /// <summary>
        /// The date the cat was added to the system
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// The date the cat was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// The collection of images for this cat
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public ICollection<CatImage>? Images { get; set; }
        
        /// <summary>
        /// The parent/mother of this cat (if known)
        /// </summary>
        public int? MotherId { get; set; }
        
        /// <summary>
        /// The parent/father of this cat (if known)
        /// </summary>
        public int? FatherId { get; set; }
        
        /// <summary>
        /// Navigation property for the mother
        /// </summary>
        [JsonIgnore]
        public Cat? Mother { get; set; }
        
        /// <summary>
        /// Navigation property for the father
        /// </summary>
        [JsonIgnore]
        public Cat? Father { get; set; }
        
        /// <summary>
        /// Navigation property for offspring where this cat is the mother
        /// </summary>
        [JsonIgnore]
        public ICollection<Cat>? Offspring { get; set; }
    }
} 