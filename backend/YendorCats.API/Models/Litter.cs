using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Represents a litter of kittens in the Yendor Cats system
    /// </summary>
    public class Litter
    {
        /// <summary>
        /// Unique identifier for the litter
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// Name or identifier for the litter
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Detailed description of the litter
        /// </summary>
        [Max<PERSON>ength(2000)]
        public string? Description { get; set; }
        
        /// <summary>
        /// Date the kittens were born
        /// </summary>
        [Required]
        public DateTime BirthDate { get; set; }
        
        /// <summary>
        /// ID of the father cat
        /// </summary>
        public int FatherId { get; set; }
        
        /// <summary>
        /// Navigation property to the father
        /// </summary>
        public Cat? Father { get; set; }
        
        /// <summary>
        /// ID of the mother cat
        /// </summary>
        public int MotherId { get; set; }
        
        /// <summary>
        /// Navigation property to the mother
        /// </summary>
        public Cat? Mother { get; set; }
        
        /// <summary>
        /// Date the kittens will be available for new homes
        /// </summary>
        public DateTime? AvailableDate { get; set; }
        
        /// <summary>
        /// Collection of kittens in this litter
        /// </summary>
        public ICollection<Cat> Kittens { get; set; } = new List<Cat>();
        
        /// <summary>
        /// Number of kittens in the litter
        /// </summary>
        public int LitterSize { get; set; }
        
        /// <summary>
        /// Additional notes about the litter
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }
        
        /// <summary>
        /// Whether this litter is featured on the website
        /// </summary>
        public bool IsFeatured { get; set; }
        
        /// <summary>
        /// Current status of the litter (Announced, Born, Available, etc.)
        /// </summary>
        [MaxLength(50)]
        public string Status { get; set; } = "Announced";
        
        /// <summary>
        /// Date this record was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Date this record was last updated
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }
} 