using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Represents a cat breed in the Yendor Cats system
    /// </summary>
    public class Breed
    {
        /// <summary>
        /// Unique identifier for the breed
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// Name of the breed
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Detailed description of the breed
        /// </summary>
        [MaxLength(2000)]
        public string? Description { get; set; }
        
        /// <summary>
        /// Origin country of the breed
        /// </summary>
        [MaxLength(100)]
        public string? Origin { get; set; }
        
        /// <summary>
        /// Average life expectancy range
        /// </summary>
        [MaxLength(50)]
        public string? LifeExpectancy { get; set; }
        
        /// <summary>
        /// Temperament characteristics
        /// </summary>
        [MaxLength(500)]
        public string? Temperament { get; set; }
        
        /// <summary>
        /// Collection of cats belonging to this breed
        /// </summary>
        public ICollection<Cat> Cats { get; set; } = new List<Cat>();
    }
} 