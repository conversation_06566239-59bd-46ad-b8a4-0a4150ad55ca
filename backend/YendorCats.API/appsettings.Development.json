{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=YendorCats;User=root;Password=password;Port=3306;"}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AWS": {"Region": "us-west-004", "UseCredentialsFromSecrets": false, "S3": {"BucketName": "yendorcats-images", "UseDirectS3Urls": true, "ServiceUrl": "https://s3.us-west-004.backblazeb2.com", "PublicUrl": "https://f004.backblazeb2.com/file/yendorcats-images/{key}", "UseCdn": false, "CdnDomain": "", "AccessKey": "your-backblaze-b2-application-key-id", "SecretKey": "your-backblaze-b2-application-key"}}}