2025-07-09 00:41:44.993 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:41:44.999 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:41:45.001 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 00:41:45.495 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:41:45.495 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:41:45.495 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:41:45.983 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:41:45.983 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:41:45.984 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:41:46.187 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:41:46.394 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:41:46.413 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:41:46.413 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:41:46.484 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:41:46.484 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:41:46.485 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:41:46.616 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:41:46.616 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:41:46.636 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:41:46.636 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:41:46.866 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:41:46.866 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:41:46.868 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:41:47.089 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:41:47.089 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:41:47.091 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:41:47.091 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:41:47.309 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:41:47.314 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:41:47.315 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:41:47.385 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:41:47.385 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:41:47.543 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:41:47.543 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:41:47.607 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:41:47.607 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:41:47.767 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:41:47.767 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:41:47.829 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:41:47.830 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:41:47.990 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:41:47.990 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:41:48.054 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:41:48.054 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:41:48.214 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:41:48.280 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:41:48.280 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:41:48.510 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:41:48.510 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:41:48.738 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:41:48.738 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:41:48.964 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:41:48.965 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:41:49.193 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:41:49.194 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:41:49.422 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:41:49.423 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:41:49.647 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:41:49.647 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:41:49.882 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:41:49.883 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:41:50.209 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:41:50.210 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:41:50.522 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:41:50.522 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:41:50.848 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:41:50.848 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:41:51.076 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:41:51.076 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:41:51.299 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:41:51.300 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:41:51.527 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:41:51.528 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:41:51.751 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:41:51.751 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:41:51.975 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:48:43.525 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:48:43.525 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:48:43.526 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:48:44.542 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:48:44.768 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:48:53.459 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:48:53.459 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:48:53.459 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:48:54.560 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:48:54.784 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:48:54.784 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:48:55.011 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:48:55.011 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:48:55.235 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:48:55.235 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:48:55.459 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:48:55.459 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:48:55.685 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:48:55.685 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:48:55.909 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:48:55.910 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:48:56.134 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:48:56.134 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:48:56.358 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:48:56.358 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:48:56.584 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:48:56.584 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:48:56.808 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:48:56.809 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:48:57.035 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:48:57.035 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:48:57.261 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:48:57.261 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:48:57.485 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:48:57.485 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:48:57.709 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:48:57.709 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:48:57.936 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:48:57.936 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:48:58.160 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:48:58.160 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:48:58.386 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:48:58.387 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:48:58.612 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:48:58.612 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:48:58.838 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:48:58.843 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:48:59.067 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:48:59.067 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:48:59.291 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:48:59.291 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:48:59.517 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:48:59.517 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:48:59.741 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:49:47.363 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:49:47.364 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:49:47.364 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:49:47.862 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:49:47.862 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:49:47.863 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:49:48.333 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:49:48.362 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:49:48.362 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:49:48.362 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:49:48.562 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:49:48.764 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:48.764 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:49:48.783 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:49:48.987 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:49:48.987 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:49:48.997 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:49:48.997 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:49:48.997 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 00:49:49.003 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:49.003 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:49:49.208 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:49.221 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:49:49.222 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:49:49.428 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:49:49.428 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:49:49.441 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:49:49.442 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:49:49.500 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:49:49.500 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:49:49.501 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:49:49.670 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:49:49.671 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:49:49.672 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:49:49.672 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:49:49.726 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:49:49.894 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:49:49.894 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:49:49.895 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:49:49.895 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:49:49.948 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:49.949 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:49:49.997 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:49:49.997 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:49:49.997 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:49:50.117 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:49:50.117 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:49:50.117 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:49:50.117 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:49:50.170 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:50.171 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:49:50.291 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:49:50.339 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:49:50.339 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:49:50.340 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:49:50.341 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:49:50.393 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:49:50.394 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:49:50.497 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:49:50.497 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:49:50.498 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:49:50.517 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:49:50.517 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:49:50.561 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:50.561 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:49:50.562 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:49:50.617 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:49:50.618 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:49:50.741 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:49:50.779 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:49:50.779 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:49:50.783 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:49:50.783 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:49:50.839 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:49:50.839 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:49:50.967 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:50.999 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:49:51.000 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:49:51.005 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:49:51.005 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:49:51.061 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:49:51.062 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:49:51.219 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:49:51.220 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:49:51.227 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:49:51.228 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:49:51.286 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:49:51.286 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:49:51.439 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:49:51.440 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:49:51.447 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:49:51.448 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:49:51.507 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:51.507 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:49:51.660 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:49:51.660 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:49:51.669 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:49:51.670 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:49:51.728 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:49:51.728 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:49:51.880 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:49:51.880 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:49:51.890 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:49:51.890 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:49:51.950 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:49:51.951 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:49:52.101 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:49:52.112 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:49:52.112 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:49:52.174 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:49:52.174 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:49:52.336 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:52.337 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:49:52.399 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:49:52.399 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:49:52.557 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:49:52.558 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:49:52.621 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:49:52.621 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:49:52.779 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:49:52.779 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:49:52.843 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:49:52.843 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:49:53.002 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:49:53.002 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:49:53.067 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:49:53.067 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:49:53.225 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:49:53.225 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:49:53.292 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:49:53.292 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:49:53.449 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:49:53.449 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:49:53.512 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:49:53.513 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:49:53.671 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:49:53.672 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:49:53.735 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:49:53.735 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:49:53.971 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:49:54.060 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:49:54.061 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:49:54.374 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:49:54.374 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:49:54.687 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:49:54.687 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:49:54.993 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:49:54.993 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:49:55.235 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:50:13.716 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:50:13.716 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:50:13.717 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 00:50:14.216 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:50:14.216 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:50:14.217 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:50:14.449 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:50:14.672 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:50:14.672 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:50:14.716 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:50:14.716 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:50:14.717 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:50:14.897 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:50:14.897 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:50:15.128 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:50:15.216 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:50:15.216 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:50:15.216 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:50:15.354 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:50:15.354 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:50:15.579 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:50:15.580 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:50:15.624 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:50:15.802 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:50:15.802 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:50:15.846 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:50:15.846 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:50:16.024 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:50:16.069 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:50:16.069 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:50:16.123 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:50:16.123 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:50:16.295 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:50:16.296 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:50:16.349 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:50:16.349 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:50:16.519 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:50:16.520 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:50:16.575 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:50:16.576 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:50:16.743 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:50:16.744 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:50:16.801 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:50:16.802 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:50:16.968 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:50:16.969 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:50:17.030 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:50:17.030 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:50:17.194 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:50:17.194 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:50:17.255 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:50:17.418 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:50:17.418 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:50:17.642 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:50:17.643 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:50:17.867 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:50:17.867 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:50:18.091 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:50:18.092 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:50:18.316 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:50:18.316 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:50:18.540 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:50:18.540 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:50:18.789 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:50:18.789 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:50:19.115 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:50:19.115 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:50:19.436 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:50:19.436 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:50:19.762 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:50:19.762 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:50:20.089 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:50:20.090 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:50:20.316 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:50:20.316 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:50:20.560 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:51:45.556 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:51:45.556 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:51:45.557 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:51:46.055 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:51:46.055 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:51:46.056 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:51:46.556 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:51:46.557 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:51:46.557 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:51:46.640 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:51:46.914 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:51:47.152 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:51:47.182 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:51:47.182 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:51:47.413 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:51:47.414 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:51:47.445 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:51:47.645 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:51:47.645 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:51:47.675 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:51:47.675 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:51:47.918 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:51:47.918 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:51:47.941 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:51:47.941 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:51:48.201 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:51:48.201 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:51:48.219 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:51:48.219 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:51:48.475 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:51:48.475 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:51:48.487 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:51:48.488 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:51:48.748 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:51:48.748 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:51:48.753 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:51:48.753 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:51:49.022 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:51:49.022 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:51:49.024 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:51:49.024 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:51:49.288 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:51:49.294 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:51:49.294 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:51:49.561 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:51:49.561 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:51:49.831 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:51:49.831 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:51:50.105 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:51:50.105 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:51:50.378 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:51:50.378 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:51:50.683 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:51:50.684 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:51:50.966 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:51:50.966 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:51:51.244 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:51:51.245 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:51:51.527 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:51:51.527 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:51:51.811 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:51:51.811 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:51:52.091 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:51:52.092 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:51:52.368 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:51:52.368 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:51:52.640 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:51:52.640 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:51:52.912 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:51:52.912 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:51:53.181 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:51:53.181 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:51:53.451 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:53:55.984 +10:00 [INF] Starting Yendor Cats API
2025-07-09 00:54:05.618 +10:00 [WRN] Failed to determine the https port for redirect.
2025-07-09 00:54:05.695 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-09 00:54:05.702 +10:00 [ERR] Failed to get JWT settings from HashiCorp Vault, using values from appsettings
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__5(JwtBearerOptions options) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 226
2025-07-09 00:54:05.848 +10:00 [INF] Using S3 credentials from appsettings configuration.
2025-07-09 00:54:05.872 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:54:05.873 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:54:05.889 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 00:54:06.308 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:54:06.309 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:54:06.309 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:54:06.809 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:54:06.809 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:54:06.810 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:54:07.236 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:54:07.250 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:54:07.308 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:54:07.308 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:54:07.309 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:54:07.464 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:07.466 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:54:07.474 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:54:07.474 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:54:07.691 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:54:07.700 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:07.701 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:54:07.726 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:54:07.727 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:54:07.924 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:07.932 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:54:07.932 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:54:07.964 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:54:07.964 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:54:08.167 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:54:08.167 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:54:08.203 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:54:08.203 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:54:08.401 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:54:08.401 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:54:08.441 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:54:08.441 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:54:08.627 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:54:08.628 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:54:08.671 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:54:08.671 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:54:08.851 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:54:08.852 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:54:08.897 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:54:08.897 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:54:09.073 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:09.073 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:54:09.132 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:54:09.296 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:54:09.296 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:54:09.518 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:54:09.518 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:54:09.740 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:54:09.740 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:54:09.965 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:54:09.965 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:54:10.191 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:54:10.191 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:54:10.415 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:54:10.415 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:54:10.642 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:54:10.642 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:54:10.877 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:10.878 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:54:11.216 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:54:11.216 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:54:11.516 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:54:11.516 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:54:11.849 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:54:11.850 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:54:12.078 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:54:12.078 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:54:12.304 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:54:12.304 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:54:12.544 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:54:12.544 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:54:12.771 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:54:25.560 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:54:25.561 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:54:25.561 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 00:54:26.060 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:54:26.060 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:54:26.060 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:54:26.561 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:54:26.561 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:54:26.562 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:54:26.728 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:54:27.025 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:27.026 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:54:27.026 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:54:27.059 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:54:27.059 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:54:27.059 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:54:27.331 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:27.331 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:54:27.331 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:54:27.331 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:54:27.555 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:54:27.558 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:54:27.558 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:54:27.774 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:54:27.774 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:54:27.782 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:27.996 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:54:27.996 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:54:28.007 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:54:28.007 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:54:28.225 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:54:28.225 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:54:28.301 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:54:28.301 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:54:28.447 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:54:28.447 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:54:28.531 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:54:28.532 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:54:28.672 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:54:28.673 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:54:28.757 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:54:28.757 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:54:28.895 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:28.896 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:54:28.983 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:54:28.983 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:54:29.121 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:54:29.121 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:54:29.208 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:54:29.345 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:54:29.345 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:54:29.566 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:54:29.567 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:54:29.790 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:54:29.790 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:54:30.010 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:54:30.010 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:54:30.233 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:54:30.233 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:54:30.463 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:54:30.463 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:54:30.696 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:54:30.697 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:54:31.020 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:54:31.020 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:54:31.328 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:54:31.329 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:54:32.071 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:54:32.071 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:54:32.312 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:54:32.312 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:54:32.534 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:54:32.534 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:54:32.759 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:54:32.759 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:54:33.055 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:55:00.246 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:00.246 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:00.247 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 00:55:00.746 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:00.746 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:00.746 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:55:00.996 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:55:01.221 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:01.222 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:55:01.246 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:01.247 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:01.247 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:55:01.451 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:01.451 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:55:01.693 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:55:01.744 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:01.745 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:01.745 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:55:01.925 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:55:01.925 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:55:02.150 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:55:02.150 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:55:02.191 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:55:02.377 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:55:02.377 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:55:02.417 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:55:02.417 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:55:02.602 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:02.641 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:55:02.642 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:55:02.716 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:55:02.716 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:55:02.868 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:55:02.868 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:55:02.939 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:55:02.939 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:55:03.095 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:55:03.096 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:55:03.160 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:55:03.160 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:55:03.322 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:55:03.323 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:55:03.385 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:03.385 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:55:03.552 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:55:03.552 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:55:03.611 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:55:03.611 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:55:03.779 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:55:03.837 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:55:03.838 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:55:04.064 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:55:04.064 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:55:04.287 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:55:04.287 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:55:04.511 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:55:04.512 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:55:04.735 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:55:04.735 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:55:04.963 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:55:04.963 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:55:05.188 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:05.188 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:55:05.512 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:55:05.512 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:55:05.837 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:55:05.837 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:55:07.048 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:55:07.049 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:55:07.272 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:55:07.273 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:55:07.501 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:55:07.502 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:55:07.726 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:55:07.726 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:55:07.950 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:55:28.748 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:28.749 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:28.749 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 00:55:29.251 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:29.251 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:29.252 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:55:29.750 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:29.750 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:29.750 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:55:30.049 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:55:30.250 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:30.250 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:30.250 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:55:30.270 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:55:30.298 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:30.298 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:55:30.519 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:55:30.548 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:55:30.549 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:55:30.581 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:30.582 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:55:30.772 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:30.791 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:55:30.791 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:55:30.805 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:55:30.805 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:55:31.002 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:55:31.002 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:55:31.064 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:55:31.064 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:55:31.214 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:55:31.215 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:55:31.320 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:55:31.320 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:55:31.422 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:55:31.422 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:55:31.572 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:55:31.573 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:55:31.626 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:55:31.627 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:55:31.828 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:55:31.829 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:55:31.830 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:31.831 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:55:32.029 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:55:32.029 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:55:32.079 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:55:32.079 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:55:32.223 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:55:32.223 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:55:32.319 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:55:32.412 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:55:32.412 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:55:32.595 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:55:32.595 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:55:32.775 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:55:32.776 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:55:32.961 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:55:32.961 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:55:33.162 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:55:33.162 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:55:33.364 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:33.364 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:55:33.568 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:55:33.568 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:55:33.805 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:55:33.806 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:55:34.105 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:55:34.106 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:55:34.398 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:55:34.399 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:55:35.019 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:55:35.019 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:55:35.241 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:55:35.241 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:55:35.445 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 00:55:36.202 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:36.202 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:36.202 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 00:55:36.705 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:36.706 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:36.707 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 00:55:36.942 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 00:55:37.158 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:37.158 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 00:55:37.202 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:37.202 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:37.203 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 00:55:37.372 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:37.372 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 00:55:37.583 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 00:55:37.702 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 00:55:37.702 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 00:55:37.703 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 00:55:37.796 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 00:55:37.797 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 00:55:38.015 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 00:55:38.015 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 00:55:38.229 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 00:55:38.245 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 00:55:38.245 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 00:55:38.459 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 00:55:38.459 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 00:55:38.475 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:38.625 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 00:55:38.626 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 00:55:38.695 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 00:55:38.695 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 00:55:38.861 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 00:55:38.861 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 00:55:38.937 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 00:55:38.937 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 00:55:39.098 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 00:55:39.098 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 00:55:39.177 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 00:55:39.177 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 00:55:39.334 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:39.334 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 00:55:39.416 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 00:55:39.417 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 00:55:39.568 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 00:55:39.568 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 00:55:39.654 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 00:55:39.655 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 00:55:39.800 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 00:55:39.801 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 00:55:39.888 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 00:55:40.028 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 00:55:40.029 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 00:55:40.259 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 00:55:40.259 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 00:55:40.489 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 00:55:40.490 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 00:55:40.729 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 00:55:40.730 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 00:55:40.969 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 00:55:40.969 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 00:55:41.210 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 00:55:41.210 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 00:55:41.452 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 00:55:41.453 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 00:55:41.704 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 00:55:41.704 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 00:55:41.973 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 00:55:41.974 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 00:55:42.315 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 00:55:42.315 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 00:55:42.641 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 00:55:42.641 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 00:55:42.970 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 00:55:42.971 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 00:55:43.257 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 01:02:49.635 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:02:49.635 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:02:49.636 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 01:02:50.121 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:02:50.122 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:02:50.123 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 01:02:50.621 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:02:50.622 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:02:50.622 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 01:02:50.810 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 01:02:51.115 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 01:02:51.115 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 01:02:51.116 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 01:02:51.121 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:02:51.121 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:02:51.122 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 01:02:51.353 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 01:02:51.353 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 01:02:51.354 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 01:02:51.354 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 01:02:51.592 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 01:02:51.592 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 01:02:51.592 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 01:02:51.826 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 01:02:51.826 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 01:02:51.826 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 01:02:52.056 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 01:02:52.056 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 01:02:52.057 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 01:02:52.057 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 01:02:52.284 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 01:02:52.285 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 01:02:52.286 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 01:02:52.286 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 01:02:52.514 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 01:02:52.515 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 01:02:52.516 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 01:02:52.517 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 01:02:52.738 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 01:02:52.739 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 01:02:52.741 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 01:02:52.741 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 01:02:52.961 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 01:02:52.961 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 01:02:52.962 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 01:02:52.962 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 01:02:53.183 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 01:02:53.185 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 01:02:53.185 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 01:02:53.409 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 01:02:53.409 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 01:02:53.641 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 01:02:53.641 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 01:02:53.873 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 01:02:53.874 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 01:02:54.100 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 01:02:54.100 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 01:02:54.326 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 01:02:54.326 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 01:02:54.554 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 01:02:54.554 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 01:02:54.784 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 01:02:54.784 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 01:02:55.032 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 01:02:55.032 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 01:02:55.362 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 01:02:55.363 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 01:02:55.663 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 01:02:55.663 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 01:02:55.987 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 01:02:55.988 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 01:02:56.276 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 01:02:56.276 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 01:02:56.531 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 01:02:56.531 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 01:02:56.773 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 01:05:24.861 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:24.862 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:24.863 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 01:05:25.334 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:25.334 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:25.335 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 01:05:25.833 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:25.833 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:25.833 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 01:05:26.295 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 01:05:26.334 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:26.334 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:26.335 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 01:05:26.477 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 01:05:26.572 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:26.572 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 01:05:26.755 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 01:05:26.848 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 01:05:26.848 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 01:05:26.938 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:26.938 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 01:05:27.027 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:27.119 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 01:05:27.119 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 01:05:27.205 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 01:05:27.205 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 01:05:27.393 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 01:05:27.393 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 01:05:27.475 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 01:05:27.475 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 01:05:27.671 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 01:05:27.672 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 01:05:27.752 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 01:05:27.752 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 01:05:27.950 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 01:05:27.950 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 01:05:28.028 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 01:05:28.028 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 01:05:28.224 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 01:05:28.224 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 01:05:28.300 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 01:05:28.300 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 01:05:28.500 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 01:05:28.500 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 01:05:28.576 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:28.577 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 01:05:28.777 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 01:05:28.852 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 01:05:28.853 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 01:05:29.127 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 01:05:29.127 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 01:05:29.397 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 01:05:29.397 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 01:05:29.670 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 01:05:29.670 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 01:05:29.944 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 01:05:29.945 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 01:05:30.213 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 01:05:30.214 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 01:05:30.482 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 01:05:30.483 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 01:05:30.752 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:30.752 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 01:05:31.599 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 01:05:31.599 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 01:05:31.949 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 01:05:31.949 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 01:05:32.235 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 01:05:32.235 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 01:05:32.530 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 01:05:32.530 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 01:05:32.809 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 01:05:32.809 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 01:05:33.054 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 01:05:33.055 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 01:05:33.316 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 01:05:39.234 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:39.235 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:39.235 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 01:05:39.718 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:39.718 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:39.718 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 01:05:40.218 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:40.218 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:40.218 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 01:05:40.649 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 01:05:40.719 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:40.719 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:40.720 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 01:05:40.810 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 01:05:40.916 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:40.916 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 01:05:41.075 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 01:05:41.177 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 01:05:41.177 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 01:05:41.285 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:41.285 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 01:05:41.339 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:41.435 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 01:05:41.435 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 01:05:41.543 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 01:05:41.544 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 01:05:41.690 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 01:05:41.691 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 01:05:41.801 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 01:05:41.801 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 01:05:41.950 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 01:05:41.950 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 01:05:42.062 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 01:05:42.063 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 01:05:42.212 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 01:05:42.212 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 01:05:42.330 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 01:05:42.330 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 01:05:42.474 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 01:05:42.475 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 01:05:42.596 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 01:05:42.597 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 01:05:42.737 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 01:05:42.737 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 01:05:42.857 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:42.857 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 01:05:42.999 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 01:05:43.119 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 01:05:43.119 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 01:05:43.381 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 01:05:43.381 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 01:05:43.641 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 01:05:43.641 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 01:05:43.906 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 01:05:43.906 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 01:05:44.181 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 01:05:44.182 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 01:05:44.455 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 01:05:44.455 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 01:05:44.702 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 01:05:44.702 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 01:05:45.022 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:45.022 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 01:05:45.378 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 01:05:45.378 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 01:05:45.763 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 01:05:45.764 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 01:05:46.120 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 01:05:46.120 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 01:05:46.389 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 01:05:46.390 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 01:05:46.654 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 01:05:46.654 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 01:05:46.922 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 01:05:46.922 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 01:05:47.244 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 01:05:56.386 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:56.386 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:56.387 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 01:05:56.867 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:56.867 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:56.868 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 01:05:57.368 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:57.368 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:57.369 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 01:05:57.812 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 01:05:57.868 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:05:57.869 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:05:57.869 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 01:05:58.003 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 01:05:58.090 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:58.090 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 01:05:58.288 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 01:05:58.374 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 01:05:58.374 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 01:05:58.502 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:58.503 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 01:05:58.580 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 01:05:58.667 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 01:05:58.667 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 01:05:58.797 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 01:05:58.797 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 01:05:58.958 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 01:05:58.959 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 01:05:59.091 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 01:05:59.091 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 01:05:59.247 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 01:05:59.247 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 01:05:59.369 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 01:05:59.370 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 01:05:59.501 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 01:05:59.502 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 01:05:59.628 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 01:05:59.628 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 01:05:59.765 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 01:05:59.766 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 01:05:59.895 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 01:05:59.895 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 01:06:00.032 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 01:06:00.032 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 01:06:00.164 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 01:06:00.164 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 01:06:00.308 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 01:06:00.443 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 01:06:00.443 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 01:06:00.729 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 01:06:00.729 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 01:06:01.019 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 01:06:01.019 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 01:06:01.312 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 01:06:01.313 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 01:06:01.604 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 01:06:01.605 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 01:06:01.864 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 01:06:01.864 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 01:06:02.127 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 01:06:02.127 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 01:06:02.435 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 01:06:02.435 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 01:06:03.297 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 01:06:03.297 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 01:06:03.636 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 01:06:03.636 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 01:06:03.881 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 01:06:03.881 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 01:06:04.142 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 01:06:04.142 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 01:06:04.446 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 01:06:04.446 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 01:06:04.735 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 01:06:04.735 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 01:06:05.060 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 01:06:44.716 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:06:44.716 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:06:44.716 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 01:06:45.192 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:06:45.192 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:06:45.193 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 01:06:45.690 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:06:45.690 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:06:45.690 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 01:06:46.160 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 01:06:46.191 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:06:46.192 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:06:46.192 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 01:06:46.306 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 01:06:46.442 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 01:06:46.442 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 01:06:46.584 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 01:06:46.724 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 01:06:46.724 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 01:06:46.864 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 01:06:46.868 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 01:06:46.868 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 01:06:47.005 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 01:06:47.005 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 01:06:47.157 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 01:06:47.157 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 01:06:47.295 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 01:06:47.295 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 01:06:47.453 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 01:06:47.453 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 01:06:47.592 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 01:06:47.593 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 01:06:47.739 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 01:06:47.739 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 01:06:47.873 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 01:06:47.873 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 01:06:48.001 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 01:06:48.001 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 01:06:48.148 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 01:06:48.148 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 01:06:48.276 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 01:06:48.276 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 01:06:48.432 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 01:06:48.432 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 01:06:48.562 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 01:06:48.562 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 01:06:48.720 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 01:06:48.847 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 01:06:48.847 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 01:06:49.131 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 01:06:49.132 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 01:06:49.411 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 01:06:49.412 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 01:06:49.695 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 01:06:49.696 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 01:06:49.984 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 01:06:49.985 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 01:06:50.276 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 01:06:50.276 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 01:06:50.560 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 01:06:50.560 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 01:06:50.852 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 01:06:50.852 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 01:06:51.237 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 01:06:51.237 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 01:06:51.591 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 01:06:51.591 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 01:06:51.983 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 01:06:51.983 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 01:06:52.270 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 01:06:52.270 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 01:06:52.557 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 01:06:52.557 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 01:06:52.836 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 01:06:52.836 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 01:06:53.083 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 01:07:40.732 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:07:40.732 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:07:40.734 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 01:07:41.211 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:07:41.212 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:07:41.212 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 01:07:41.711 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:07:41.712 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:07:41.712 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 01:07:42.211 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:07:42.211 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:07:42.211 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 01:07:42.252 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 01:07:42.333 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 01:07:42.512 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 01:07:42.594 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 01:07:42.594 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 01:07:42.778 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 01:07:42.779 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 01:07:42.782 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 01:07:42.865 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 01:07:42.865 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 01:07:43.049 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 01:07:43.051 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 01:07:43.133 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 01:07:43.134 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 01:07:43.315 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 01:07:43.315 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 01:07:43.403 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 01:07:43.403 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 01:07:43.587 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 01:07:43.587 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 01:07:43.677 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 01:07:43.677 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 01:07:43.861 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 01:07:43.861 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 01:07:43.954 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 01:07:43.954 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 01:07:44.134 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 01:07:44.134 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 01:07:44.228 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 01:07:44.228 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 01:07:44.404 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 01:07:44.404 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 01:07:44.500 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 01:07:44.500 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 01:07:44.678 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 01:07:44.772 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 01:07:44.772 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 01:07:45.047 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 01:07:45.047 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 01:07:45.328 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 01:07:45.328 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 01:07:45.605 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 01:07:45.605 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 01:07:45.887 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 01:07:45.888 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 01:07:46.224 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 01:07:46.224 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 01:07:46.460 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 01:07:46.460 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 01:07:46.729 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 01:07:46.729 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 01:07:47.042 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 01:07:47.042 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 01:07:47.418 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 01:07:47.418 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 01:07:47.782 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 01:07:47.783 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 01:07:48.046 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 01:07:48.046 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 01:07:48.339 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 01:07:48.339 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 01:07:48.606 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 01:07:48.606 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 01:07:48.894 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 01:10:25.834 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:10:25.835 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:10:25.835 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 01:10:26.327 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:10:26.327 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:10:26.327 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 01:10:26.827 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:10:26.827 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:10:26.827 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 01:10:27.250 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 01:10:27.329 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:10:27.329 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:10:27.330 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 01:10:27.418 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 01:10:27.475 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 01:10:27.475 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 01:10:27.641 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 01:10:27.743 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 01:10:27.743 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 01:10:27.828 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 01:10:27.828 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 01:10:27.879 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 01:10:27.990 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 01:10:27.990 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 01:10:28.086 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 01:10:28.086 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 01:10:28.250 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 01:10:28.250 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 01:10:28.348 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 01:10:28.348 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 01:10:28.517 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 01:10:28.517 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 01:10:28.614 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 01:10:28.614 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 01:10:28.786 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 01:10:28.787 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 01:10:28.883 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 01:10:28.883 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 01:10:29.057 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 01:10:29.057 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 01:10:29.153 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 01:10:29.153 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 01:10:29.329 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 01:10:29.330 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 01:10:29.425 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 01:10:29.425 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 01:10:29.605 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 01:10:29.702 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 01:10:29.702 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 01:10:29.978 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 01:10:29.978 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 01:10:30.256 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 01:10:30.256 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 01:10:30.536 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 01:10:30.536 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 01:10:30.816 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 01:10:30.817 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 01:10:31.103 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 01:10:31.103 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 01:10:31.384 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 01:10:31.384 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 01:10:31.668 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 01:10:31.668 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 01:10:32.026 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 01:10:32.026 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 01:10:32.407 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 01:10:32.408 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 01:10:32.782 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 01:10:32.782 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 01:10:33.134 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 01:10:33.134 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 01:10:33.425 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 01:10:33.425 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 01:10:33.691 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 01:10:33.692 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 01:10:34.068 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 01:12:55.315 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 01:12:55.316 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 01:12:55.316 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 13:22:10.366 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 13:22:10.367 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 13:22:10.367 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 13:22:10.557 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 13:22:10.557 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 13:22:10.557 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 13:22:11.055 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 13:22:11.055 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 13:22:11.056 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 13:22:11.460 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 13:22:11.523 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 13:22:11.556 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 13:22:11.556 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 13:22:11.556 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 13:22:11.681 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 13:22:11.682 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 13:22:11.747 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 13:22:11.747 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 13:22:11.903 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 13:22:11.953 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 13:22:11.953 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 13:22:11.969 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 13:22:11.970 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 13:22:12.123 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 13:22:12.175 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 13:22:12.175 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 13:22:12.195 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 13:22:12.196 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 13:22:12.398 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 13:22:12.398 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 13:22:12.418 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 13:22:12.418 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 13:22:12.620 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 13:22:12.620 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 13:22:12.641 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 13:22:12.642 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 13:22:12.842 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 13:22:12.843 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 13:22:12.864 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 13:22:12.864 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 13:22:13.086 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 13:22:13.087 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 13:22:13.182 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 13:22:13.182 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 13:22:13.310 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 13:22:13.409 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 13:22:13.409 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 13:22:13.633 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 13:22:13.633 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 13:22:13.856 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 13:22:13.856 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 13:22:14.080 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 13:22:14.080 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 13:22:14.305 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 13:22:14.305 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 13:22:14.530 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 13:22:14.530 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 13:22:14.756 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 13:22:14.756 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 13:22:14.990 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 13:22:14.990 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 13:22:15.320 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 13:22:15.326 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 13:22:15.652 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 13:22:15.653 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 13:22:15.977 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 13:22:15.977 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 13:22:16.270 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 13:22:16.270 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 13:22:16.494 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 13:22:16.494 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 13:22:16.716 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 13:22:16.716 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 13:22:16.940 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 13:22:16.941 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 13:22:17.162 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 17:08:26.716 +10:00 [INF] Starting Yendor Cats API
2025-07-09 17:12:16.829 +10:00 [WRN] Failed to determine the https port for redirect.
2025-07-09 17:12:16.847 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-09 17:12:16.852 +10:00 [ERR] Failed to get JWT settings from HashiCorp Vault, using values from appsettings
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__5(JwtBearerOptions options) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 226
2025-07-09 17:12:17.460 +10:00 [INF] Using S3 credentials from appsettings configuration.
2025-07-09 17:12:17.486 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 17:12:17.486 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 17:12:17.499 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 17:12:17.932 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 17:12:17.933 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 17:12:17.933 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 17:12:18.432 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 17:12:18.432 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 17:12:18.433 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 17:12:18.933 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 17:12:18.934 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 17:12:18.934 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 17:12:19.668 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 17:12:19.726 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 17:12:19.877 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 17:12:19.893 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 17:12:19.895 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 17:12:19.998 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 17:12:19.998 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 17:12:20.107 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 17:12:20.126 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 17:12:20.126 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 17:12:20.280 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 17:12:20.280 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 17:12:20.345 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 17:12:20.346 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 17:12:20.574 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 17:12:20.574 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 17:12:20.576 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 17:12:20.576 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 17:12:20.798 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 17:12:20.799 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 17:12:20.859 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 17:12:20.859 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 17:12:21.029 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 17:12:21.030 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 17:12:21.153 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 17:12:21.153 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 17:12:21.267 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 17:12:21.267 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 17:12:21.445 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 17:12:21.446 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 17:12:21.513 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 17:12:21.513 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 17:12:21.715 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 17:12:21.716 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 17:12:21.734 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 17:12:21.734 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 17:12:21.981 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 17:12:21.982 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 17:12:22.010 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 17:12:22.215 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 17:12:22.216 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 17:12:22.443 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 17:12:22.443 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 17:12:22.668 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 17:12:22.668 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 17:12:22.989 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 17:12:22.989 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 17:12:23.257 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 17:12:23.257 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 17:12:23.577 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 17:12:23.577 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 17:12:23.814 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 17:12:23.814 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 17:12:24.109 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 17:12:24.109 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 17:12:24.474 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 17:12:24.474 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 17:12:24.810 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 17:12:24.810 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 17:12:25.175 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 17:12:25.176 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 17:12:25.421 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 17:12:25.421 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 17:12:25.657 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 20:18:31.493 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 20:18:31.495 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 20:18:31.496 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 20:18:31.991 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 20:18:31.991 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 20:18:31.992 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 20:18:32.491 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 20:18:32.491 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 20:18:32.492 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 20:18:32.992 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 20:18:32.992 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 20:18:32.994 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 20:19:02.170 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 20:19:02.170 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 20:19:02.212 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 20:19:02.648 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 20:19:02.648 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 20:19:02.648 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 20:19:02.702 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 20:19:02.702 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 20:19:03.083 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 20:19:03.084 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 20:19:03.134 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 20:19:03.134 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 20:19:03.629 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 20:19:03.629 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 20:19:03.764 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 20:19:03.764 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 20:19:04.452 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 20:19:04.456 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 20:19:04.790 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 20:19:04.790 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 20:19:05.466 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 20:19:05.466 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 20:19:05.563 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 20:19:05.563 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 20:19:05.689 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 20:19:05.689 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 20:19:05.821 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 20:19:05.821 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 20:19:05.924 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 20:19:05.924 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 20:19:06.068 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 20:19:06.068 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 20:19:06.172 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 20:19:06.322 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 20:19:06.322 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 20:19:06.703 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 20:19:06.703 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 20:19:06.925 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 20:19:06.925 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 20:19:07.130 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 20:19:07.130 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 20:19:07.390 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 20:19:07.390 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 20:19:07.605 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 20:19:07.605 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 20:19:07.847 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 20:19:07.847 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 20:19:08.093 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 20:19:08.094 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 20:19:08.383 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 20:19:08.383 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 20:19:08.673 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 20:19:08.674 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 20:19:08.973 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 20:19:08.973 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 20:19:09.223 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 20:19:09.224 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 20:19:09.531 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 20:19:09.531 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 20:19:09.782 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 20:19:09.782 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 20:19:10.004 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 20:19:10.004 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 20:19:10.357 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 22:27:56.530 +10:00 [INF] Starting Yendor Cats API
2025-07-09 22:27:56.599 +10:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5002: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-09 22:27:56.607 +10:00 [FTL] Application start-up failed
System.IO.IOException: Failed to bind to address http://127.0.0.1:5002: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 349
2025-07-09 22:28:36.101 +10:00 [INF] Starting Yendor Cats API
2025-07-09 22:32:27.864 +10:00 [INF] Starting Yendor Cats API
2025-07-09 22:33:51.777 +10:00 [INF] Starting Yendor Cats API
2025-07-09 22:44:42.955 +10:00 [INF] Starting Yendor Cats API
2025-07-09 22:47:05.210 +10:00 [INF] Starting Yendor Cats API
2025-07-09 22:47:05.266 +10:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5002: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-09 22:47:05.272 +10:00 [FTL] Application start-up failed
System.IO.IOException: Failed to bind to address http://127.0.0.1:5002: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 349
2025-07-09 22:48:40.431 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 22:49:16.182 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 22:49:16.524 +10:00 [INF] Starting Yendor Cats API
2025-07-09 22:49:58.110 +10:00 [INF] Using S3 credentials from appsettings configuration.
2025-07-09 22:49:58.167 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:49:58.168 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:49:58.182 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 22:49:58.518 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:49:58.518 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:49:58.521 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 22:49:59.020 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:49:59.020 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:49:59.021 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 22:49:59.519 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:49:59.520 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:49:59.521 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 22:49:59.546 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 22:49:59.546 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 22:49:59.787 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 22:49:59.789 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 22:49:59.790 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 22:50:00.144 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 22:50:00.144 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 22:50:00.144 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 22:50:00.144 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 22:50:00.144 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 22:50:00.364 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 22:50:00.364 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 22:50:00.366 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 22:50:00.367 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 22:50:00.649 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 22:50:00.649 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 22:50:00.649 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 22:50:00.649 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 22:50:01.004 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 22:50:01.004 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 22:50:01.005 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 22:50:01.005 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 22:50:01.241 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 22:50:01.241 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 22:50:01.388 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 22:50:01.388 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 22:50:01.628 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 22:50:01.628 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 22:50:01.628 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 22:50:01.628 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 22:50:01.851 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 22:50:01.851 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 22:50:01.852 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 22:50:01.852 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 22:50:02.073 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 22:50:02.073 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 22:50:02.074 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 22:50:02.304 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 22:50:02.304 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 22:50:02.526 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 22:50:02.526 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 22:50:02.752 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 22:50:02.752 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 22:50:02.974 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 22:50:02.975 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 22:50:03.212 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 22:50:03.213 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 22:50:03.451 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 22:50:03.451 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 22:50:03.677 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 22:50:03.677 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 22:50:04.002 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 22:50:04.002 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 22:50:04.325 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 22:50:04.326 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 22:50:04.608 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 22:50:04.609 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 22:50:04.836 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 22:50:04.836 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 22:50:05.117 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 22:50:05.118 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 22:50:05.340 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 22:50:05.341 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 22:50:05.565 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 22:51:21.651 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:51:21.652 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:51:21.653 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 22:51:22.152 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:51:22.152 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:51:22.153 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 22:51:22.653 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:51:22.653 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:51:22.653 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 22:51:22.914 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 22:51:23.154 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 22:51:23.276 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 22:51:23.378 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 22:51:23.378 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 22:51:23.500 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 22:51:23.501 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 22:51:23.587 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 22:51:23.601 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 22:51:23.601 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 22:51:23.723 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 22:51:23.724 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 22:51:23.827 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 22:51:23.828 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 22:51:23.945 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 22:51:23.945 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 22:51:24.053 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 22:51:24.053 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 22:51:24.167 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 22:51:24.168 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 22:51:24.277 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 22:51:24.278 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 22:51:24.390 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 22:51:24.391 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 22:51:24.503 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 22:51:24.503 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 22:51:24.613 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 22:51:24.613 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 22:51:24.727 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 22:51:24.728 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 22:51:24.833 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 22:51:24.833 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 22:51:24.952 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 22:51:24.952 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 22:51:25.056 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 22:51:25.176 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 22:51:25.176 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 22:51:25.400 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 22:51:25.401 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 22:51:25.626 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 22:51:25.627 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 22:51:25.870 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 22:51:25.870 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 22:51:26.094 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 22:51:26.094 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 22:51:26.320 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 22:51:26.320 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 22:51:26.547 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 22:51:26.547 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 22:51:26.875 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 22:51:26.875 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 22:51:27.189 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 22:51:27.190 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 22:51:27.515 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 22:51:27.515 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 22:51:27.752 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 22:51:27.753 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 22:51:27.978 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 22:51:27.978 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 22:51:28.219 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 22:51:28.219 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 22:51:28.443 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 22:51:28.443 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 22:51:28.667 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 22:57:22.112 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:57:22.112 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:57:22.112 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 22:57:22.613 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:57:22.614 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:57:22.615 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 22:57:23.111 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:57:23.112 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:57:23.113 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 22:57:23.284 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 22:57:23.509 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:23.509 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 22:57:23.512 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 22:57:23.612 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:57:23.612 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:57:23.613 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 22:57:23.731 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:23.731 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 22:57:23.732 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 22:57:23.733 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 22:57:23.953 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 22:57:23.954 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 22:57:23.957 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 22:57:24.173 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 22:57:24.174 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 22:57:24.181 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 22:57:24.182 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 22:57:24.393 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:24.405 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 22:57:24.406 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 22:57:24.526 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 22:57:24.526 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 22:57:24.632 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 22:57:24.632 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 22:57:24.752 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 22:57:24.752 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 22:57:24.860 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 22:57:24.860 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 22:57:24.980 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 22:57:24.980 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 22:57:25.084 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 22:57:25.085 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 22:57:25.206 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:25.207 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 22:57:25.310 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 22:57:25.311 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 22:57:25.432 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 22:57:25.432 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 22:57:25.534 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 22:57:25.659 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 22:57:25.659 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 22:57:25.883 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 22:57:25.883 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 22:57:26.109 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 22:57:26.110 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 22:57:26.336 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 22:57:26.336 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 22:57:26.566 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 22:57:26.567 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 22:57:26.793 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 22:57:26.795 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 22:57:27.024 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:27.025 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 22:57:27.317 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 22:57:27.317 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 22:57:27.647 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 22:57:27.648 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 22:57:28.073 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 22:57:28.074 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 22:57:28.526 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 22:57:28.526 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 22:57:28.892 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 22:57:28.892 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 22:57:29.173 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 22:57:29.174 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 22:57:29.659 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 22:57:49.456 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:57:49.456 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:57:49.457 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 22:57:49.943 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:57:49.944 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:57:49.944 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 22:57:50.442 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:57:50.442 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:57:50.443 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 22:57:50.597 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 22:57:50.832 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 22:57:50.849 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:50.850 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 22:57:50.944 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 22:57:50.944 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 22:57:50.944 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 22:57:51.058 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 22:57:51.058 +10:00 [INF] Getting metadata for file: queens/IMG_4275.jpg
2025-07-09 22:57:51.074 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:51.074 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 22:57:51.287 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 22:57:51.298 +10:00 [INF] Retrieved metadata for file: queens/IMG_4275.jpg, MetadataCount: 1
2025-07-09 22:57:51.299 +10:00 [INF] Getting metadata for file: queens/IMG_4400.jpg
2025-07-09 22:57:51.512 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 22:57:51.513 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 22:57:51.524 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:51.738 +10:00 [INF] Retrieved metadata for file: queens/IMG_4400.jpg, MetadataCount: 1
2025-07-09 22:57:51.739 +10:00 [INF] Getting metadata for file: queens/IMG_4406.jpg
2025-07-09 22:57:51.748 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 22:57:51.748 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 22:57:51.965 +10:00 [INF] Retrieved metadata for file: queens/IMG_4406.jpg, MetadataCount: 1
2025-07-09 22:57:51.965 +10:00 [INF] Getting metadata for file: queens/IMG_6277.jpg
2025-07-09 22:57:51.975 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 22:57:51.975 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 22:57:52.191 +10:00 [INF] Retrieved metadata for file: queens/IMG_6277.jpg, MetadataCount: 1
2025-07-09 22:57:52.192 +10:00 [INF] Getting metadata for file: queens/IMG_7867 - Copy.jpg
2025-07-09 22:57:52.199 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 22:57:52.200 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 22:57:52.417 +10:00 [INF] Retrieved metadata for file: queens/IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 22:57:52.418 +10:00 [INF] Getting metadata for file: queens/IMG_8520.jpg
2025-07-09 22:57:52.425 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 22:57:52.425 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 22:57:52.643 +10:00 [INF] Retrieved metadata for file: queens/IMG_8520.jpg, MetadataCount: 1
2025-07-09 22:57:52.644 +10:00 [INF] Getting metadata for file: queens/IMG_9260.jpg
2025-07-09 22:57:52.650 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:52.650 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 22:57:52.869 +10:00 [INF] Retrieved metadata for file: queens/IMG_9260.jpg, MetadataCount: 1
2025-07-09 22:57:52.876 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 22:57:52.876 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 22:57:53.102 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 22:57:53.102 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 22:57:53.328 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 22:57:53.328 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 22:57:53.554 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 22:57:53.555 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 22:57:53.779 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 22:57:53.779 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 22:57:54.007 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 22:57:54.007 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 22:57:54.234 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 22:57:54.234 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 22:57:54.547 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 22:57:54.547 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 22:57:54.952 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 22:57:54.952 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 22:57:55.445 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 22:57:55.445 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 22:57:55.808 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 22:57:55.808 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 22:57:56.042 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 22:57:56.042 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 22:57:56.306 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 22:57:56.306 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 22:57:56.663 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 22:57:56.664 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 22:57:56.948 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 23:04:44.368 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:04:44.368 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:04:44.368 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 23:04:45.411 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 23:04:45.635 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 23:04:45.635 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 23:04:45.857 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 23:04:45.857 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 23:04:46.079 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 23:04:46.079 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 23:04:46.303 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 23:04:46.303 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 23:04:46.533 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 23:04:46.533 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 23:04:46.756 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 23:04:46.756 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 23:04:46.980 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 23:04:46.980 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 23:04:47.202 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 23:04:47.202 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 23:04:47.424 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 23:04:47.425 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 23:04:47.646 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 23:04:47.646 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 23:04:47.871 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 23:04:47.871 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 23:04:48.093 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 23:04:48.093 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 23:04:48.315 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 23:04:48.315 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 23:04:48.537 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 23:04:48.537 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 23:04:48.777 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 23:04:48.777 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 23:04:48.999 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 23:04:49.000 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 23:04:49.223 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 23:04:49.223 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 23:04:49.446 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 23:04:49.446 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 23:04:49.670 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 23:04:49.670 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 23:04:49.892 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 23:04:49.892 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 23:04:50.117 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 23:04:50.117 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 23:04:50.339 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 23:04:50.339 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 23:04:50.561 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 23:09:23.741 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:10:05.159 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:10:05.512 +10:00 [INF] Starting Yendor Cats API
2025-07-09 23:10:05.546 +10:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5002: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-09 23:10:05.553 +10:00 [FTL] Application start-up failed
System.IO.IOException: Failed to bind to address http://127.0.0.1:5002: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 363
2025-07-09 23:10:24.172 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:10:24.513 +10:00 [INF] Starting Yendor Cats API
2025-07-09 23:11:05.614 +10:00 [INF] Using S3 credentials from appsettings configuration.
2025-07-09 23:11:05.634 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:11:05.635 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:11:05.636 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:11:05.717 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 23:11:06.785 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 23:11:07.012 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 23:11:07.013 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 23:11:07.236 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 23:11:07.236 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 23:11:07.459 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 23:11:07.459 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 23:11:07.681 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 23:11:07.681 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 23:11:07.903 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 23:11:07.903 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 23:11:08.125 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 23:11:08.125 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 23:11:08.352 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 23:11:08.352 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 23:11:08.576 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 23:11:08.576 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 23:11:08.798 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 23:11:08.798 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 23:11:09.022 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 23:11:09.022 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 23:11:09.244 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 23:11:09.244 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 23:11:09.467 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 23:11:09.467 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 23:11:09.724 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 23:11:09.724 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 23:11:09.951 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 23:11:09.951 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 23:11:10.173 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 23:11:10.173 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 23:11:10.397 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 23:11:10.397 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 23:11:10.620 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 23:11:10.620 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 23:11:10.844 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 23:11:10.844 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 23:11:11.066 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 23:11:11.066 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 23:11:11.288 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 23:11:11.288 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 23:11:11.510 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 23:11:11.511 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 23:11:11.733 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 23:11:11.733 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 23:11:11.957 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 23:11:11.957 +10:00 [INF] Total images found for category studs: 23 (DB: 0, S3: 23)
2025-07-09 23:42:57.713 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:42:57.714 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:42:57.717 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:42:57.741 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 23:42:58.206 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:42:58.206 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:42:58.206 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:42:58.207 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 23:42:58.736 +10:00 [INF] Total images found for category gallery: 0 (DB: 0, S3: 0)
2025-07-09 23:42:58.852 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:42:58.852 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:42:58.852 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:42:58.853 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 23:42:58.962 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 23:42:59.214 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:42:59.214 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:42:59.214 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:42:59.215 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 23:42:59.261 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 23:42:59.261 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 23:42:59.261 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 23:42:59.483 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 23:42:59.484 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 23:42:59.487 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 23:42:59.783 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 23:42:59.783 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 23:42:59.783 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_4275.jpg
2025-07-09 23:42:59.783 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 23:43:00.005 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 23:43:00.005 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_4275.jpg, MetadataCount: 1
2025-07-09 23:43:00.006 +10:00 [INF] Total images found for category kittens: 1 (DB: 0, S3: 1)
2025-07-09 23:43:00.006 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_4400.jpg
2025-07-09 23:43:00.312 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_4400.jpg, MetadataCount: 1
2025-07-09 23:43:00.312 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 23:43:00.313 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_4406.jpg
2025-07-09 23:43:00.313 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 23:43:00.538 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 23:43:00.538 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 23:43:00.539 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_4406.jpg, MetadataCount: 1
2025-07-09 23:43:00.539 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_6277.jpg
2025-07-09 23:43:00.833 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 23:43:00.833 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_6277.jpg, MetadataCount: 1
2025-07-09 23:43:00.834 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 23:43:00.834 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_7867 - Copy.jpg
2025-07-09 23:43:01.055 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 23:43:01.056 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 23:43:01.059 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 23:43:01.060 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_8520.jpg
2025-07-09 23:43:01.357 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_8520.jpg, MetadataCount: 1
2025-07-09 23:43:01.357 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 23:43:01.357 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_9260.jpg
2025-07-09 23:43:01.358 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 23:43:01.582 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 23:43:01.582 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 23:43:01.585 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_9260.jpg, MetadataCount: 1
2025-07-09 23:43:01.585 +10:00 [INF] Total images found for category queens: 8 (DB: 0, S3: 8)
2025-07-09 23:43:01.883 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 23:43:01.884 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 23:43:02.113 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 23:43:02.113 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 23:43:02.406 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 23:43:02.406 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 23:43:02.629 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 23:43:02.629 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 23:43:02.931 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 23:43:02.931 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 23:43:03.158 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 23:43:03.159 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 23:43:03.457 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 23:43:03.457 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 23:43:03.786 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 23:43:03.787 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 23:43:04.111 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 23:43:04.111 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 23:43:04.380 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 23:43:04.380 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 23:43:04.617 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 23:43:04.618 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 23:43:04.843 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 23:43:04.844 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 23:43:05.070 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 23:43:05.070 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 23:43:05.295 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 23:43:05.295 +10:00 [INF] Total images found for category studs: 23 (DB: 0, S3: 23)
2025-07-09 23:44:13.833 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:44:13.833 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:44:13.833 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:44:13.835 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-09 23:44:14.327 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:44:14.327 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:44:14.327 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:44:14.328 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-09 23:44:14.758 +10:00 [INF] Total images found for category gallery: 0 (DB: 0, S3: 0)
2025-07-09 23:44:14.827 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:44:14.827 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:44:14.827 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:44:14.828 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-09 23:44:14.989 +10:00 [INF] Getting metadata for file: studs/.bzEmpty
2025-07-09 23:44:15.284 +10:00 [INF] Retrieved metadata for file: studs/.bzEmpty, MetadataCount: 0
2025-07-09 23:44:15.284 +10:00 [INF] Getting metadata for file: studs/Dennis/.bzEmpty
2025-07-09 23:44:15.284 +10:00 [INF] Getting metadata for file: queens/.bzEmpty
2025-07-09 23:44:15.327 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-09 23:44:15.327 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-09 23:44:15.327 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-09 23:44:15.328 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-09 23:44:15.510 +10:00 [INF] Retrieved metadata for file: studs/Dennis/.bzEmpty, MetadataCount: 0
2025-07-09 23:44:15.511 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-09 23:44:15.519 +10:00 [INF] Retrieved metadata for file: queens/.bzEmpty, MetadataCount: 1
2025-07-09 23:44:15.520 +10:00 [INF] Getting metadata for file: queens/Cat1/.bzEmpty
2025-07-09 23:44:15.808 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-09 23:44:15.808 +10:00 [INF] Getting metadata for file: kittens/.bzEmpty
2025-07-09 23:44:15.808 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-09 23:44:16.033 +10:00 [INF] Retrieved metadata for file: queens/Cat1/.bzEmpty, MetadataCount: 0
2025-07-09 23:44:16.033 +10:00 [INF] Retrieved metadata for file: kittens/.bzEmpty, MetadataCount: 0
2025-07-09 23:44:16.033 +10:00 [INF] Total images found for category kittens: 1 (DB: 0, S3: 1)
2025-07-09 23:44:16.033 +10:00 [INF] Getting metadata for file: queens/Cat1/TESTING-IMG_4275.jpg
2025-07-09 23:44:16.331 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-09 23:44:16.331 +10:00 [INF] Retrieved metadata for file: queens/Cat1/TESTING-IMG_4275.jpg, MetadataCount: 1
2025-07-09 23:44:16.331 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-09 23:44:16.332 +10:00 [INF] Getting metadata for file: queens/Cat1/TESTING-IMG_4400.jpg
2025-07-09 23:44:16.557 +10:00 [INF] Retrieved metadata for file: queens/Cat1/TESTING-IMG_4400.jpg, MetadataCount: 1
2025-07-09 23:44:16.557 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_4406.jpg
2025-07-09 23:44:16.558 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-09 23:44:16.559 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-09 23:44:16.856 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_4406.jpg, MetadataCount: 1
2025-07-09 23:44:16.856 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-09 23:44:16.856 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_6277.jpg
2025-07-09 23:44:16.856 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-09 23:44:17.082 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_6277.jpg, MetadataCount: 1
2025-07-09 23:44:17.082 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_7867 - Copy.jpg
2025-07-09 23:44:17.085 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-09 23:44:17.085 +10:00 [INF] Getting metadata for file: studs/Soren/.bzEmpty
2025-07-09 23:44:17.378 +10:00 [INF] Retrieved metadata for file: studs/Soren/.bzEmpty, MetadataCount: 0
2025-07-09 23:44:17.378 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_7867 - Copy.jpg, MetadataCount: 1
2025-07-09 23:44:17.378 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-09 23:44:17.378 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_8520.jpg
2025-07-09 23:44:17.602 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-09 23:44:17.603 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-09 23:44:17.604 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_8520.jpg, MetadataCount: 1
2025-07-09 23:44:17.604 +10:00 [INF] Getting metadata for file: queens/TESTING-IMG_9260.jpg
2025-07-09 23:44:17.905 +10:00 [INF] Retrieved metadata for file: queens/TESTING-IMG_9260.jpg, MetadataCount: 1
2025-07-09 23:44:17.905 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-09 23:44:17.906 +10:00 [INF] Total images found for category queens: 9 (DB: 0, S3: 9)
2025-07-09 23:44:17.906 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-09 23:44:18.133 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-09 23:44:18.134 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-09 23:44:18.429 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-09 23:44:18.430 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-09 23:44:18.656 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-09 23:44:18.656 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-09 23:44:18.954 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-09 23:44:18.954 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-09 23:44:19.182 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-09 23:44:19.182 +10:00 [INF] Getting metadata for file: studs/louie/.bzEmpty
2025-07-09 23:44:19.477 +10:00 [INF] Retrieved metadata for file: studs/louie/.bzEmpty, MetadataCount: 0
2025-07-09 23:44:19.478 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-09 23:44:19.758 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-09 23:44:19.758 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-09 23:44:20.085 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-09 23:44:20.086 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-09 23:44:20.416 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-09 23:44:20.416 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-09 23:44:20.684 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-09 23:44:20.684 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-09 23:44:21.053 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-09 23:44:21.054 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-09 23:44:21.295 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-09 23:44:21.295 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-09 23:44:21.573 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-09 23:44:21.574 +10:00 [INF] Total images found for category studs: 23 (DB: 0, S3: 23)
