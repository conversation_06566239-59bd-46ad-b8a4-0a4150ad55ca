2025-03-23 15:56:32.946 +10:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'Cat'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-23 15:57:36.378 +10:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'Cat'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-23 15:58:07.270 +10:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'Cat'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-23 16:00:12.225 +10:00 [INF] Starting Yendor Cats API
2025-03-23 16:00:15.564 +10:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'Cat'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-23 16:46:07.794 +10:00 [INF] Starting Yendor Cats API
