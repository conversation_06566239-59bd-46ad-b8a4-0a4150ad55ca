# Yendor Cats Development Guide

## Development Environment Setup

The Yendor Cats project uses a .NET backend with a static HTML/CSS/JavaScript frontend. To simplify development, we've implemented a build and watch system that automatically synchronizes frontend changes to the ASP.NET Core wwwroot directory.

## Key Features

- **Clean wwwroot During Build**: The wwwroot directory is cleaned before each build to prevent old files from accumulating.
- **Frontend File Watching**: Changes to frontend files are automatically copied to wwwroot.
- **Hot Reload**: The application supports dotnet watch for backend hot reloading.

## How to Run the Development Environment

### Option 1: Using dotnet CLI directly

```bash
# Navigate to the API project directory
cd backend/YendorCats.API

# Run with dotnet watch
dotnet watch run
```

This will:
1. Clean the wwwroot directory
2. Copy all frontend files to wwwroot
3. Start the application with hot reload for backend changes

However, frontend changes won't be automatically copied with this approach.

### Option 2: Using the Watch Script (Recommended)

#### For Linux/macOS:

```bash
# Navigate to the API project directory
cd backend/YendorCats.API

# Run the watch script
./watch-frontend.sh
```

#### For Windows:

```powershell
# Navigate to the API project directory
cd backend\YendorCats.API

# Run the watch script
.\watch-frontend.ps1
```

This will:
1. Clean the wwwroot directory
2. Copy all frontend files to wwwroot
3. Watch for changes in the frontend directory and automatically copy them to wwwroot
4. Start the application with dotnet watch for hot reload

## How It Works

### Build Process

The .csproj file includes custom MSBuild targets that:
1. Clean the wwwroot directory before each build
2. Copy all frontend files to wwwroot
3. Verify the files were copied correctly

### Watch System

The watch scripts run a file system watcher that monitors the frontend directory for changes. When a file is created, modified, or deleted:
1. The corresponding file in wwwroot is updated
2. The change is logged to the console

Meanwhile, dotnet watch monitors the backend code for changes and automatically restarts the application when needed.

## Troubleshooting

If you encounter issues:

1. **Files not being copied**: Make sure the frontend directory exists and contains the necessary files.
2. **Permission errors**: Ensure you have write permissions to the wwwroot directory.
3. **Watch script crashes**: Make sure dotnet is installed and in your PATH.

## Additional Notes

- The watch system ignores hidden files and node_modules directory
- When running dotnet watch directly, you may need to manually rebuild to see frontend changes 