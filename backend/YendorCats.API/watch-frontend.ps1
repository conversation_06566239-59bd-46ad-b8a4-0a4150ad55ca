# Yendor Cats Frontend Watcher and Development Server
# This script:
# 1. Watches the frontend directory for changes
# 2. Copies changes to wwwroot
# 3. Runs dotnet watch for backend hot reload

Write-Host "Starting Yendor Cats Development Environment" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# Make sure wwwroot exists
if (-not (Test-Path -Path "wwwroot")) {
    New-Item -ItemType Directory -Path "wwwroot" | Out-Null
}

# Initial clean and copy of frontend files
Write-Host "Performing initial cleanup and copy of frontend files..." -ForegroundColor Cyan
if (Test-Path -Path "wwwroot") {
    Get-ChildItem -Path "wwwroot" -Recurse | Remove-Item -Force -Recurse
}
Copy-Item -Path "../../frontend/*" -Destination "wwwroot/" -Recurse -Force

# Create a job to monitor frontend changes
$monitorJob = Start-Job -ScriptBlock {
    param($rootPath)
    
    Write-Host "Starting frontend file watcher..." -ForegroundColor Cyan
    
    # Use a FileSystemWatcher to watch for changes
    $watcher = New-Object System.IO.FileSystemWatcher
    $watcher.Path = "$rootPath/../../frontend"
    $watcher.IncludeSubdirectories = $true
    $watcher.EnableRaisingEvents = $true
    
    # Define the events
    $action = {
        $path = $Event.SourceEventArgs.FullPath
        $changetype = $Event.SourceEventArgs.ChangeType
        $filename = $Event.SourceEventArgs.Name
        
        # Skip hidden files and node_modules
        if ($path -match "\\node_modules\\" -or $path -match "\\\.[^\\]+") {
            return
        }
        
        # Get relative path for destination
        $relativePath = $path.Substring("$rootPath/../../frontend".Length)
        $destPath = "$rootPath/wwwroot$relativePath"
        
        if ($changetype -eq 'Changed' -or $changetype -eq 'Created') {
            try {
                # Make sure the directory exists
                $destDir = Split-Path -Path $destPath -Parent
                if (-not (Test-Path -Path $destDir)) {
                    New-Item -ItemType Directory -Path $destDir -Force | Out-Null
                }
                
                # Copy the file
                Copy-Item -Path $path -Destination $destPath -Force
                Write-Host "Copied: $relativePath" -ForegroundColor Green
            } catch {
                Write-Host "Error copying file: $_" -ForegroundColor Red
            }
        } elseif ($changetype -eq 'Deleted') {
            if (Test-Path -Path $destPath) {
                Remove-Item -Path $destPath -Force
                Write-Host "Deleted: $relativePath" -ForegroundColor Yellow
            }
        } elseif ($changetype -eq 'Renamed') {
            # Not handling renames specifically as they generate delete/create events
        }
    }
    
    # Register event handlers
    $handlers = @()
    $handlers += Register-ObjectEvent -InputObject $watcher -EventName Created -Action $action
    $handlers += Register-ObjectEvent -InputObject $watcher -EventName Changed -Action $action
    $handlers += Register-ObjectEvent -InputObject $watcher -EventName Deleted -Action $action
    $handlers += Register-ObjectEvent -InputObject $watcher -EventName Renamed -Action $action
    
    try {
        # Keep the script running
        Write-Host "Watching for changes in frontend directory..." -ForegroundColor Cyan
        while ($true) { Start-Sleep -Seconds 1 }
    } finally {
        # Clean up
        $handlers | ForEach-Object { Unregister-Event -SourceIdentifier $_.Name }
        $watcher.Dispose()
    }
} -ArgumentList $PWD

Write-Host "Starting dotnet watch..." -ForegroundColor Cyan

try {
    # Run dotnet watch
    dotnet watch run
} finally {
    # Clean up the monitoring job when dotnet watch exits
    Stop-Job -Job $monitorJob
    Remove-Job -Job $monitorJob -Force
    Write-Host "Stopped frontend watcher." -ForegroundColor Yellow
} 