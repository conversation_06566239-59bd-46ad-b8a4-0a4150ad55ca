#!/bin/bash

# Yendor Cats Frontend Watcher and Development Server
# This script:
# 1. Watches the frontend directory for changes
# 2. Copies changes to wwwroot
# 3. Runs dotnet watch for backend hot reload

echo "Starting Yendor Cats Development Environment"
echo "=========================================="

# Make sure wwwroot exists
mkdir -p wwwroot

# Initial clean and copy of frontend files
echo "Performing initial cleanup and copy of frontend files..."
find ./wwwroot -mindepth 1 -delete 2>/dev/null
cp -r ../../frontend/* ./wwwroot/

# Monitor frontend changes and copy them to wwwroot
monitor_frontend() {
    echo "Starting frontend file watcher..."
    while true; do
        find ../../frontend -type f -not -path "*/node_modules/*" -not -path "*/\.*" | 
        while read file; do
            # Get the relative path from frontend to determine destination
            rel_path=${file#../../frontend/}
            dest_file="./wwwroot/$rel_path"
            dest_dir=$(dirname "$dest_file")
            
            # Create destination directory if it doesn't exist
            mkdir -p "$dest_dir"
            
            # Copy the file
            cp "$file" "$dest_file"
            echo "Copied: $rel_path"
        done
        sleep 2
    done
}

# Start monitoring in the background
monitor_frontend &
monitor_pid=$!

# Function to clean up when the script exits
cleanup() {
    echo "Stopping frontend watcher..."
    kill $monitor_pid
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Run dotnet watch
echo "Starting dotnet watch..."
dotnet watch run

# This line should never be reached as dotnet watch keeps running,
# but just in case, we'll handle cleanup
cleanup 