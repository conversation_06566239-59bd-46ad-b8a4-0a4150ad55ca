using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;

public interface ISecretsManagerService
{
    Task<AppSecrets> GetAppSecretsAsync();
    Task<string> GetSecretAsync(string secretName);
}

public class SecretsManagerService : ISecretsManagerService
{
    private readonly IAmazonSecretsManager _secretsManager;
    private readonly IConfiguration _configuration;
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<SecretsManagerService> _logger;
    private AppSecrets? _cachedSecrets;

    public SecretsManagerService(
        IAmazonSecretsManager secretsManager,
        IConfiguration configuration,
        IWebHostEnvironment environment,
        ILogger<SecretsManagerService> logger)
    {
        _secretsManager = secretsManager;
        _configuration = configuration;
        _environment = environment;
        _logger = logger;
    }

    public async Task<AppSecrets> GetAppSecretsAsync()
    {
        if (_cachedSecrets != null)
            return _cachedSecrets;

        if (_environment.IsDevelopment())
        {
            // In development, use appsettings values
            _cachedSecrets = new AppSecrets
            {
                DbConnectionString = _configuration.GetConnectionString("DefaultConnection"),
                JwtSecret = _configuration["JwtSettings:Secret"],
                JwtIssuer = _configuration["JwtSettings:Issuer"],
                JwtAudience = _configuration["JwtSettings:Audience"],
                JwtExpiryMinutes = int.Parse(_configuration["JwtSettings:ExpiryMinutes"] ?? "60"),
                RefreshExpiryDays = int.Parse(_configuration["JwtSettings:RefreshExpiryDays"] ?? "7"),

                // For development, you can set these in user secrets or environment variables
                // These will be used only if AWS:UseCredentialsFromSecrets is true
                S3AccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID"),
                S3SecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY"),
                S3SessionToken = Environment.GetEnvironmentVariable("AWS_SESSION_TOKEN")
            };
            return _cachedSecrets;
        }

        try
        {
            // In production, get from AWS Secrets Manager
            string secretJson = await GetSecretAsync("yendorcats/app-secrets");
            _cachedSecrets = JsonSerializer.Deserialize<AppSecrets>(secretJson) ?? new AppSecrets();
            return _cachedSecrets;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve secrets from AWS Secrets Manager");
            throw;
        }
    }

    public async Task<string> GetSecretAsync(string secretName)
    {
        if (_environment.IsDevelopment() && secretName == "yendorcats/app-secrets")
        {
            // Mock secret in development to avoid AWS costs
            return JsonSerializer.Serialize(new AppSecrets
            {
                DbConnectionString = _configuration.GetConnectionString("DefaultConnection"),
                JwtSecret = _configuration["JwtSettings:Secret"],
                JwtIssuer = _configuration["JwtSettings:Issuer"],
                JwtAudience = _configuration["JwtSettings:Audience"],
                JwtExpiryMinutes = int.Parse(_configuration["JwtSettings:ExpiryMinutes"] ?? "60"),
                RefreshExpiryDays = int.Parse(_configuration["JwtSettings:RefreshExpiryDays"] ?? "7"),

                // For development, you can set these in user secrets or environment variables
                S3AccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID"),
                S3SecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY"),
                S3SessionToken = Environment.GetEnvironmentVariable("AWS_SESSION_TOKEN")
            });
        }

        var request = new GetSecretValueRequest
        {
            SecretId = secretName,
            VersionStage = "AWSCURRENT"
        };

        try
        {
            var response = await _secretsManager.GetSecretValueAsync(request);
            return response.SecretString;
        }
        catch (ResourceNotFoundException)
        {
            _logger.LogError($"Secret {secretName} not found");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error retrieving secret {secretName}");
            throw;
        }
    }
}
