using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for authentication and authorization operations
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// Authenticate a user and generate a JWT token
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>A tuple containing JWT token and refresh token if authentication is successful</returns>
        Task<(string token, string refreshToken)> AuthenticateAsync(string username, string password);
        
        /// <summary>
        /// Validate a JWT token
        /// </summary>
        /// <param name="token">The JWT token to validate</param>
        /// <returns>True if the token is valid, false otherwise</returns>
        bool ValidateToken(string token);
        
        /// <summary>
        /// Generate a new refresh token for a user
        /// </summary>
        /// <param name="userId">The ID of the user</param>
        /// <returns>The refresh token</returns>
        Task<string> GenerateRefreshTokenAsync(int userId);
        
        /// <summary>
        /// Verify a refresh token and generate a new JWT token
        /// </summary>
        /// <param name="refreshToken">The refresh token</param>
        /// <returns>A tuple containing a new JWT token and refresh token if the refresh token is valid</returns>
        Task<(string token, string refreshToken)> RefreshTokenAsync(string refreshToken);
        
        /// <summary>
        /// Revoke a refresh token
        /// </summary>
        /// <param name="userId">The ID of the user</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task RevokeRefreshTokenAsync(int userId);
        
        /// <summary>
        /// Get the user ID from a JWT token
        /// </summary>
        /// <param name="token">The JWT token</param>
        /// <returns>The user ID from the token</returns>
        int GetUserIdFromToken(string token);
        
        /// <summary>
        /// Get the username from a JWT token
        /// </summary>
        /// <param name="token">The JWT token</param>
        /// <returns>The username from the token</returns>
        string GetUsernameFromToken(string token);
        
        /// <summary>
        /// Get the user role from a JWT token
        /// </summary>
        /// <param name="token">The JWT token</param>
        /// <returns>The user role from the token</returns>
        string GetRoleFromToken(string token);
        
        /// <summary>
        /// Hash a password
        /// </summary>
        /// <param name="password">The password to hash</param>
        /// <returns>A tuple containing the password hash and salt</returns>
        (string passwordHash, string passwordSalt) HashPassword(string password);
        
        /// <summary>
        /// Verify a password against a hash
        /// </summary>
        /// <param name="password">The password to verify</param>
        /// <param name="storedHash">The hash to verify against</param>
        /// <param name="storedSalt">The salt used for hashing</param>
        /// <returns>True if the password matches the hash, false otherwise</returns>
        bool VerifyPassword(string password, string storedHash, string storedSalt);
    }
} 