using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for user-related operations
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// Get all users
        /// </summary>
        /// <returns>All users in the system</returns>
        Task<IEnumerable<User>> GetAllUsersAsync();
        
        /// <summary>
        /// Get a user by ID
        /// </summary>
        /// <param name="id">The ID of the user to retrieve</param>
        /// <returns>The requested user or null if not found</returns>
        Task<User?> GetUserByIdAsync(int id);
        
        /// <summary>
        /// Get a user by username
        /// </summary>
        /// <param name="username">The username to search for</param>
        /// <returns>The requested user or null if not found</returns>
        Task<User?> GetUserByUsernameAsync(string username);
        
        /// <summary>
        /// Get a user by email
        /// </summary>
        /// <param name="email">The email to search for</param>
        /// <returns>The requested user or null if not found</returns>
        Task<User?> GetUserByEmailAsync(string email);
        
        /// <summary>
        /// Add a new user
        /// </summary>
        /// <param name="user">The user to add</param>
        /// <param name="password">The plaintext password (will be hashed)</param>
        /// <returns>The added user with its new ID</returns>
        Task<User> AddUserAsync(User user, string password);
        
        /// <summary>
        /// Update an existing user
        /// </summary>
        /// <param name="user">The updated user data</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task UpdateUserAsync(User user);
        
        /// <summary>
        /// Delete a user
        /// </summary>
        /// <param name="id">The ID of the user to delete</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task DeleteUserAsync(int id);
        
        /// <summary>
        /// Change a user's password
        /// </summary>
        /// <param name="userId">The ID of the user</param>
        /// <param name="currentPassword">The current password</param>
        /// <param name="newPassword">The new password</param>
        /// <returns>True if the password was changed, false otherwise</returns>
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        
        /// <summary>
        /// Check if a username is available
        /// </summary>
        /// <param name="username">The username to check</param>
        /// <returns>True if the username is available, false otherwise</returns>
        Task<bool> IsUsernameAvailableAsync(string username);
        
        /// <summary>
        /// Check if an email is available
        /// </summary>
        /// <param name="email">The email to check</param>
        /// <returns>True if the email is available, false otherwise</returns>
        Task<bool> IsEmailAvailableAsync(string email);
    }
} 