using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for cat-related operations
    /// </summary>
    public interface ICatService
    {
        /// <summary>
        /// Get all cats
        /// </summary>
        /// <param name="includeImages">Whether to include images in the result</param>
        /// <param name="includeInactive">Whether to include inactive cats</param>
        /// <returns>All cats matching the criteria</returns>
        Task<IEnumerable<Cat>> GetAllCatsAsync(bool includeImages = false, bool includeInactive = false);
        
        /// <summary>
        /// Get a cat by ID
        /// </summary>
        /// <param name="id">The ID of the cat to retrieve</param>
        /// <param name="includeImages">Whether to include images in the result</param>
        /// <returns>The requested cat or null if not found</returns>
        Task<Cat?> GetCatByIdAsync(int id, bool includeImages = true);
        
        /// <summary>
        /// Get cats by breed
        /// </summary>
        /// <param name="breed">The breed to filter by</param>
        /// <param name="includeImages">Whether to include images in the result</param>
        /// <param name="includeInactive">Whether to include inactive cats</param>
        /// <returns>All cats of the specified breed</returns>
        Task<IEnumerable<Cat>> GetCatsByBreedAsync(string breed, bool includeImages = false, bool includeInactive = false);
        
        /// <summary>
        /// Get cats by availability status
        /// </summary>
        /// <param name="available">The availability status to filter by</param>
        /// <param name="includeImages">Whether to include images in the result</param>
        /// <returns>All cats with the specified availability status</returns>
        Task<IEnumerable<Cat>> GetCatsByAvailabilityAsync(bool available, bool includeImages = false);
        
        /// <summary>
        /// Add a new cat
        /// </summary>
        /// <param name="cat">The cat to add</param>
        /// <returns>The added cat with its new ID</returns>
        Task<Cat> AddCatAsync(Cat cat);
        
        /// <summary>
        /// Update an existing cat
        /// </summary>
        /// <param name="cat">The updated cat data</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task UpdateCatAsync(Cat cat);
        
        /// <summary>
        /// Delete a cat
        /// </summary>
        /// <param name="id">The ID of the cat to delete</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task DeleteCatAsync(int id);
        
        /// <summary>
        /// Set a cat's availability status
        /// </summary>
        /// <param name="id">The ID of the cat</param>
        /// <param name="isAvailable">The new availability status</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task SetAvailabilityAsync(int id, bool isAvailable);
        
        /// <summary>
        /// Set a cat's active status
        /// </summary>
        /// <param name="id">The ID of the cat</param>
        /// <param name="isActive">The new active status</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task SetActiveStatusAsync(int id, bool isActive);
        
        /// <summary>
        /// Get all available breeds
        /// </summary>
        /// <returns>All breeds in the system</returns>
        Task<IEnumerable<string>> GetAllBreedsAsync();
    }
}