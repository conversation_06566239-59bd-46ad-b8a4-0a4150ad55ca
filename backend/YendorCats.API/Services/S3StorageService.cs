using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for S3 storage operations
    /// </summary>
    public class S3StorageService : IS3StorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly ILogger<S3StorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _bucketName;
        private readonly string _cdnDomain;
        private readonly bool _useCdn;
        private readonly bool _useDirectS3Urls;
        private readonly string _serviceUrl;
        private readonly string _publicUrlTemplate;

        /// <summary>
        /// Constructor for the S3 storage service
        /// </summary>
        /// <param name="s3Client">The S3 client</param>
        /// <param name="configuration">The application configuration</param>
        /// <param name="logger">The logger</param>
        public S3StorageService(
            IAmazonS3 s3Client,
            IConfiguration configuration,
            ILogger<S3StorageService> logger)
        {
            _s3Client = s3Client;
            _logger = logger;
            _configuration = configuration;

            // Get bucket name from configuration
            _bucketName = configuration["AWS:S3:BucketName"]
                ?? throw new ArgumentNullException("AWS:S3:BucketName", "S3 bucket name is not configured");

            // Check if we should use direct S3 URLs
            _useDirectS3Urls = bool.TryParse(configuration["AWS:S3:UseDirectS3Urls"], out bool useDirectS3Urls) && useDirectS3Urls;

            // Get S3 service URL
            _serviceUrl = configuration["AWS:S3:ServiceUrl"] ?? "https://s3.amazonaws.com";

            // Get public URL template
            _publicUrlTemplate = configuration["AWS:S3:PublicUrl"] ?? "https://{bucket}.s3.amazonaws.com/{key}";

            // Check if we should use CDN
            _useCdn = bool.TryParse(configuration["AWS:S3:UseCdn"], out bool useCdn) && useCdn;

            // Get CDN domain if configured
            _cdnDomain = configuration["AWS:S3:CdnDomain"] ?? string.Empty;

            // Note: S3 client should already be configured with the custom endpoint
            // We can't modify the ServiceURL after client creation
            if (!string.IsNullOrEmpty(_serviceUrl))
            {
                _logger.LogInformation("Using S3 client with endpoint: {ServiceUrl}", _serviceUrl);
            }

            _logger.LogInformation("S3StorageService initialized with bucket: {BucketName}, UseDirectS3Urls: {UseDirectS3Urls}, UseCDN: {UseCDN}",
                _bucketName, _useDirectS3Urls, _useCdn);
        }

        /// <inheritdoc />
        public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType)
        {
            _logger.LogInformation("Uploading file to S3: {FileName}, ContentType: {ContentType}",
                fileName, contentType);

            try
            {
                // Create a TransferUtility for easy uploading
                using var transferUtility = new TransferUtility(_s3Client);

                // Configure the upload request
                var uploadRequest = new TransferUtilityUploadRequest
                {
                    InputStream = fileStream,
                    BucketName = _bucketName,
                    Key = fileName,
                    ContentType = contentType,
                    CannedACL = S3CannedACL.PublicRead // Make the file publicly readable
                };

                // Upload the file
                await transferUtility.UploadAsync(uploadRequest);

                // Return the URL to the file
                return GetFileUrl(fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to S3: {FileName}", fileName);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<string> UploadFileWithMetadataAsync(Stream fileStream, string fileName, string contentType, Dictionary<string, string> metadata)
        {
            _logger.LogInformation("Uploading file to S3 with metadata: {FileName}, ContentType: {ContentType}, MetadataCount: {MetadataCount}",
                fileName, contentType, metadata?.Count ?? 0);

            try
            {
                // Create a PutObjectRequest for more control over metadata
                var putRequest = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = fileName,
                    InputStream = fileStream,
                    ContentType = contentType,
                    CannedACL = S3CannedACL.PublicRead // Make the file publicly readable
                };

                // Add metadata if provided
                if (metadata != null && metadata.Count > 0)
                {
                    foreach (var kvp in metadata)
                    {
                        putRequest.Metadata.Add(kvp.Key, kvp.Value);
                    }
                }

                // Upload the file
                await _s3Client.PutObjectAsync(putRequest);

                // Return the URL to the file
                return GetFileUrl(fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file with metadata to S3: {FileName}", fileName);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task DeleteFileAsync(string fileName)
        {
            _logger.LogInformation("Deleting file from S3: {FileName}", fileName);

            try
            {
                // Create a delete request
                var deleteRequest = new DeleteObjectRequest
                {
                    BucketName = _bucketName,
                    Key = fileName
                };

                // Delete the file
                await _s3Client.DeleteObjectAsync(deleteRequest);

                _logger.LogInformation("File deleted from S3: {FileName}", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file from S3: {FileName}", fileName);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<string> GetPreSignedUrlAsync(string fileName, int expiryMinutes = 60)
        {
            _logger.LogInformation("Generating pre-signed URL for file: {FileName}, ExpiryMinutes: {ExpiryMinutes}",
                fileName, expiryMinutes);

            try
            {
                // Create a request for a pre-signed URL
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _bucketName,
                    Key = fileName,
                    Expires = DateTime.UtcNow.AddMinutes(expiryMinutes)
                };

                // Get the pre-signed URL
                var url = await Task.FromResult(_s3Client.GetPreSignedURL(request));

                _logger.LogInformation("Generated pre-signed URL for file: {FileName}", fileName);

                return url;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating pre-signed URL for file: {FileName}", fileName);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<Dictionary<string, string>> GetObjectMetadataAsync(string fileName)
        {
            _logger.LogInformation("Getting metadata for file: {FileName}", fileName);

            try
            {
                // Create a request to get the object metadata
                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = fileName
                };

                // Get the object metadata
                var response = await _s3Client.GetObjectMetadataAsync(request);

                // Convert the metadata to a dictionary
                var metadata = new Dictionary<string, string>();

                foreach (var key in response.Metadata.Keys)
                {
                    metadata[key] = response.Metadata[key];
                }

                _logger.LogInformation("Retrieved metadata for file: {FileName}, MetadataCount: {MetadataCount}",
                    fileName, metadata.Count);

                return metadata;
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("File not found when retrieving metadata: {FileName}", fileName);
                return new Dictionary<string, string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving metadata for file: {FileName}", fileName);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task ConfigureCorsAsync()
        {
            _logger.LogInformation("Configuring CORS for S3 bucket: {BucketName}", _bucketName);

            try
            {
                // Create a CORS configuration
                var corsConfiguration = new CORSConfiguration
                {
                    Rules = new List<CORSRule>
                    {
                        new CORSRule
                        {
                            AllowedHeaders = new List<string> { "*" },
                            AllowedMethods = new List<string> { "GET", "PUT", "POST", "DELETE", "HEAD" },
                            AllowedOrigins = new List<string>
                            {
                                "http://localhost:5000",
                                "https://localhost:5001",
                                "https://yendorcats.com",
                                "https://www.yendorcats.com"
                            },
                            ExposeHeaders = new List<string>
                            {
                                "ETag",
                                "x-amz-server-side-encryption",
                                "x-amz-request-id",
                                "x-amz-id-2"
                            },
                            MaxAgeSeconds = 3600 // 1 hour
                        }
                    }
                };

                // Create a request to set the CORS configuration
                var putCorsRequest = new PutCORSConfigurationRequest
                {
                    BucketName = _bucketName,
                    Configuration = corsConfiguration
                };

                // Set the CORS configuration
                await _s3Client.PutCORSConfigurationAsync(putCorsRequest);

                _logger.LogInformation("CORS configuration set for S3 bucket: {BucketName}", _bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error configuring CORS for S3 bucket: {BucketName}", _bucketName);
                throw;
            }
        }

        /// <summary>
        /// Get the URL for a file in S3
        /// </summary>
        /// <param name="fileName">The name of the file</param>
        /// <returns>The URL to the file</returns>
        private string GetFileUrl(string fileName)
        {
            // If using a CDN, return the CDN URL
            if (_useCdn && !string.IsNullOrEmpty(_cdnDomain))
            {
                return $"https://{_cdnDomain}/{fileName}";
            }

            // If using direct S3 URLs, use the public URL template
            if (_useDirectS3Urls && !string.IsNullOrEmpty(_publicUrlTemplate))
            {
                return _publicUrlTemplate
                    .Replace("{bucket}", _bucketName)
                    .Replace("{key}", fileName);
            }

            // Otherwise, return the standard S3 URL
            return $"https://{_bucketName}.s3.amazonaws.com/{fileName}";
        }
    }
}
