using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace YendorCats.API.Services
{
    public class S3StorageService : IS3StorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly ILogger<S3StorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _bucketName;
        private readonly string _cdnDomain;
        private readonly bool _useCdn;
        private readonly bool _useDirectS3Urls;
        private readonly string _serviceUrl;
        private readonly string _publicUrlTemplate;

        public S3StorageService(
            IAmazonS3 s3Client,
            IConfiguration configuration,
            ILogger<S3StorageService> logger)
        {
            _s3Client = s3Client;
            _logger = logger;
            _configuration = configuration;

            _bucketName = configuration["AWS:S3:BucketName"]
                ?? throw new ArgumentNullException("AWS:S3:BucketName", "S3 bucket name is not configured");

            _useDirectS3Urls = bool.TryParse(configuration["AWS:S3:UseDirectS3Urls"], out bool useDirectS3Urls) && useDirectS3Urls;
            _serviceUrl = configuration["AWS:S3:ServiceUrl"] ?? "https://s3.amazonaws.com";
            _publicUrlTemplate = configuration["AWS:S3:PublicUrl"] ?? "https://{bucket}.s3.amazonaws.com/{key}";
            _useCdn = bool.TryParse(configuration["AWS:S3:UseCdn"], out bool useCdn) && useCdn;
            _cdnDomain = configuration["AWS:S3:CdnDomain"] ?? string.Empty;

            if (!string.IsNullOrEmpty(_serviceUrl))
            {
                _logger.LogInformation("Using S3 client with endpoint: {ServiceUrl}", _serviceUrl);
            }

            _logger.LogInformation("S3StorageService initialized with bucket: {BucketName}, UseDirectS3Urls: {UseDirectS3Urls}, UseCDN: {UseCDN}",
                _bucketName, _useDirectS3Urls, _useCdn);
        }

        public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType)
        {
            _logger.LogInformation("Uploading file to S3: {FileName}, ContentType: {ContentType}",
                fileName, contentType);

            try
            {
                using var transferUtility = new TransferUtility(_s3Client);
                var uploadRequest = new TransferUtilityUploadRequest
                {
                    InputStream = fileStream,
                    BucketName = _bucketName,
                    Key = fileName,
                    ContentType = contentType,
                    CannedACL = S3CannedACL.PublicRead
                };
                await transferUtility.UploadAsync(uploadRequest);
                return GetFileUrl(fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to S3: {FileName}", fileName);
                throw;
            }
        }

        public async Task<string> UploadFileWithMetadataAsync(Stream fileStream, string fileName, string contentType, Dictionary<string, string> metadata)
        {
            _logger.LogInformation("Uploading file to S3 with metadata: {FileName}, ContentType: {ContentType}, MetadataCount: {MetadataCount}",
                fileName, contentType, metadata?.Count ?? 0);

            try
            {
                var putRequest = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = fileName,
                    InputStream = fileStream,
                    ContentType = contentType,
                    CannedACL = S3CannedACL.PublicRead
                };

                if (metadata != null && metadata.Count > 0)
                {
                    foreach (var kvp in metadata)
                    {
                        putRequest.Metadata.Add(kvp.Key, kvp.Value);
                    }
                }

                await _s3Client.PutObjectAsync(putRequest);
                return GetFileUrl(fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file with metadata to S3: {FileName}", fileName);
                throw;
            }
        }

        public async Task DeleteFileAsync(string fileName)
        {
            _logger.LogInformation("Deleting file from S3: {FileName}", fileName);

            try
            {
                var deleteRequest = new DeleteObjectRequest
                {
                    BucketName = _bucketName,
                    Key = fileName
                };
                await _s3Client.DeleteObjectAsync(deleteRequest);
                _logger.LogInformation("File deleted from S3: {FileName}", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file from S3: {FileName}", fileName);
                throw;
            }
        }

        public async Task<string> GetPreSignedUrlAsync(string fileName, int expiryMinutes = 60)
        {
            _logger.LogInformation("Generating pre-signed URL for file: {FileName}, ExpiryMinutes: {ExpiryMinutes}",
                fileName, expiryMinutes);

            try
            {
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _bucketName,
                    Key = fileName,
                    Expires = DateTime.UtcNow.AddMinutes(expiryMinutes)
                };
                var url = await Task.FromResult(_s3Client.GetPreSignedURL(request));
                _logger.LogInformation("Generated pre-signed URL for file: {FileName}", fileName);
                return url;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating pre-signed URL for file: {FileName}", fileName);
                throw;
            }
        }

        public async Task<Dictionary<string, string>> GetObjectMetadataAsync(string fileName)
        {
            _logger.LogInformation("Getting metadata for file: {FileName}", fileName);

            try
            {
                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = fileName
                };
                var response = await _s3Client.GetObjectMetadataAsync(request);
                var metadata = new Dictionary<string, string>();
                foreach (var key in response.Metadata.Keys)
                {
                    metadata[key] = response.Metadata[key];
                }
                _logger.LogInformation("Retrieved metadata for file: {FileName}, MetadataCount: {MetadataCount}",
                    fileName, metadata.Count);
                return metadata;
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("File not found when retrieving metadata: {FileName}", fileName);
                return new Dictionary<string, string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving metadata for file: {FileName}", fileName);
                throw;
            }
        }

        public async Task ConfigureCorsAsync()
        {
            _logger.LogInformation("Configuring CORS for S3 bucket: {BucketName}", _bucketName);

            try
            {
                var corsConfiguration = new CORSConfiguration
                {
                    Rules = new List<CORSRule>
                    {
                        new CORSRule
                        {
                            AllowedHeaders = new List<string> { "*" },
                            AllowedMethods = new List<string> { "GET", "PUT", "POST", "DELETE", "HEAD" },
                            AllowedOrigins = new List<string>
                            {
                                "http://localhost:5000",
                                "https://localhost:5001",
                                "https://yendorcats.com",
                                "https://www.yendorcats.com"
                            },
                            ExposeHeaders = new List<string>
                            {
                                "ETag",
                                "x-amz-server-side-encryption",
                                "x-amz-request-id",
                                "x-amz-id-2"
                            },
                            MaxAgeSeconds = 3600
                        }
                    }
                };
                var putCorsRequest = new PutCORSConfigurationRequest
                {
                    BucketName = _bucketName,
                    Configuration = corsConfiguration
                };
                await _s3Client.PutCORSConfigurationAsync(putCorsRequest);
                _logger.LogInformation("CORS configuration set for S3 bucket: {BucketName}", _bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error configuring CORS for S3 bucket: {BucketName}", _bucketName);
                throw;
            }
        }

        public async Task<List<S3Object>> ListFilesAsync(string prefix)
        {
            _logger.LogInformation("Listing files in S3 with prefix: {Prefix}", prefix);
            var request = new ListObjectsV2Request
            {
                BucketName = _bucketName,
                Prefix = prefix
            };
            var response = await _s3Client.ListObjectsV2Async(request);
            return response.S3Objects;
        }

        public async Task UpdateObjectMetadataAsync(string fileName, Dictionary<string, string> metadata)
        {
            _logger.LogInformation("Updating metadata for S3 object: {FileName}, MetadataCount: {MetadataCount}",
                fileName, metadata?.Count ?? 0);

            try
            {
                // Get current object to preserve existing content
                var getRequest = new GetObjectRequest
                {
                    BucketName = _bucketName,
                    Key = fileName
                };

                var getResponse = await _s3Client.GetObjectAsync(getRequest);

                // Create copy request with new metadata
                var copyRequest = new CopyObjectRequest
                {
                    SourceBucket = _bucketName,
                    SourceKey = fileName,
                    DestinationBucket = _bucketName,
                    DestinationKey = fileName,
                    ContentType = getResponse.Headers.ContentType,
                    CannedACL = S3CannedACL.PublicRead,
                    MetadataDirective = S3MetadataDirective.REPLACE
                };

                // Add new metadata
                if (metadata != null && metadata.Count > 0)
                {
                    foreach (var kvp in metadata)
                    {
                        copyRequest.Metadata.Add(kvp.Key, kvp.Value);
                    }
                }

                await _s3Client.CopyObjectAsync(copyRequest);
                _logger.LogInformation("Successfully updated metadata for S3 object: {FileName}", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating metadata for S3 object: {FileName}", fileName);
                throw;
            }
        }

        public async Task<Dictionary<string, object>> GetS3ConfigurationAsync()
        {
            _logger.LogInformation("Retrieving S3 configuration information");

            try
            {
                var config = new Dictionary<string, object>
                {
                    ["bucketName"] = _bucketName,
                    ["useCdn"] = _useCdn,
                    ["cdnDomain"] = _cdnDomain ?? string.Empty,
                    ["useDirectS3Urls"] = _useDirectS3Urls,
                    ["publicUrlTemplate"] = _publicUrlTemplate,
                    ["serviceUrl"] = _serviceUrl
                };

                // Get bucket location
                try
                {
                    var locationRequest = new GetBucketLocationRequest
                    {
                        BucketName = _bucketName
                    };
                    var locationResponse = await _s3Client.GetBucketLocationAsync(locationRequest);
                    config["region"] = locationResponse.Location.Value;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not retrieve bucket location");
                    config["region"] = "unknown";
                }

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving S3 configuration");
                throw;
            }
        }

        public async Task<List<S3ObjectWithMetadata>> SearchByMetadataAsync(Dictionary<string, string> metadataFilters, string prefix = "")
        {
            _logger.LogInformation("Searching S3 objects by metadata with {FilterCount} filters and prefix: {Prefix}",
                metadataFilters?.Count ?? 0, prefix);

            try
            {
                var results = new List<S3ObjectWithMetadata>();
                var allObjects = await ListFilesAsync(prefix);

                foreach (var s3Object in allObjects)
                {
                    try
                    {
                        var metadata = await GetObjectMetadataAsync(s3Object.Key);
                        var matches = true;

                        // Check if all metadata filters match
                        if (metadataFilters != null && metadataFilters.Count > 0)
                        {
                            foreach (var filter in metadataFilters)
                            {
                                if (!metadata.ContainsKey(filter.Key) || 
                                    !metadata[filter.Key].Equals(filter.Value, StringComparison.OrdinalIgnoreCase))
                                {
                                    matches = false;
                                    break;
                                }
                            }
                        }

                        if (matches)
                        {
                            results.Add(new S3ObjectWithMetadata
                            {
                                S3Object = s3Object,
                                Metadata = metadata,
                                PublicUrl = GetFileUrl(s3Object.Key)
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error processing metadata for object: {Key}", s3Object.Key);
                        // Continue processing other objects
                    }
                }

                _logger.LogInformation("Found {ResultCount} objects matching metadata criteria", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching objects by metadata");
                throw;
            }
        }

        private string GetFileUrl(string fileName)
        {
            if (_useCdn && !string.IsNullOrEmpty(_cdnDomain))
            {
                return $"https://{_cdnDomain}/{fileName}";
            }

            if (_useDirectS3Urls && !string.IsNullOrEmpty(_publicUrlTemplate))
            {
                return _publicUrlTemplate
                    .Replace("{bucket}", _bucketName)
                    .Replace("{key}", fileName);
            }

            return $"https://{_bucketName}.s3.amazonaws.com/{fileName}";
        }
    }
}
