using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.IO;
using YendorCats.API.Data;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for managing cat images
    /// </summary>
    public class ImageService : IImageService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<ImageService> _logger;
        private readonly IWebHostEnvironment _environment;
        // Temporarily removed IS3StorageService dependency for debugging
        // private readonly IS3StorageService _s3StorageService;
        private readonly IConfiguration _configuration;
        private readonly string _imageDirectory;
        private readonly bool _useS3Storage;

        /// <summary>
        /// Constructor for the image service
        /// </summary>
        /// <param name="context">Database context</param>
        /// <param name="logger">Logger for logging operations</param>
        /// <param name="environment">Web host environment for file paths</param>
        /// <param name="configuration">Application configuration</param>
        public ImageService(
            AppDbContext context,
            ILogger<ImageService> logger,
            IWebHostEnvironment environment,
            IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _environment = environment;
            // _s3StorageService = s3StorageService;
            _configuration = configuration;

            // Temporarily disable S3 storage for debugging
            _useS3Storage = false; // !string.IsNullOrEmpty(_configuration["AWS:S3:BucketName"]);

            // Create a directory path for storing images locally (fallback)
            _imageDirectory = Path.Combine(_environment.ContentRootPath, "Uploads", "Images");

            // Ensure the directory exists for local storage
            if (!Directory.Exists(_imageDirectory))
            {
                Directory.CreateDirectory(_imageDirectory);
                _logger.LogInformation("Created image upload directory: {Directory}", _imageDirectory);
            }

            _logger.LogInformation("ImageService initialized. Using S3 Storage: {UseS3Storage}", _useS3Storage);
        }

        /// <inheritdoc />
        public async Task<IEnumerable<CatImage>> GetImagesForCatAsync(int catId)
        {
            _logger.LogInformation("Retrieving images for cat with ID: {CatId}", catId);

            return await _context.CatImages
                .Where(i => i.CatId == catId)
                .OrderBy(i => i.DisplayOrder)
                .ToListAsync();
        }

        /// <inheritdoc />
        public async Task<CatImage?> GetImageByIdAsync(int id)
        {
            _logger.LogInformation("Retrieving image with ID: {ImageId}", id);

            return await _context.CatImages.FindAsync(id);
        }

        /// <inheritdoc />
        public async Task<CatImage?> GetPrimaryImageForCatAsync(int catId)
        {
            _logger.LogInformation("Retrieving primary image for cat with ID: {CatId}", catId);

            return await _context.CatImages
                .FirstOrDefaultAsync(i => i.CatId == catId && i.IsPrimary);
        }

        /// <inheritdoc />
        public async Task<CatImage> AddImageAsync(int catId, string fileName, string contentType, Stream fileStream, string? caption = null, bool isPrimary = false)
        {
            _logger.LogInformation("Adding new image for cat with ID: {CatId}. IsPrimary: {IsPrimary}", catId, isPrimary);

            // Verify cat exists
            var cat = await _context.Cats.FindAsync(catId)
                ?? throw new KeyNotFoundException($"Cat with ID {catId} not found");

            // If this is set as primary, reset any existing primary images
            if (isPrimary)
            {
                await ResetPrimaryFlagForCatAsync(catId);
            }

            // Generate a unique filename
            var uniqueFileName = $"{Guid.NewGuid()}_{Path.GetFileName(fileName)}";

            // Save the file to disk
            var filePath = await SaveImageFileAsync(uniqueFileName, fileStream);

            // Create image metadata record
            var image = new CatImage
            {
                CatId = catId,
                FileName = uniqueFileName,
                FilePath = filePath,
                ContentType = contentType,
                Caption = caption,
                IsPrimary = isPrimary,
                FileSize = fileStream.Length,
                UploadedAt = DateTime.UtcNow,
                DisplayOrder = await GetNextDisplayOrderAsync(catId)
            };

            await _context.CatImages.AddAsync(image);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Image added with ID: {ImageId} for cat: {CatId}", image.Id, catId);
            return image;
        }

        /// <inheritdoc />
        public async Task UpdateImageAsync(CatImage image)
        {
            _logger.LogInformation("Updating image with ID: {ImageId}", image.Id);

            var existingImage = await _context.CatImages.FindAsync(image.Id)
                ?? throw new KeyNotFoundException($"Image with ID {image.Id} not found");

            // If setting this as primary, reset other primary images
            if (image.IsPrimary && !existingImage.IsPrimary)
            {
                await ResetPrimaryFlagForCatAsync(existingImage.CatId);
            }

            // Only update specific fields
            existingImage.Caption = image.Caption;
            existingImage.IsPrimary = image.IsPrimary;
            existingImage.DisplayOrder = image.DisplayOrder;

            _context.CatImages.Update(existingImage);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Image updated: {ImageId}", image.Id);
        }

        /// <inheritdoc />
        public async Task DeleteImageAsync(int id)
        {
            _logger.LogInformation("Deleting image with ID: {ImageId}", id);

            var image = await _context.CatImages.FindAsync(id)
                ?? throw new KeyNotFoundException($"Image with ID {id} not found");

            // Delete the file first
            if (File.Exists(image.FilePath))
            {
                await DeleteImageFileAsync(image.FilePath);
            }

            // Remove from database
            _context.CatImages.Remove(image);
            await _context.SaveChangesAsync();

            // If this was a primary image, set a new primary image if possible
            if (image.IsPrimary)
            {
                await SetNewPrimaryImageAsync(image.CatId);
            }

            _logger.LogInformation("Image deleted: {ImageId}", id);
        }

        /// <inheritdoc />
        public async Task SetAsPrimaryAsync(int imageId)
        {
            _logger.LogInformation("Setting image with ID: {ImageId} as primary", imageId);

            var image = await _context.CatImages.FindAsync(imageId)
                ?? throw new KeyNotFoundException($"Image with ID {imageId} not found");

            // Reset any existing primary images
            await ResetPrimaryFlagForCatAsync(image.CatId);

            // Set this image as primary
            image.IsPrimary = true;
            _context.CatImages.Update(image);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Image set as primary: {ImageId} for cat: {CatId}", imageId, image.CatId);
        }

        /// <inheritdoc />
        public async Task<string?> GetImageFilePathAsync(int imageId)
        {
            var image = await _context.CatImages.FindAsync(imageId);
            return image?.FilePath;
        }

        /// <inheritdoc />
        public async Task<string> SaveImageFileAsync(string fileName, Stream fileStream)
        {
            _logger.LogInformation("Saving image file: {FileName}, Using S3: {UseS3Storage}", fileName, _useS3Storage);

            if (_useS3Storage)
            {
                try
                {
                    // Determine content type based on file extension
                    string contentType = GetContentTypeFromFileName(fileName);

                    // Upload to S3 - temporarily disabled for debugging
                    // string s3Url = await _s3StorageService.UploadFileAsync(fileStream, fileName, contentType);
                    // _logger.LogInformation("File uploaded to S3: {FileName}, URL: {S3Url}", fileName, s3Url);
                    // return s3Url;
                    throw new NotImplementedException("S3 upload temporarily disabled for debugging");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error uploading to S3, falling back to local storage: {FileName}", fileName);
                    // Fall back to local storage on error
                }
            }

            // Local storage (fallback)
            var filePath = Path.Combine(_imageDirectory, fileName);

            using (var fileStream2 = new FileStream(filePath, FileMode.Create))
            {
                await fileStream.CopyToAsync(fileStream2);
            }

            _logger.LogInformation("File saved locally: {FilePath}", filePath);
            return filePath;
        }

        /// <inheritdoc />
        public async Task DeleteImageFileAsync(string filePath)
        {
            _logger.LogInformation("Deleting image file: {FilePath}", filePath);

            // Check if this is an S3 URL
            if (_useS3Storage && IsS3Url(filePath))
            {
                try
                {
                    // Extract the file name from the S3 URL
                    string fileName = ExtractFileNameFromS3Url(filePath);

                    // Delete from S3 - temporarily disabled for debugging
                    // await _s3StorageService.DeleteFileAsync(fileName);
                    _logger.LogWarning("S3 delete temporarily disabled for debugging: {FileName}", fileName);

                    _logger.LogInformation("File deleted from S3: {FileName}", fileName);
                    return;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error deleting from S3: {FilePath}", filePath);
                    // Continue to try local deletion as fallback
                }
            }

            // Local file deletion (fallback)
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                _logger.LogInformation("File deleted locally: {FilePath}", filePath);
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Reset the primary flag for all images of a cat
        /// </summary>
        /// <param name="catId">The ID of the cat</param>
        /// <returns>Task representing the asynchronous operation</returns>
        private async Task ResetPrimaryFlagForCatAsync(int catId)
        {
            _logger.LogInformation("Resetting primary flag for all images of cat: {CatId}", catId);

            var images = await _context.CatImages
                .Where(i => i.CatId == catId && i.IsPrimary)
                .ToListAsync();

            foreach (var image in images)
            {
                image.IsPrimary = false;
                _context.CatImages.Update(image);
            }

            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// Set a new primary image for a cat after the current primary is deleted
        /// </summary>
        /// <param name="catId">The ID of the cat</param>
        /// <returns>Task representing the asynchronous operation</returns>
        private async Task SetNewPrimaryImageAsync(int catId)
        {
            _logger.LogInformation("Setting new primary image for cat: {CatId}", catId);

            // Get the first available image
            var firstImage = await _context.CatImages
                .Where(i => i.CatId == catId)
                .OrderBy(i => i.DisplayOrder)
                .FirstOrDefaultAsync();

            if (firstImage != null)
            {
                firstImage.IsPrimary = true;
                _context.CatImages.Update(firstImage);
                await _context.SaveChangesAsync();

                _logger.LogInformation("New primary image set: {ImageId} for cat: {CatId}", firstImage.Id, catId);
            }
        }

        /// <summary>
        /// Get the next display order value for a cat's images
        /// </summary>
        /// <param name="catId">The ID of the cat</param>
        /// <returns>The next display order value</returns>
        private async Task<int> GetNextDisplayOrderAsync(int catId)
        {
            var maxOrder = await _context.CatImages
                .Where(i => i.CatId == catId)
                .Select(i => (int?)i.DisplayOrder)
                .MaxAsync() ?? 0;

            return maxOrder + 1;
        }

        /// <summary>
        /// Check if a file path is an S3 URL
        /// </summary>
        /// <param name="filePath">The file path to check</param>
        /// <returns>True if the file path is an S3 URL, false otherwise</returns>
        private bool IsS3Url(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            // Check for standard S3 URL
            if (filePath.Contains("s3.amazonaws.com"))
                return true;

            // Check for custom S3 endpoint
            var serviceUrl = _configuration["AWS:S3:ServiceUrl"];
            if (!string.IsNullOrEmpty(serviceUrl) && filePath.Contains(serviceUrl))
                return true;

            // Check for CDN domain
            if (!string.IsNullOrEmpty(_configuration["AWS:S3:CdnDomain"]) &&
                filePath.Contains(_configuration["AWS:S3:CdnDomain"]))
                return true;

            // Check for bucket name in URL (common pattern)
            var bucketName = _configuration["AWS:S3:BucketName"];
            if (!string.IsNullOrEmpty(bucketName) && filePath.Contains(bucketName))
                return true;

            return false;
        }

        /// <summary>
        /// Extract the file name from an S3 URL
        /// </summary>
        /// <param name="s3Url">The S3 URL</param>
        /// <returns>The file name</returns>
        private string ExtractFileNameFromS3Url(string s3Url)
        {
            if (string.IsNullOrEmpty(s3Url))
                return string.Empty;

            // Handle URL encoded characters
            s3Url = Uri.UnescapeDataString(s3Url);

            // Remove query parameters if present
            int queryIndex = s3Url.IndexOf('?');
            if (queryIndex > 0)
            {
                s3Url = s3Url.Substring(0, queryIndex);
            }

            // Get the last part of the URL after the last slash
            return s3Url.Substring(s3Url.LastIndexOf('/') + 1);
        }

        /// <summary>
        /// Get the content type from a file name
        /// </summary>
        /// <param name="fileName">The file name</param>
        /// <returns>The content type</returns>
        private string GetContentTypeFromFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "application/octet-stream";

            string extension = Path.GetExtension(fileName).ToLowerInvariant();

            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                ".tiff" or ".tif" => "image/tiff",
                ".svg" => "image/svg+xml",
                _ => "application/octet-stream"
            };
        }
    }
}