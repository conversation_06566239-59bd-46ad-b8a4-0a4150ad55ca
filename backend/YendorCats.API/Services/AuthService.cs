using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using YendorCats.API.Configuration;
using YendorCats.API.Data;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for handling authentication and authorization
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<AuthService> _logger;
        private readonly JwtSettings _jwtSettings;
        // Temporarily removed ISecretsManagerService dependency for debugging
        // private readonly ISecretsManagerService _secretsManager;
        private readonly IWebHostEnvironment _environment;

        /// <summary>
        /// Constructor for the authentication service
        /// </summary>
        /// <param name="context">Database context</param>
        /// <param name="logger">Logger for logging operations</param>
        /// <param name="jwtSettings">JWT configuration settings</param>
        /// <param name="environment">Web host environment</param>
        public AuthService(
            AppDbContext context,
            ILogger<AuthService> logger,
            IOptions<JwtSettings> jwtSettings,
            IWebHostEnvironment environment)
        {
            _context = context;
            _logger = logger;
            _jwtSettings = jwtSettings.Value;
            // _secretsManager = secretsManager;
            _environment = environment;
        }

        /// <inheritdoc />
        public async Task<(string token, string refreshToken)> AuthenticateAsync(string username, string password)
        {
            _logger.LogInformation("Authenticating user: {Username}", username);
            
            var user = await _context.Users.FirstOrDefaultAsync(u => 
                u.Username.ToLower() == username.ToLower() && u.IsActive);
            
            if (user == null)
            {
                _logger.LogWarning("Authentication failed: User not found or inactive: {Username}", username);
                throw new UnauthorizedAccessException("Invalid username or password");
            }
            
            if (!VerifyPassword(password, user.PasswordHash, user.PasswordSalt))
            {
                _logger.LogWarning("Authentication failed: Invalid password for user: {Username}", username);
                throw new UnauthorizedAccessException("Invalid username or password");
            }
            
            // Update last login time
            user.LastLoginAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            
            // Generate tokens
            var token = GenerateJwtToken(user);
            var refreshToken = await GenerateRefreshTokenAsync(user.Id);
            
            _logger.LogInformation("User authenticated successfully: {Username}", username);
            return (token, refreshToken);
        }

        /// <inheritdoc />
        public bool ValidateToken(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogWarning("Token validation failed: Token is null or empty");
                return false;
            }
            
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var tokenValidationParameters = GetTokenValidationParameters();
                
                tokenHandler.ValidateToken(token, tokenValidationParameters, out var validatedToken);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Token validation failed");
                return false;
            }
        }

        /// <inheritdoc />
        public async Task<string> GenerateRefreshTokenAsync(int userId)
        {
            _logger.LogInformation("Generating refresh token for user: {UserId}", userId);
            
            var user = await _context.Users.FindAsync(userId) 
                ?? throw new KeyNotFoundException($"User with ID {userId} not found");
            
            // Generate a random token
            var randomNumber = new byte[32];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomNumber);
            var refreshToken = Convert.ToBase64String(randomNumber);
            
            // Save to the database
            user.RefreshToken = refreshToken;
            
            // Get refresh token expiry days from the appropriate settings source
            int refreshTokenExpiryDays;
            if (_environment.IsDevelopment())
            {
                refreshTokenExpiryDays = _jwtSettings.RefreshTokenExpiryDays;
            }
            else
            {
                // Temporarily use configuration values instead of secrets manager
                refreshTokenExpiryDays = _jwtSettings.RefreshTokenExpiryDays;
                _logger.LogInformation("Using refresh token expiry days from configuration: {Days}", refreshTokenExpiryDays);
            }
            
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(refreshTokenExpiryDays);
            
            await _context.SaveChangesAsync();
            
            return refreshToken;
        }

        /// <inheritdoc />
        public async Task<(string token, string refreshToken)> RefreshTokenAsync(string refreshToken)
        {
            _logger.LogInformation("Processing refresh token request");
            
            var user = await _context.Users.FirstOrDefaultAsync(u => 
                u.RefreshToken == refreshToken && 
                u.RefreshTokenExpiryTime > DateTime.UtcNow &&
                u.IsActive);
            
            if (user == null)
            {
                _logger.LogWarning("Refresh token invalid or expired");
                throw new UnauthorizedAccessException("Invalid or expired refresh token");
            }
            
            // Generate new tokens
            var newJwtToken = GenerateJwtToken(user);
            var newRefreshToken = await GenerateRefreshTokenAsync(user.Id);
            
            _logger.LogInformation("Refresh token process completed successfully for user: {UserId}", user.Id);
            return (newJwtToken, newRefreshToken);
        }

        /// <inheritdoc />
        public async Task RevokeRefreshTokenAsync(int userId)
        {
            _logger.LogInformation("Revoking refresh token for user: {UserId}", userId);
            
            var user = await _context.Users.FindAsync(userId) 
                ?? throw new KeyNotFoundException($"User with ID {userId} not found");
            
            user.RefreshToken = null;
            user.RefreshTokenExpiryTime = null;
            
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Refresh token revoked for user: {UserId}", userId);
        }

        /// <inheritdoc />
        public int GetUserIdFromToken(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadJwtToken(token);
            
            var userIdClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                throw new InvalidOperationException("Invalid token: user ID claim missing or invalid");
            }
            
            return userId;
        }

        /// <inheritdoc />
        public string GetUsernameFromToken(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadJwtToken(token);
            
            var usernameClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Name);
            if (usernameClaim == null)
            {
                throw new InvalidOperationException("Invalid token: username claim missing");
            }
            
            return usernameClaim.Value;
        }

        /// <inheritdoc />
        public string GetRoleFromToken(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadJwtToken(token);
            
            var roleClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Role);
            if (roleClaim == null)
            {
                throw new InvalidOperationException("Invalid token: role claim missing");
            }
            
            return roleClaim.Value;
        }

        /// <inheritdoc />
        public (string passwordHash, string passwordSalt) HashPassword(string password)
        {
            using var hmac = new HMACSHA512();
            var salt = Convert.ToBase64String(hmac.Key);
            var hash = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(password)));
            
            return (hash, salt);
        }

        /// <inheritdoc />
        public bool VerifyPassword(string password, string storedHash, string storedSalt)
        {
            var saltBytes = Convert.FromBase64String(storedSalt);
            using var hmac = new HMACSHA512(saltBytes);
            var computedHash = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(password)));
            
            return computedHash == storedHash;
        }

        /// <summary>
        /// Generates a JWT token for the specified user
        /// </summary>
        /// <param name="user">The user to generate a token for</param>
        /// <returns>A JWT token string</returns>
        private string GenerateJwtToken(User user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new(ClaimTypes.Name, user.Username),
                new(ClaimTypes.Email, user.Email),
                new(ClaimTypes.Role, user.Role)
            };
            
            string jwtSecret;
            string jwtIssuer;
            string jwtAudience;
            int expiryMinutes;
            
            if (_environment.IsDevelopment())
            {
                // Use configuration values in development
                jwtSecret = _jwtSettings.Secret;
                jwtIssuer = _jwtSettings.Issuer;
                jwtAudience = _jwtSettings.Audience;
                expiryMinutes = _jwtSettings.ExpiryMinutes;
            }
            else
            {
                // Temporarily use configuration values instead of secrets manager
                jwtSecret = _jwtSettings.Secret;
                jwtIssuer = _jwtSettings.Issuer;
                jwtAudience = _jwtSettings.Audience;
                expiryMinutes = _jwtSettings.ExpiryMinutes;
                _logger.LogInformation("Using JWT settings from configuration in production mode");
            }
            
            var key = Encoding.ASCII.GetBytes(jwtSecret);
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(expiryMinutes),
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                Issuer = jwtIssuer,
                Audience = jwtAudience
            };
            
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
        
        /// <summary>
        /// Gets the token validation parameters with the appropriate configuration
        /// </summary>
        /// <returns>Token validation parameters</returns>
        private TokenValidationParameters GetTokenValidationParameters()
        {
            string jwtSecret;
            string jwtIssuer;
            string jwtAudience;
            
            if (_environment.IsDevelopment())
            {
                // Use configuration values in development
                jwtSecret = _jwtSettings.Secret;
                jwtIssuer = _jwtSettings.Issuer;
                jwtAudience = _jwtSettings.Audience;
            }
            else
            {
                // Temporarily use configuration values instead of secrets manager
                jwtSecret = _jwtSettings.Secret;
                jwtIssuer = _jwtSettings.Issuer;
                jwtAudience = _jwtSettings.Audience;
                _logger.LogInformation("Using JWT settings from configuration for token validation");
            }
            
            var key = Encoding.ASCII.GetBytes(jwtSecret);
            return new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = !string.IsNullOrEmpty(jwtIssuer),
                ValidIssuer = jwtIssuer,
                ValidateAudience = !string.IsNullOrEmpty(jwtAudience),
                ValidAudience = jwtAudience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };
        }
    }
} 