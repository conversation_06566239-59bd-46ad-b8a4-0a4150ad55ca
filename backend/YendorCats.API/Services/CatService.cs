using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for managing cats in the system
    /// </summary>
    public class CatService : ICatService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<CatService> _logger;

        /// <summary>
        /// Constructor for the cat service
        /// </summary>
        /// <param name="context">Database context</param>
        /// <param name="logger">Logger for logging operations</param>
        public CatService(AppDbContext context, ILogger<CatService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Cat>> GetAllCatsAsync(bool includeImages = false, bool includeInactive = false)
        {
            _logger.LogInformation("Retrieving all cats. IncludeImages: {IncludeImages}, IncludeInactive: {IncludeInactive}", 
                includeImages, includeInactive);
            
            IQueryable<Cat> query = _context.Cats;
            
            // Apply filtering for inactive cats
            if (!includeInactive)
            {
                query = query.Where(c => c.IsActive);
            }
            
            // Include images if requested
            if (includeImages)
            {
                query = query.Include(c => c.Images);
            }
            
            return await query.ToListAsync();
        }

        /// <inheritdoc />
        public async Task<Cat?> GetCatByIdAsync(int id, bool includeImages = true)
        {
            _logger.LogInformation("Retrieving cat with ID: {CatId}. IncludeImages: {IncludeImages}", id, includeImages);
            
            IQueryable<Cat> query = _context.Cats;
            
            // Include images if requested
            if (includeImages)
            {
                query = query.Include(c => c.Images);
            }
            
            return await query.FirstOrDefaultAsync(c => c.Id == id);
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Cat>> GetCatsByBreedAsync(string breed, bool includeImages = false, bool includeInactive = false)
        {
            _logger.LogInformation("Retrieving cats by breed: {Breed}. IncludeImages: {IncludeImages}, IncludeInactive: {IncludeInactive}", 
                breed, includeImages, includeInactive);
            
            IQueryable<Cat> query = _context.Cats.Where(c => c.Breed.ToLower() == breed.ToLower());
            
            // Apply filtering for inactive cats
            if (!includeInactive)
            {
                query = query.Where(c => c.IsActive);
            }
            
            // Include images if requested
            if (includeImages)
            {
                query = query.Include(c => c.Images);
            }
            
            return await query.ToListAsync();
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Cat>> GetCatsByAvailabilityAsync(bool available, bool includeImages = false)
        {
            _logger.LogInformation("Retrieving cats by availability: {Available}. IncludeImages: {IncludeImages}", 
                available, includeImages);
            
            IQueryable<Cat> query = _context.Cats
                .Where(c => c.IsAvailable == available && c.IsActive);
            
            // Include images if requested
            if (includeImages)
            {
                query = query.Include(c => c.Images);
            }
            
            return await query.ToListAsync();
        }

        /// <inheritdoc />
        public async Task<Cat> AddCatAsync(Cat cat)
        {
            _logger.LogInformation("Adding new cat: {CatName}", cat.Name);
            
            // Set default values for new cat
            cat.CreatedAt = DateTime.UtcNow;
            cat.IsActive = true;
            
            await _context.Cats.AddAsync(cat);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Cat added with ID: {CatId}", cat.Id);
            return cat;
        }

        /// <inheritdoc />
        public async Task UpdateCatAsync(Cat cat)
        {
            _logger.LogInformation("Updating cat with ID: {CatId}", cat.Id);
            
            var existingCat = await _context.Cats.FindAsync(cat.Id) 
                ?? throw new KeyNotFoundException($"Cat with ID {cat.Id} not found");
            
            // Update properties
            existingCat.Name = cat.Name;
            existingCat.Breed = cat.Breed;
            existingCat.DateOfBirth = cat.DateOfBirth;
            existingCat.Gender = cat.Gender;
            existingCat.Color = cat.Color;
            existingCat.Description = cat.Description;
            existingCat.Price = cat.Price;
            existingCat.IsAvailable = cat.IsAvailable;
            existingCat.Markings = cat.Markings;
            existingCat.UpdatedAt = DateTime.UtcNow;
            existingCat.MotherId = cat.MotherId;
            existingCat.FatherId = cat.FatherId;
            
            _context.Cats.Update(existingCat);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Cat updated: {CatId}", cat.Id);
        }

        /// <inheritdoc />
        public async Task DeleteCatAsync(int id)
        {
            _logger.LogInformation("Deleting cat with ID: {CatId}", id);
            
            var cat = await _context.Cats.FindAsync(id) 
                ?? throw new KeyNotFoundException($"Cat with ID {id} not found");
            
            // Check for related images
            var hasImages = await _context.CatImages.AnyAsync(i => i.CatId == id);
            if (hasImages)
            {
                _logger.LogWarning("Cat with ID: {CatId} has related images. Setting IsActive to false instead of deleting", id);
                
                // Soft delete if there are related images
                cat.IsActive = false;
                cat.UpdatedAt = DateTime.UtcNow;
                
                _context.Cats.Update(cat);
            }
            else
            {
                // Hard delete if there are no related entities
                _context.Cats.Remove(cat);
            }
            
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Cat deleted: {CatId}", id);
        }

        /// <inheritdoc />
        public async Task SetAvailabilityAsync(int id, bool isAvailable)
        {
            _logger.LogInformation("Setting availability for cat with ID: {CatId} to {IsAvailable}", 
                id, isAvailable);
            
            var cat = await _context.Cats.FindAsync(id) 
                ?? throw new KeyNotFoundException($"Cat with ID {id} not found");
            
            cat.IsAvailable = isAvailable;
            cat.UpdatedAt = DateTime.UtcNow;
            
            _context.Cats.Update(cat);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Cat availability updated: {CatId}", id);
        }

        /// <inheritdoc />
        public async Task SetActiveStatusAsync(int id, bool isActive)
        {
            _logger.LogInformation("Setting active status for cat with ID: {CatId} to {IsActive}", 
                id, isActive);
            
            var cat = await _context.Cats.FindAsync(id) 
                ?? throw new KeyNotFoundException($"Cat with ID {id} not found");
            
            cat.IsActive = isActive;
            cat.UpdatedAt = DateTime.UtcNow;
            
            _context.Cats.Update(cat);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Cat active status updated: {CatId}", id);
        }

        /// <inheritdoc />
        public async Task<IEnumerable<string>> GetAllBreedsAsync()
        {
            _logger.LogInformation("Retrieving all unique cat breeds");
            
            return await _context.Cats
                .Where(c => c.IsActive)
                .Select(c => c.Breed)
                .Distinct()
                .OrderBy(b => b)
                .ToListAsync();
        }
    }
} 