using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for extracting metadata from image files
    /// </summary>
    public interface IImageMetadataService
    {
        /// <summary>
        /// Extract IPTC metadata from an image stream
        /// </summary>
        /// <param name="imageStream">The image stream to extract metadata from</param>
        /// <param name="fileName">The filename for context</param>
        /// <returns>Dictionary containing extracted IPTC metadata</returns>
        Task<Dictionary<string, string>> ExtractIptcMetadataAsync(Stream imageStream, string fileName);

        /// <summary>
        /// Extract EXIF metadata from an image stream
        /// </summary>
        /// <param name="imageStream">The image stream to extract metadata from</param>
        /// <param name="fileName">The filename for context</param>
        /// <returns>Dictionary containing extracted EXIF metadata</returns>
        Task<Dictionary<string, string>> ExtractExifMetadataAsync(Stream imageStream, string fileName);

        /// <summary>
        /// Extract all available metadata from an image stream
        /// </summary>
        /// <param name="imageStream">The image stream to extract metadata from</param>
        /// <param name="fileName">The filename for context</param>
        /// <returns>Dictionary containing all extracted metadata</returns>
        Task<Dictionary<string, string>> ExtractAllMetadataAsync(Stream imageStream, string fileName);

        /// <summary>
        /// Extract cat-specific metadata from IPTC fields
        /// </summary>
        /// <param name="imageStream">The image stream to extract metadata from</param>
        /// <param name="fileName">The filename for context</param>
        /// <returns>CatImageMetadata object with extracted data</returns>
        Task<CatImageMetadata> ExtractCatMetadataAsync(Stream imageStream, string fileName);
    }
}
