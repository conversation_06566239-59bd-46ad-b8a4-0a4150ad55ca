using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for cat gallery operations
    /// </summary>
    public interface ICatGalleryService
    {
        /// <summary>
        /// Get all cat gallery images from all categories or a specific category
        /// </summary>
        /// <param name="category">Optional category filter (studs, queens, kittens, gallery)</param>
        /// <returns>A list of cat gallery images with metadata</returns>
        Task<IEnumerable<CatGalleryImage>> GetCatImagesAsync(string? category = null);
        
        /// <summary>
        /// Get cat gallery images for a specific category, ordered by the specified criteria
        /// </summary>
        /// <param name="category">Category to scan (studs, queens, kittens, gallery)</param>
        /// <param name="orderBy">Order by field (date, name, age)</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>A list of sorted cat gallery images</returns>
        Task<IEnumerable<CatGalleryImage>> GetCategoryImagesAsync(string category, string orderBy = "date", bool descending = true);
        
        /// <summary>
        /// Upload a new gallery image
        /// </summary>
        /// <param name="category">The category for the image</param>
        /// <param name="fileName">The filename with metadata</param>
        /// <param name="contentType">The content type</param>
        /// <param name="fileStream">The file stream</param>
        /// <returns>The uploaded image information</returns>
        Task<CatGalleryImage> UploadGalleryImageAsync(string category, string fileName, string contentType, Stream fileStream);
    }
}
