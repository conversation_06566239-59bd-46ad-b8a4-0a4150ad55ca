using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for admin authentication services
    /// </summary>
    public interface IAdminAuthService
    {
        /// <summary>
        /// Authenticate admin user with username/email and password
        /// </summary>
        /// <param name="username">Username or email</param>
        /// <param name="password">Plain text password</param>
        /// <returns>Admin login response with token if successful</returns>
        Task<AdminLoginResponse> AuthenticateAsync(string username, string password);

        /// <summary>
        /// Create a new admin user
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="email">Email address</param>
        /// <param name="password">Plain text password</param>
        /// <param name="role">Admin role</param>
        /// <returns>Created admin user</returns>
        Task<AdminUser> CreateAdminAsync(string username, string email, string password, string role = AdminRoles.Admin);

        /// <summary>
        /// Validate JWT token and get admin user
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>Admin user if token is valid</returns>
        Task<AdminUser?> ValidateTokenAsync(string token);

        /// <summary>
        /// Generate JWT token for admin user
        /// </summary>
        /// <param name="adminUser">Admin user</param>
        /// <returns>JWT token</returns>
        string GenerateJwtToken(AdminUser adminUser);

        /// <summary>
        /// Hash password with salt
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <param name="salt">Salt (generated if null)</param>
        /// <returns>Tuple of hashed password and salt</returns>
        (string hashedPassword, string salt) HashPassword(string password, string? salt = null);

        /// <summary>
        /// Verify password against hash
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <param name="hashedPassword">Stored hash</param>
        /// <param name="salt">Stored salt</param>
        /// <returns>True if password matches</returns>
        bool VerifyPassword(string password, string hashedPassword, string salt);

        /// <summary>
        /// Check if admin user exists
        /// </summary>
        /// <param name="username">Username or email</param>
        /// <returns>True if user exists</returns>
        Task<bool> AdminExistsAsync(string username);

        /// <summary>
        /// Initialize default admin user if none exist
        /// </summary>
        /// <returns>True if default admin was created</returns>
        Task<bool> InitializeDefaultAdminAsync();
    }
}
