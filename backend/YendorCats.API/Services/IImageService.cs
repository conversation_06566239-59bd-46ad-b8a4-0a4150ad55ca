using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for image-related operations
    /// </summary>
    public interface IImageService
    {
        /// <summary>
        /// Get all images for a cat
        /// </summary>
        /// <param name="catId">The ID of the cat</param>
        /// <returns>All images for the cat</returns>
        Task<IEnumerable<CatImage>> GetImagesForCatAsync(int catId);
        
        /// <summary>
        /// Get an image by ID
        /// </summary>
        /// <param name="id">The ID of the image to retrieve</param>
        /// <returns>The requested image or null if not found</returns>
        Task<CatImage?> GetImageByIdAsync(int id);
        
        /// <summary>
        /// Get the primary image for a cat
        /// </summary>
        /// <param name="catId">The ID of the cat</param>
        /// <returns>The primary image for the cat or null if not found</returns>
        Task<CatImage?> GetPrimaryImageForCatAsync(int catId);
        
        /// <summary>
        /// Add a new image
        /// </summary>
        /// <param name="catId">The ID of the cat</param>
        /// <param name="fileName">The filename</param>
        /// <param name="contentType">The content type (MIME type)</param>
        /// <param name="fileStream">The file stream</param>
        /// <param name="caption">Optional caption</param>
        /// <param name="isPrimary">Whether this is the primary image</param>
        /// <returns>The added image with its new ID</returns>
        Task<CatImage> AddImageAsync(int catId, string fileName, string contentType, Stream fileStream, string? caption = null, bool isPrimary = false);
        
        /// <summary>
        /// Update an existing image
        /// </summary>
        /// <param name="image">The updated image data</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task UpdateImageAsync(CatImage image);
        
        /// <summary>
        /// Delete an image
        /// </summary>
        /// <param name="id">The ID of the image to delete</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task DeleteImageAsync(int id);
        
        /// <summary>
        /// Set an image as the primary image for a cat
        /// </summary>
        /// <param name="imageId">The ID of the image</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task SetAsPrimaryAsync(int imageId);
        
        /// <summary>
        /// Get the physical file path for an image
        /// </summary>
        /// <param name="imageId">The ID of the image</param>
        /// <returns>The physical file path for the image or null if not found</returns>
        Task<string?> GetImageFilePathAsync(int imageId);
        
        /// <summary>
        /// Save an image file to disk
        /// </summary>
        /// <param name="fileName">The filename</param>
        /// <param name="fileStream">The file stream</param>
        /// <returns>The file path where the image was saved</returns>
        Task<string> SaveImageFileAsync(string fileName, Stream fileStream);
        
        /// <summary>
        /// Delete an image file from disk
        /// </summary>
        /// <param name="filePath">The file path of the image to delete</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task DeleteImageFileAsync(string filePath);
    }
} 