using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for managing photo index without requiring S3 credentials for public access
    /// </summary>
    public interface IPhotoIndexService
    {
        /// <summary>
        /// Get all photos for a category using the static index
        /// </summary>
        /// <param name="category">Category name (studs, queens, kittens, gallery)</param>
        /// <returns>List of cat gallery images</returns>
        Task<List<CatGalleryImage>> GetCategoryPhotosAsync(string category);

        /// <summary>
        /// Get photos for a specific cat using the static index
        /// </summary>
        /// <param name="catName">Name of the cat</param>
        /// <returns>List of photos for the cat</returns>
        Task<List<CatGalleryImage>> GetCatPhotosAsync(string catName);

        /// <summary>
        /// Get random featured photos from the index
        /// </summary>
        /// <param name="count">Number of photos to return</param>
        /// <returns>Random selection of photos</returns>
        Task<List<CatGalleryImage>> GetFeaturedPhotosAsync(int count);

        /// <summary>
        /// Refresh the photo index from S3 (admin only - requires credentials)
        /// </summary>
        /// <returns>True if refresh was successful</returns>
        Task<bool> RefreshIndexAsync();

        /// <summary>
        /// Add a new photo to the index (called after upload)
        /// </summary>
        /// <param name="category">Category</param>
        /// <param name="catName">Cat name</param>
        /// <param name="fileName">File name</param>
        /// <param name="metadata">Photo metadata</param>
        /// <returns>True if added successfully</returns>
        Task<bool> AddPhotoToIndexAsync(string category, string catName, string fileName, Dictionary<string, string>? metadata = null);

        /// <summary>
        /// Remove a photo from the index (called after deletion)
        /// </summary>
        /// <param name="category">Category</param>
        /// <param name="catName">Cat name</param>
        /// <param name="fileName">File name</param>
        /// <returns>True if removed successfully</returns>
        Task<bool> RemovePhotoFromIndexAsync(string category, string catName, string fileName);
    }

    /// <summary>
    /// Photo index data structure
    /// </summary>
    public class PhotoIndex
    {
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, Dictionary<string, List<string>>> Categories { get; set; } = new();
        public Dictionary<string, PhotoMetadata> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Photo metadata structure
    /// </summary>
    public class PhotoMetadata
    {
        public string CatName { get; set; } = string.Empty;
        public string Age { get; set; } = "0";
        public string Description { get; set; } = string.Empty;
        public string Breed { get; set; } = "Maine Coon";
        public string Gender { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public string DateTaken { get; set; } = string.Empty;
        public string Personality { get; set; } = string.Empty;
        public string Bloodline { get; set; } = string.Empty;
    }
}
