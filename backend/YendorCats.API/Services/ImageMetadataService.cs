using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MetadataExtractor;
using MetadataExtractor.Formats.Iptc;
using MetadataExtractor.Formats.Exif;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for extracting metadata from image files using MetadataExtractor library
    /// </summary>
    public class ImageMetadataService : IImageMetadataService
    {
        private readonly ILogger<ImageMetadataService> _logger;

        public ImageMetadataService(ILogger<ImageMetadataService> logger)
        {
            _logger = logger;
        }

        public async Task<Dictionary<string, string>> ExtractIptcMetadataAsync(Stream imageStream, string fileName)
        {
            _logger.LogInformation("Extracting IPTC metadata from image: {FileName}", fileName);
            
            var metadata = new Dictionary<string, string>();
            
            try
            {
                // Reset stream position
                imageStream.Position = 0;
                
                var directories = ImageMetadataReader.ReadMetadata(imageStream);
                var iptcDirectory = directories.OfType<IptcDirectory>().FirstOrDefault();
                
                if (iptcDirectory != null)
                {
                    // Extract IPTC fields
                    foreach (var tag in iptcDirectory.Tags)
                    {
                        try
                        {
                            var description = iptcDirectory.GetDescription(tag.Type);
                            if (!string.IsNullOrEmpty(description))
                            {
                                metadata[tag.Name.ToLowerInvariant().Replace(" ", "_")] = description;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error extracting IPTC tag {TagName} from {FileName}", tag.Name, fileName);
                        }
                    }
                    
                    _logger.LogInformation("Extracted {Count} IPTC metadata fields from {FileName}", metadata.Count, fileName);
                }
                else
                {
                    _logger.LogInformation("No IPTC metadata found in {FileName}", fileName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting IPTC metadata from {FileName}", fileName);
            }
            
            return metadata;
        }

        public async Task<Dictionary<string, string>> ExtractExifMetadataAsync(Stream imageStream, string fileName)
        {
            _logger.LogInformation("Extracting EXIF metadata from image: {FileName}", fileName);
            
            var metadata = new Dictionary<string, string>();
            
            try
            {
                // Reset stream position
                imageStream.Position = 0;
                
                var directories = ImageMetadataReader.ReadMetadata(imageStream);
                var exifDirectories = directories.OfType<ExifDirectoryBase>();
                
                foreach (var directory in exifDirectories)
                {
                    foreach (var tag in directory.Tags)
                    {
                        try
                        {
                            var description = directory.GetDescription(tag.Type);
                            if (!string.IsNullOrEmpty(description))
                            {
                                metadata[tag.Name.ToLowerInvariant().Replace(" ", "_")] = description;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error extracting EXIF tag {TagName} from {FileName}", tag.Name, fileName);
                        }
                    }
                }
                
                _logger.LogInformation("Extracted {Count} EXIF metadata fields from {FileName}", metadata.Count, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting EXIF metadata from {FileName}", fileName);
            }
            
            return metadata;
        }

        public async Task<Dictionary<string, string>> ExtractAllMetadataAsync(Stream imageStream, string fileName)
        {
            _logger.LogInformation("Extracting all metadata from image: {FileName}", fileName);
            
            var allMetadata = new Dictionary<string, string>();
            
            try
            {
                // Extract IPTC metadata
                var iptcMetadata = await ExtractIptcMetadataAsync(imageStream, fileName);
                foreach (var kvp in iptcMetadata)
                {
                    allMetadata[$"iptc_{kvp.Key}"] = kvp.Value;
                }
                
                // Extract EXIF metadata
                var exifMetadata = await ExtractExifMetadataAsync(imageStream, fileName);
                foreach (var kvp in exifMetadata)
                {
                    allMetadata[$"exif_{kvp.Key}"] = kvp.Value;
                }
                
                _logger.LogInformation("Extracted {Count} total metadata fields from {FileName}", allMetadata.Count, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting all metadata from {FileName}", fileName);
            }
            
            return allMetadata;
        }

        public async Task<CatImageMetadata> ExtractCatMetadataAsync(Stream imageStream, string fileName)
        {
            _logger.LogInformation("Extracting cat-specific metadata from image: {FileName}", fileName);
            
            var catMetadata = new CatImageMetadata
            {
                FileFormat = Path.GetExtension(fileName).ToLower(),
                ContentType = GetContentTypeFromFileName(fileName)
            };
            
            try
            {
                // Reset stream position
                imageStream.Position = 0;
                
                var directories = ImageMetadataReader.ReadMetadata(imageStream);
                var iptcDirectory = directories.OfType<IptcDirectory>().FirstOrDefault();
                
                if (iptcDirectory != null)
                {
                    // Extract Person Shown (cat name) - IPTC tag 0x0228
                    if (iptcDirectory.HasTagName(0x0228))
                    {
                        var personShown = iptcDirectory.GetDescription(0x0228);
                        if (!string.IsNullOrEmpty(personShown))
                        {
                            catMetadata.Name = personShown.Trim();
                            _logger.LogInformation("Extracted cat name from Person Shown: {Name}", catMetadata.Name);
                        }
                    }

                    // Extract Model Age (cat age) - IPTC tag 0x0229
                    if (iptcDirectory.HasTagName(0x0229))
                    {
                        var modelAge = iptcDirectory.GetDescription(0x0229);
                        if (!string.IsNullOrEmpty(modelAge))
                        {
                            catMetadata.Age = modelAge.Trim();
                            _logger.LogInformation("Extracted cat age from Model Age: {Age}", catMetadata.Age);
                        }
                    }

                    // Extract Date Created - IPTC tag 0x0237
                    if (iptcDirectory.HasTagName(0x0237))
                    {
                        var dateCreated = iptcDirectory.GetDescription(0x0237);
                        if (!string.IsNullOrEmpty(dateCreated) && DateTime.TryParse(dateCreated, out var parsedDate))
                        {
                            catMetadata.DateTaken = parsedDate;
                            _logger.LogInformation("Extracted date taken from Date Created: {Date}", catMetadata.DateTaken);
                        }
                    }
                    
                    // Extract other useful IPTC fields
                    if (iptcDirectory.HasTagName(IptcDirectory.TagCaption))
                    {
                        var caption = iptcDirectory.GetDescription(IptcDirectory.TagCaption);
                        if (!string.IsNullOrEmpty(caption))
                        {
                            catMetadata.Description = caption.Trim();
                        }
                    }

                    if (iptcDirectory.HasTagName(IptcDirectory.TagKeywords))
                    {
                        var keywords = iptcDirectory.GetDescription(IptcDirectory.TagKeywords);
                        if (!string.IsNullOrEmpty(keywords))
                        {
                            catMetadata.Tags = keywords.Trim();
                        }
                    }
                    
                    // Store all IPTC metadata in AdditionalMetadata for reference
                    foreach (var tag in iptcDirectory.Tags)
                    {
                        try
                        {
                            var description = iptcDirectory.GetDescription(tag.Type);
                            if (!string.IsNullOrEmpty(description))
                            {
                                catMetadata.AdditionalMetadata[$"iptc_{tag.Name.ToLowerInvariant().Replace(" ", "_")}"] = description;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error storing IPTC tag {TagName} in additional metadata", tag.Name);
                        }
                    }
                }
                
                _logger.LogInformation("Successfully extracted cat metadata from {FileName}. Name: {Name}, Age: {Age}", 
                    fileName, catMetadata.Name, catMetadata.Age);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting cat metadata from {FileName}", fileName);
            }
            
            return catMetadata;
        }

        private string GetContentTypeFromFileName(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                ".tiff" or ".tif" => "image/tiff",
                _ => "application/octet-stream"
            };
        }
    }
}
