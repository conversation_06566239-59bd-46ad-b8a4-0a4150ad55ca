using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for managing users in the system
    /// </summary>
    public class UserService : IUserService
    {
        private readonly AppDbContext _context;
        private readonly IAuthService _authService;
        private readonly ILogger<UserService> _logger;

        /// <summary>
        /// Constructor for the user service
        /// </summary>
        /// <param name="context">Database context</param>
        /// <param name="authService">Authentication service for password hashing</param>
        /// <param name="logger">Logger for logging operations</param>
        public UserService(AppDbContext context, IAuthService authService, ILogger<UserService> logger)
        {
            _context = context;
            _authService = authService;
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            _logger.LogInformation("Retrieving all users");
            return await _context.Users.ToListAsync();
        }

        /// <inheritdoc />
        public async Task<User?> GetUserByIdAsync(int id)
        {
            _logger.LogInformation("Retrieving user with ID: {UserId}", id);
            return await _context.Users.FindAsync(id);
        }

        /// <inheritdoc />
        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            _logger.LogInformation("Retrieving user with username: {Username}", username);
            return await _context.Users
                .FirstOrDefaultAsync(u => u.Username.ToLower() == username.ToLower());
        }

        /// <inheritdoc />
        public async Task<User?> GetUserByEmailAsync(string email)
        {
            _logger.LogInformation("Retrieving user with email: {Email}", email);
            return await _context.Users
                .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());
        }

        /// <inheritdoc />
        public async Task<User> AddUserAsync(User user, string password)
        {
            _logger.LogInformation("Adding new user with username: {Username}", user.Username);
            
            // Validate username and email uniqueness
            if (await IsUsernameAvailableAsync(user.Username) == false)
            {
                throw new InvalidOperationException($"Username '{user.Username}' is already taken.");
            }
            
            if (await IsEmailAvailableAsync(user.Email) == false)
            {
                throw new InvalidOperationException($"Email '{user.Email}' is already registered.");
            }
            
            // Hash the password
            var (hash, salt) = _authService.HashPassword(password);
            user.PasswordHash = hash;
            user.PasswordSalt = salt;
            
            // Set creation date and active status
            user.CreatedAt = DateTime.UtcNow;
            user.IsActive = true;
            
            // Add to database
            await _context.Users.AddAsync(user);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("User created with ID: {UserId}", user.Id);
            return user;
        }

        /// <inheritdoc />
        public async Task UpdateUserAsync(User user)
        {
            _logger.LogInformation("Updating user with ID: {UserId}", user.Id);
            
            var existingUser = await _context.Users.FindAsync(user.Id) 
                ?? throw new KeyNotFoundException($"User with ID {user.Id} not found.");
            
            // Check if username or email is being changed, and if so, check availability
            if (existingUser.Username != user.Username && !await IsUsernameAvailableAsync(user.Username))
            {
                throw new InvalidOperationException($"Username '{user.Username}' is already taken.");
            }
            
            if (existingUser.Email != user.Email && !await IsEmailAvailableAsync(user.Email))
            {
                throw new InvalidOperationException($"Email '{user.Email}' is already registered.");
            }
            
            // Update properties (exclude security-related ones)
            existingUser.Username = user.Username;
            existingUser.Email = user.Email;
            existingUser.FirstName = user.FirstName;
            existingUser.LastName = user.LastName;
            existingUser.Role = user.Role;
            existingUser.IsActive = user.IsActive;
            
            _context.Users.Update(existingUser);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("User updated: {UserId}", user.Id);
        }

        /// <inheritdoc />
        public async Task DeleteUserAsync(int id)
        {
            _logger.LogInformation("Deleting user with ID: {UserId}", id);
            
            var user = await _context.Users.FindAsync(id) 
                ?? throw new KeyNotFoundException($"User with ID {id} not found.");
            
            _context.Users.Remove(user);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("User deleted: {UserId}", id);
        }

        /// <inheritdoc />
        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            _logger.LogInformation("Changing password for user with ID: {UserId}", userId);
            
            var user = await _context.Users.FindAsync(userId) 
                ?? throw new KeyNotFoundException($"User with ID {userId} not found.");
            
            // Verify current password
            if (!_authService.VerifyPassword(currentPassword, user.PasswordHash, user.PasswordSalt))
            {
                _logger.LogWarning("Password change failed - current password incorrect for user: {UserId}", userId);
                return false;
            }
            
            // Hash and set new password
            var (hash, salt) = _authService.HashPassword(newPassword);
            user.PasswordHash = hash;
            user.PasswordSalt = salt;
            
            _context.Users.Update(user);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Password changed successfully for user: {UserId}", userId);
            return true;
        }

        /// <inheritdoc />
        public async Task<bool> IsUsernameAvailableAsync(string username)
        {
            return !await _context.Users.AnyAsync(u => u.Username.ToLower() == username.ToLower());
        }

        /// <inheritdoc />
        public async Task<bool> IsEmailAvailableAsync(string email)
        {
            return !await _context.Users.AnyAsync(u => u.Email.ToLower() == email.ToLower());
        }
    }
} 