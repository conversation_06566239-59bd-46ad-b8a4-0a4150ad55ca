using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using YendorCats.API.Services;

namespace YendorCats.API.Attributes
{
    /// <summary>
    /// Custom authorization attribute for admin-only endpoints
    /// </summary>
    public class AdminAuthorizeAttribute : Attribute, IAsyncAuthorizationFilter
    {
        private readonly string[] _allowedRoles;

        public AdminAuthorizeAttribute(params string[] allowedRoles)
        {
            _allowedRoles = allowedRoles.Length > 0 ? allowedRoles : new[] { "SuperAdmin", "Admin", "Editor" };
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            try
            {
                var adminAuthService = context.HttpContext.RequestServices.GetRequiredService<IAdminAuthService>();
                
                // Try to get token from cookie first, then Authorization header
                var token = context.HttpContext.Request.Cookies["admin_token"];
                
                if (string.IsNullOrEmpty(token))
                {
                    var authHeader = context.HttpContext.Request.Headers["Authorization"].ToString();
                    if (authHeader.StartsWith("Bearer "))
                    {
                        token = authHeader.Substring("Bearer ".Length);
                    }
                }

                if (string.IsNullOrEmpty(token))
                {
                    context.Result = new UnauthorizedObjectResult(new { message = "No authentication token provided" });
                    return;
                }

                var admin = await adminAuthService.ValidateTokenAsync(token);

                if (admin == null)
                {
                    context.Result = new UnauthorizedObjectResult(new { message = "Invalid or expired token" });
                    return;
                }

                if (!admin.IsActive)
                {
                    context.Result = new UnauthorizedObjectResult(new { message = "Admin account is inactive" });
                    return;
                }

                if (!_allowedRoles.Contains(admin.Role))
                {
                    context.Result = new ForbidResult();
                    return;
                }

                // Add admin info to HttpContext for use in controllers
                context.HttpContext.Items["AdminUser"] = admin;
            }
            catch (Exception)
            {
                context.Result = new UnauthorizedObjectResult(new { message = "Authentication failed" });
            }
        }
    }

    /// <summary>
    /// Extension methods for getting admin user from HttpContext
    /// </summary>
    public static class HttpContextExtensions
    {
        public static Models.AdminUser? GetAdminUser(this HttpContext context)
        {
            return context.Items["AdminUser"] as Models.AdminUser;
        }
    }
}
