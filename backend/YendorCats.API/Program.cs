using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Text;
using YendorCats.API.Configuration;
using YendorCats.API.Data;
using YendorCats.API.Middleware;
using YendorCats.API.Services;
using System.Reflection;
using Amazon;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;
using Amazon.S3;
using System.Text.Json;
using Amazon.Extensions.NETCore.Setup;
using Pomelo.EntityFrameworkCore.MySql.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();

// Configure AWS Services
var awsOptions = new AWSOptions
{
    Region = RegionEndpoint.GetBySystemName(builder.Configuration["AWS:Region"] ?? "ap-southeast-2")
};

// Add AWS Secrets Manager first so we can use it for S3 credentials
builder.Services.AddAWSService<IAmazonSecretsManager>(awsOptions);
builder.Services.AddSingleton<ISecretsManagerService, SecretsManagerService>();

// Configure S3 client options
var s3ServiceUrl = builder.Configuration["AWS:S3:ServiceUrl"];
var useCredentialsFromSecrets = builder.Configuration.GetValue<bool>("AWS:UseCredentialsFromSecrets");

if (useCredentialsFromSecrets && !builder.Environment.IsDevelopment())
{
    // Register S3 client with credentials from Secrets Manager
    builder.Services.AddSingleton<IAmazonS3>(sp =>
    {
        try
        {
            var secretsManager = sp.GetRequiredService<ISecretsManagerService>();
            var secrets = secretsManager.GetAppSecretsAsync().GetAwaiter().GetResult();

            // Create S3 client configuration
            var s3Config = new AmazonS3Config
            {
                ServiceURL = s3ServiceUrl,
                ForcePathStyle = true
            };

            // Create S3 client with credentials from Secrets Manager
            if (!string.IsNullOrEmpty(secrets.S3AccessKey) && !string.IsNullOrEmpty(secrets.S3SecretKey))
            {
                Log.Information("Using S3 credentials from AWS Secrets Manager");

                if (!string.IsNullOrEmpty(secrets.S3SessionToken))
                {
                    // Use temporary credentials with session token
                    return new AmazonS3Client(
                        secrets.S3AccessKey,
                        secrets.S3SecretKey,
                        secrets.S3SessionToken,
                        s3Config);
                }
                else
                {
                    // Use long-term credentials
                    return new AmazonS3Client(
                        secrets.S3AccessKey,
                        secrets.S3SecretKey,
                        s3Config);
                }
            }

            // Fall back to default credentials if no S3 credentials in Secrets Manager
            Log.Information("No S3 credentials found in Secrets Manager, using default credentials");
            return new AmazonS3Client(s3Config);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to get S3 credentials from Secrets Manager, using default credentials");
            // Create a new config instance here since the previous one is out of scope
            var fallbackConfig = new AmazonS3Config
            {
                ServiceURL = s3ServiceUrl,
                ForcePathStyle = true
            };
            return new AmazonS3Client(fallbackConfig);
        }
    });
}
else if (!string.IsNullOrEmpty(s3ServiceUrl))
{
    // Create custom S3 client configuration
    var s3Config = new AmazonS3Config
    {
        ServiceURL = s3ServiceUrl,
        ForcePathStyle = true // Required for some S3-compatible storage services
    };

    // Check if we have explicit S3 credentials in configuration (for development)
    var s3AccessKey = builder.Configuration["AWS:S3:AccessKey"];
    var s3SecretKey = builder.Configuration["AWS:S3:SecretKey"];

    if (!string.IsNullOrEmpty(s3AccessKey) && !string.IsNullOrEmpty(s3SecretKey))
    {
        // Register S3 client with explicit credentials from configuration
        Log.Information("Using S3 with custom endpoint and explicit credentials from configuration");
        builder.Services.AddSingleton<IAmazonS3>(sp => new AmazonS3Client(
            s3AccessKey,
            s3SecretKey,
            s3Config));
    }
    else
    {
        // Register S3 client with custom configuration and default credentials
        Log.Information("Using S3 with custom endpoint and default credentials");
        builder.Services.AddSingleton<IAmazonS3>(sp => new AmazonS3Client(s3Config));
    }
}
else
{
    // Use standard AWS S3 client with default credentials
    Log.Information("Using standard S3 client with default credentials");
    builder.Services.AddAWSService<IAmazonS3>(awsOptions);
}

// Add S3 storage service
builder.Services.AddScoped<IS3StorageService, S3StorageService>();

// Configure JWT settings
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));

// Configure Entity Framework
builder.Services.AddDbContext<AppDbContext>((serviceProvider, options) =>
{
    var environment = builder.Environment;
    var secretsManager = serviceProvider.GetRequiredService<ISecretsManagerService>();

    if (environment.IsDevelopment())
    {
        // Use local connection string in development
        var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException(
                "Development connection string not found. Please check your appsettings.Development.json file.");
        }
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mySqlOptions => mySqlOptions.EnableRetryOnFailure());
    }
    else
    {
        // Use AWS Secrets Manager in production
        try
        {
            // Get DB credentials from AWS Secrets Manager
            var secrets = secretsManager.GetAppSecretsAsync().GetAwaiter().GetResult();
            options.UseMySql(secrets.DbConnectionString, ServerVersion.AutoDetect(secrets.DbConnectionString), mySqlOptions => mySqlOptions.EnableRetryOnFailure());
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to retrieve database connection from AWS Secrets Manager");
            // Fall back to configuration if available
            var connectionString = builder.Configuration.GetConnectionString("ProductionConnection");
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException(
                    "Failed to get connection string from Secrets Manager and no fallback connection string found.");
            }
            options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mySqlOptions => mySqlOptions.EnableRetryOnFailure());
        }
    }
});

// Configure Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Yendor Cats API",
        Version = "v1",
        Description = "API for the Yendor Cats exotic cat breeder website.",
        Contact = new OpenApiContact
        {
            Name = "Admin",
            Email = "<EMAIL>"
        }
    });

    // Include XML comments in Swagger
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);

    // Add JWT authentication to Swagger UI
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigins",
        policy =>
        {
            policy.WithOrigins(
                "http://localhost:5000",
                "https://yendorcats.com")
                .AllowAnyMethod()
                .AllowAnyHeader()
                .AllowCredentials();
        });
});

// Configure JWT authentication
builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    var secretsManager = builder.Services.BuildServiceProvider().GetRequiredService<ISecretsManagerService>();
    AppSecrets secrets;

    try
    {
        // Try to get from secrets manager
        secrets = secretsManager.GetAppSecretsAsync().GetAwaiter().GetResult();
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Failed to get JWT settings from secrets manager, using values from appsettings");
        // Fallback to appsettings
        var jwtSettings = builder.Configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["Secret"] ?? "YendorCatsDefaultSecretKey1234567890123456789012"; // Fallback key for dev

        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretKey)),
            ValidateIssuer = !string.IsNullOrEmpty(jwtSettings["Issuer"]),
            ValidIssuer = jwtSettings["Issuer"],
            ValidateAudience = !string.IsNullOrEmpty(jwtSettings["Audience"]),
            ValidAudience = jwtSettings["Audience"],
            ClockSkew = TimeSpan.Zero
        };

        return;
    }

    // Using values from secrets manager
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secrets.JwtSecret)),
        ValidateIssuer = !string.IsNullOrEmpty(secrets.JwtIssuer),
        ValidIssuer = secrets.JwtIssuer,
        ValidateAudience = !string.IsNullOrEmpty(secrets.JwtAudience),
        ValidAudience = secrets.JwtAudience,
        ClockSkew = TimeSpan.Zero
    };
});

// Register services
builder.Services.AddScoped<ICatService, CatService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IImageService, ImageService>();

var app = builder.Build();

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseDeveloperExceptionPage();
}
else
{
    // Custom exception handling for production
    app.UseMiddleware<ErrorHandlingMiddleware>();
    app.UseHsts();
}

// Configure S3 CORS on startup
if (!string.IsNullOrEmpty(builder.Configuration["AWS:S3:BucketName"]))
{
    try
    {
        using (var scope = app.Services.CreateScope())
        {
            var s3Service = scope.ServiceProvider.GetRequiredService<IS3StorageService>();
            s3Service.ConfigureCorsAsync().GetAwaiter().GetResult();
            Log.Information("S3 CORS configuration applied successfully");
        }
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Failed to configure S3 CORS on startup");
    }
}

// Apply migrations automatically in development
if (app.Environment.IsDevelopment())
{
    using (var scope = app.Services.CreateScope())
    {
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        dbContext.Database.Migrate();
    }
}

app.UseHttpsRedirection();

// Configure static files with default file support
app.UseDefaultFiles(new DefaultFilesOptions
{
    DefaultFileNames = new List<string> { "index.html" }
});
app.UseStaticFiles();

// Add SPA fallback route handler
app.MapFallbackToFile("index.html");

// Enable CORS
app.UseCors("AllowSpecificOrigins");

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();

// Configure file watcher in development mode for real-time updates
if (app.Environment.IsDevelopment())
{
    app.UseLiveReload();
    app.UseFileWatcher(app.Environment, app.Services.GetRequiredService<ILogger<FileSystemWatcher>>());
}

app.MapControllers();

try
{
    Log.Information("Starting Yendor Cats API");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application start-up failed");
}
finally
{
    Log.CloseAndFlush();
}