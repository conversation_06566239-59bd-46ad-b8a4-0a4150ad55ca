#!/bin/bash

# Script to create the initial migration for YendorCats Entity Framework

set -e

echo "Creating Entity Framework migration for YendorCats..."

# Make sure we're in the correct directory
cd "$(dirname "$0")/.."

# Ensure EF tools are installed
dotnet tool install --global dotnet-ef || true
export PATH="$PATH:$HOME/.dotnet/tools"

# Check if any migrations already exist
MIGRATION_COUNT=$(find Migrations -name "*.cs" 2>/dev/null | wc -l)

if [ "$MIGRATION_COUNT" -gt 0 ]; then
  echo "Migrations already exist in the project. If you want to add a new migration, run:"
  echo "dotnet ef migrations add YourMigrationName"
  exit 0
fi

# Create the initial migration
echo "Creating initial migration..."
dotnet ef migrations add InitialCreate

# Verify the migration was created
if [ ! -d "Migrations" ] || [ "$(find Migrations -name "*.cs" | wc -l)" -eq 0 ]; then
  echo "Error: Failed to create migration files"
  exit 1
fi

echo "Migration created successfully!"
echo "You can now apply this migration with: dotnet ef database update"
