#!/bin/bash

# <PERSON><PERSON>t to build a pre-populated SQLite database for production/staging environments
# This script is intended to be run during the Docker build process

set -e

echo "Building SQLite database for production deployment..."

# Define database path
DB_PATH="/app/data/yendorcats.db"
TEMP_DB_PATH="/tmp/yendorcats.db"

# Ensure EF tools are installed
dotnet tool install --global dotnet-ef || true
export PATH="$PATH:$HOME/.dotnet/tools"

# Create temporary database
echo "Creating SQLite database at $TEMP_DB_PATH..."
export ConnectionStrings__SqliteConnection="Data Source=$TEMP_DB_PATH"

# Apply migrations to create schema
echo "Applying Entity Framework migrations..."
dotnet ef database update

# Verify database was created
if [[ ! -f "$TEMP_DB_PATH" ]]; then
  echo "Error: Failed to create SQLite database"
  exit 1
fi

# Create directories if they don't exist
mkdir -p $(dirname "$DB_PATH")

# Copy database to final location
cp "$TEMP_DB_PATH" "$DB_PATH"
echo "Database built and copied to $DB_PATH"

# Set appropriate permissions
chmod 644 "$DB_PATH"

echo "Database build complete!"
