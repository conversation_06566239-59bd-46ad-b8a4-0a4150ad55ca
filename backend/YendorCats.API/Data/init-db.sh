#!/bin/bash

# Initialize SQLite database for YendorCats

set -e

echo "Initializing SQLite database for YendorCats..."

# Environment detection
if [[ -n "$ASPNETCORE_ENVIRONMENT" && "$ASPNETCORE_ENVIRONMENT" == "Production" ]]; then
  DB_PATH="/app/data/yendorcats.db"
  echo "Production environment detected, using path: $DB_PATH"
else
  DB_PATH="Data/yendorcats.db"
  echo "Development environment detected, using path: $DB_PATH"
fi

# Create data directory if it doesn't exist
mkdir -p $(dirname "$DB_PATH")

# Check if the database already exists
if [[ -f "$DB_PATH" ]]; then
  echo "Database already exists at $DB_PATH"
  exit 0
fi

# Use EF Core to create and migrate the database
echo "Running EF Core migrations to create database..."
dotnet ef database update --no-build

echo "Database initialization complete!"
