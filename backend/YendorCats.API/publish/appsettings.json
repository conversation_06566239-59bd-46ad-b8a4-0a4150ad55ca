{"ConnectionStrings": {"DefaultConnection": "Server=localhost\\SQLEXPRESS;Database=YendorCats;Trusted_Connection=True;TrustServerCertificate=True;", "ProductionConnection": "Server=your-production-server;Database=YendorCats;User Id=your-username;Password=your-password;TrustServerCertificate=True;"}, "JwtSettings": {"Secret": "YourSecretKeyHere12345678901234567890123456789012", "Issuer": "YendorCatsApi", "Audience": "YendorCatsClients", "ExpiryMinutes": 60, "RefreshExpiryDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day"}}]}}