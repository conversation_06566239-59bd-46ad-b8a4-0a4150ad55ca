{"Version": 1, "ManifestType": "Publish", "Endpoints": [{"Route": "cat-details.html", "AssetFile": "cat-details.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Nqnn8clbgv+5l0PgxcTOldg8mkMKrFn4TvPL+rYUUGg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 04:33:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nqnn8clbgv+5l0PgxcTOldg8mkMKrFn4TvPL+rYUUGg="}]}, {"Route": "cat-details.yu78tspv7y.html", "AssetFile": "cat-details.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Nqnn8clbgv+5l0PgxcTOldg8mkMKrFn4TvPL+rYUUGg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 04:33:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yu78tspv7y"}, {"Name": "integrity", "Value": "sha256-Nqnn8clbgv+5l0PgxcTOldg8mkMKrFn4TvPL+rYUUGg="}, {"Name": "label", "Value": "cat-details.html"}]}, {"Route": "css/base.css", "AssetFile": "css/base.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2550"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KvLA1/7uWfaxSqKq1UcDOEBKspQEWvPvydp42V6phIs=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KvLA1/7uWfaxSqKq1UcDOEBKspQEWvPvydp42V6phIs="}]}, {"Route": "css/base.y1hi3tf22k.css", "AssetFile": "css/base.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2550"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KvLA1/7uWfaxSqKq1UcDOEBKspQEWvPvydp42V6phIs=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y1hi3tf22k"}, {"Name": "integrity", "Value": "sha256-KvLA1/7uWfaxSqKq1UcDOEBKspQEWvPvydp42V6phIs="}, {"Name": "label", "Value": "css/base.css"}]}, {"Route": "css/components/buttons.css", "AssetFile": "css/components/buttons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1813"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzESzc+soC4pu5PuJBJoSdjNLFyL5LbpACtcQXUIJSk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 05:36:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzESzc+soC4pu5PuJBJoSdjNLFyL5LbpACtcQXUIJSk="}]}, {"Route": "css/components/buttons.ffe0khzlux.css", "AssetFile": "css/components/buttons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1813"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzESzc+soC4pu5PuJBJoSdjNLFyL5LbpACtcQXUIJSk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 05:36:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ffe0khzlux"}, {"Name": "integrity", "Value": "sha256-CzESzc+soC4pu5PuJBJoSdjNLFyL5LbpACtcQXUIJSk="}, {"Name": "label", "Value": "css/components/buttons.css"}]}, {"Route": "css/components/carousel.css", "AssetFile": "css/components/carousel.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18920"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"t78RHWhiIPnPFiTglS572zt0Dqx50WYNmWlyPMm2uTg=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:18:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-t78RHWhiIPnPFiTglS572zt0Dqx50WYNmWlyPMm2uTg="}]}, {"Route": "css/components/carousel.p4qgcvr966.css", "AssetFile": "css/components/carousel.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18920"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"t78RHWhiIPnPFiTglS572zt0Dqx50WYNmWlyPMm2uTg=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:18:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "p4qgcvr966"}, {"Name": "integrity", "Value": "sha256-t78RHWhiIPnPFiTglS572zt0Dqx50WYNmWlyPMm2uTg="}, {"Name": "label", "Value": "css/components/carousel.css"}]}, {"Route": "css/components/footer.css", "AssetFile": "css/components/footer.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "704"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+1L/T040eegMl9BDUwSnXDse1BUG+cKV+OxYi6aNAdI=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+1L/T040eegMl9BDUwSnXDse1BUG+cKV+OxYi6aNAdI="}]}, {"Route": "css/components/footer.v3vabf5n3e.css", "AssetFile": "css/components/footer.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "704"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+1L/T040eegMl9BDUwSnXDse1BUG+cKV+OxYi6aNAdI=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v3vabf5n3e"}, {"Name": "integrity", "Value": "sha256-+1L/T040eegMl9BDUwSnXDse1BUG+cKV+OxYi6aNAdI="}, {"Name": "label", "Value": "css/components/footer.css"}]}, {"Route": "css/components/navbar.css", "AssetFile": "css/components/navbar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11211"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4hiKQdbpmXCoww0C8ZNv2VckW0haCKoiGF7iXetbc7g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 10:52:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4hiKQdbpmXCoww0C8ZNv2VckW0haCKoiGF7iXetbc7g="}]}, {"Route": "css/components/navbar.ijm05wxz13.css", "AssetFile": "css/components/navbar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11211"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4hiKQdbpmXCoww0C8ZNv2VckW0haCKoiGF7iXetbc7g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 10:52:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ijm05wxz13"}, {"Name": "integrity", "Value": "sha256-4hiKQdbpmXCoww0C8ZNv2VckW0haCKoiGF7iXetbc7g="}, {"Name": "label", "Value": "css/components/navbar.css"}]}, {"Route": "css/main.css", "AssetFile": "css/main.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1390"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Eipt+RbhjkzSqr96S6GTyfq0vFTgw/y5oh7mKI/+sIU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 09:41:13 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E<PERSON>t+RbhjkzSqr96S6GTyfq0vFTgw/y5oh7mKI/+sIU="}]}, {"Route": "css/main.uwd6enkyp2.css", "AssetFile": "css/main.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1390"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Eipt+RbhjkzSqr96S6GTyfq0vFTgw/y5oh7mKI/+sIU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 09:41:13 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uwd6enkyp2"}, {"Name": "integrity", "Value": "sha256-E<PERSON>t+RbhjkzSqr96S6GTyfq0vFTgw/y5oh7mKI/+sIU="}, {"Name": "label", "Value": "css/main.css"}]}, {"Route": "css/sections.0rfhvbmnz0.css", "AssetFile": "css/sections.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17045"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/BSyeiBoGcF8g2mwNDsCsm1ie8lTaxlEuWCBuZy44Wk=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:07:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0rfhvbmnz0"}, {"Name": "integrity", "Value": "sha256-/BSyeiBoGcF8g2mwNDsCsm1ie8lTaxlEuWCBuZy44Wk="}, {"Name": "label", "Value": "css/sections.css"}]}, {"Route": "css/sections.css", "AssetFile": "css/sections.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17045"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/BSyeiBoGcF8g2mwNDsCsm1ie8lTaxlEuWCBuZy44Wk=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:07:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/BSyeiBoGcF8g2mwNDsCsm1ie8lTaxlEuWCBuZy44Wk="}]}, {"Route": "css/styles.3crd4kny4y.css", "AssetFile": "css/styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19526"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ow9Ack3nkYgYX1s/PCsWHcocdcMjIF/oZM2L3nIhtmk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 09:39:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3crd4kny4y"}, {"Name": "integrity", "Value": "sha256-ow9Ack3nkYgYX1s/PCsWHcocdcMjIF/oZM2L3nIhtmk="}, {"Name": "label", "Value": "css/styles.css"}]}, {"Route": "css/styles.css", "AssetFile": "css/styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19526"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ow9Ack3nkYgYX1s/PCsWHcocdcMjIF/oZM2L3nIhtmk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 09:39:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ow9Ack3nkYgYX1s/PCsWHcocdcMjIF/oZM2L3nIhtmk="}]}, {"Route": "css/variables.css", "AssetFile": "css/variables.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2225"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"E5U7Ym5uJL6OfvgZoEGqVIX2jGJT/Pp3m0O4sOR2tqo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 05:46:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E5U7Ym5uJL6OfvgZoEGqVIX2jGJT/Pp3m0O4sOR2tqo="}]}, {"Route": "css/variables.xr0o5zc8sz.css", "AssetFile": "css/variables.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2225"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"E5U7Ym5uJL6OfvgZoEGqVIX2jGJT/Pp3m0O4sOR2tqo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 05:46:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xr0o5zc8sz"}, {"Name": "integrity", "Value": "sha256-E5U7Ym5uJL6OfvgZoEGqVIX2jGJT/Pp3m0O4sOR2tqo="}, {"Name": "label", "Value": "css/variables.css"}]}, {"Route": "images/favicon.ico", "AssetFile": "images/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "55942"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"2Ov/7hGuHmRiPMOTRIVfcqzcnHfhOjxXBniYede0VSA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 10:40:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2Ov/7hGuHmRiPMOTRIVfcqzcnHfhOjxXBniYede0VSA="}]}, {"Route": "images/favicon.srnm2qqhme.ico", "AssetFile": "images/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55942"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"2Ov/7hGuHmRiPMOTRIVfcqzcnHfhOjxXBniYede0VSA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 10:40:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "srnm2qqhme"}, {"Name": "integrity", "Value": "sha256-2Ov/7hGuHmRiPMOTRIVfcqzcnHfhOjxXBniYede0VSA="}, {"Name": "label", "Value": "images/favicon.ico"}]}, {"Route": "images/gallery/gallery-1.jpg", "AssetFile": "images/gallery/gallery-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "477193"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"mk8gbbfYU7yOQXviQ7lIMhh8Xn4JXiKHeBRlPU6Yp/w=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mk8gbbfYU7yOQXviQ7lIMhh8Xn4JXiKHeBRlPU6Yp/w="}]}, {"Route": "images/gallery/gallery-1.u2sdd93zdr.jpg", "AssetFile": "images/gallery/gallery-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "477193"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"mk8gbbfYU7yOQXviQ7lIMhh8Xn4JXiKHeBRlPU6Yp/w=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u2sdd93zdr"}, {"Name": "integrity", "Value": "sha256-mk8gbbfYU7yOQXviQ7lIMhh8Xn4JXiKHeBRlPU6Yp/w="}, {"Name": "label", "Value": "images/gallery/gallery-1.jpg"}]}, {"Route": "images/gallery/gallery-2.jpg", "AssetFile": "images/gallery/gallery-2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "372530"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Qw76NYctWWZmnX+4xOwDL2zQZMs6UxTejy5HO/6krLw=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qw76NYctWWZmnX+4xOwDL2zQZMs6UxTejy5HO/6krLw="}]}, {"Route": "images/gallery/gallery-2.nbezfj4svv.jpg", "AssetFile": "images/gallery/gallery-2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "372530"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Qw76NYctWWZmnX+4xOwDL2zQZMs6UxTejy5HO/6krLw=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nbezfj4svv"}, {"Name": "integrity", "Value": "sha256-Qw76NYctWWZmnX+4xOwDL2zQZMs6UxTejy5HO/6krLw="}, {"Name": "label", "Value": "images/gallery/gallery-2.jpg"}]}, {"Route": "images/gallery/gallery-3.jpg", "AssetFile": "images/gallery/gallery-3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "515467"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"f6o5irdXN6GwRsYRrHxoUmyo5yYvbuCsT0q6IoeNsUs=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f6o5irdXN6GwRsYRrHxoUmyo5yYvbuCsT0q6IoeNsUs="}]}, {"Route": "images/gallery/gallery-3.t18jue1n4u.jpg", "AssetFile": "images/gallery/gallery-3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "515467"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"f6o5irdXN6GwRsYRrHxoUmyo5yYvbuCsT0q6IoeNsUs=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t18jue1n4u"}, {"Name": "integrity", "Value": "sha256-f6o5irdXN6GwRsYRrHxoUmyo5yYvbuCsT0q6IoeNsUs="}, {"Name": "label", "Value": "images/gallery/gallery-3.jpg"}]}, {"Route": "images/gallery/gallery-4.5qd5gpuitj.jpg", "AssetFile": "images/gallery/gallery-4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "371806"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"vUj1zgcuj396RHG/LTo+ZV4CYz1lvi33jurN8VnOxHI=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5qd5gpuitj"}, {"Name": "integrity", "Value": "sha256-vUj1zgcuj396RHG/LTo+ZV4CYz1lvi33jurN8VnOxHI="}, {"Name": "label", "Value": "images/gallery/gallery-4.jpg"}]}, {"Route": "images/gallery/gallery-4.jpg", "AssetFile": "images/gallery/gallery-4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "371806"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"vUj1zgcuj396RHG/LTo+ZV4CYz1lvi33jurN8VnOxHI=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vUj1zgcuj396RHG/LTo+ZV4CYz1lvi33jurN8VnOxHI="}]}, {"Route": "images/gallery/gallery-5.ffwg9szw3o.jpg", "AssetFile": "images/gallery/gallery-5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "243826"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"K9HmMOjg76czbpQLxDglaGKwRHLVtAH0SeVxCXo0y5o=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ffwg9szw3o"}, {"Name": "integrity", "Value": "sha256-K9HmMOjg76czbpQLxDglaGKwRHLVtAH0SeVxCXo0y5o="}, {"Name": "label", "Value": "images/gallery/gallery-5.jpg"}]}, {"Route": "images/gallery/gallery-5.jpg", "AssetFile": "images/gallery/gallery-5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "243826"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"K9HmMOjg76czbpQLxDglaGKwRHLVtAH0SeVxCXo0y5o=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K9HmMOjg76czbpQLxDglaGKwRHLVtAH0SeVxCXo0y5o="}]}, {"Route": "images/logo_shield.webp", "AssetFile": "images/logo_shield.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "310710"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"x6KWZRuNln53E0qZxKx2ZPNT15u2k2UheX8x+wZqVhg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 05:27:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-x6KWZRuNln53E0qZxKx2ZPNT15u2k2UheX8x+wZqVhg="}]}, {"Route": "images/logo_shield.z188w6lmn8.webp", "AssetFile": "images/logo_shield.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "310710"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"x6KWZRuNln53E0qZxKx2ZPNT15u2k2UheX8x+wZqVhg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 05:27:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z188w6lmn8"}, {"Name": "integrity", "Value": "sha256-x6KWZRuNln53E0qZxKx2ZPNT15u2k2UheX8x+wZqVhg="}, {"Name": "label", "Value": "images/logo_shield.webp"}]}, {"Route": "images/placeholder-cat.5ipweew5fc.jpg", "AssetFile": "images/placeholder-cat.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 04:34:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "images/placeholder-cat.jpg"}]}, {"Route": "images/placeholder-cat.jpg", "AssetFile": "images/placeholder-cat.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 04:34:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "index.6f0grii10w.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19996"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ikl8jS7HWTVywSVnsMBUZFtNv74oGQsyeZYB0ujvXaU=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:03:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6f0grii10w"}, {"Name": "integrity", "Value": "sha256-Ikl8jS7HWTVywSVnsMBUZFtNv74oGQsyeZYB0ujvXaU="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19996"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ikl8jS7HWTVywSVnsMBUZFtNv74oGQsyeZYB0ujvXaU=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:03:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ikl8jS7HWTVywSVnsMBUZFtNv74oGQsyeZYB0ujvXaU="}]}, {"Route": "js/available-cats.13sgf4aylz.js", "AssetFile": "js/available-cats.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14962"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0+4c9kyoPkDCvp8kR+SPmHhodzzsD8BKY4D+BaCq8cg=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "13sgf4aylz"}, {"Name": "integrity", "Value": "sha256-0+4c9kyoPkDCvp8kR+SPmHhodzzsD8BKY4D+BaCq8cg="}, {"Name": "label", "Value": "js/available-cats.js"}]}, {"Route": "js/available-cats.js", "AssetFile": "js/available-cats.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14962"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0+4c9kyoPkDCvp8kR+SPmHhodzzsD8BKY4D+BaCq8cg=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0+4c9kyoPkDCvp8kR+SPmHhodzzsD8BKY4D+BaCq8cg="}]}, {"Route": "js/carousel.h74lhwg9kw.js", "AssetFile": "js/carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21932"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4aDudLzctX0X9IC6MnMOGUfC08yhEhsB60R3T4Tfcc=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 05:24:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h74lhwg9kw"}, {"Name": "integrity", "Value": "sha256-c4aDudLzctX0X9IC6MnMOGUfC08yhEhsB60R3T4Tfcc="}, {"Name": "label", "Value": "js/carousel.js"}]}, {"Route": "js/carousel.js", "AssetFile": "js/carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21932"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4aDudLzctX0X9IC6MnMOGUfC08yhEhsB60R3T4Tfcc=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 05:24:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c4aDudLzctX0X9IC6MnMOGUfC08yhEhsB60R3T4Tfcc="}]}, {"Route": "js/cat-carousel.1rgsexmnik.js", "AssetFile": "js/cat-carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "44400"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"M6rwfVg7iNzADoT79JXXGcjVQYyigeYy0NAMAF0Hk2c=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:24:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1rgsexmnik"}, {"Name": "integrity", "Value": "sha256-M6rwfVg7iNzADoT79JXXGcjVQYyigeYy0NAMAF0Hk2c="}, {"Name": "label", "Value": "js/cat-carousel.js"}]}, {"Route": "js/cat-carousel.js", "AssetFile": "js/cat-carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44400"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"M6rwfVg7iNzADoT79JXXGcjVQYyigeYy0NAMAF0Hk2c=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:24:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M6rwfVg7iNzADoT79JXXGcjVQYyigeYy0NAMAF0Hk2c="}]}, {"Route": "js/gallery.04qgh60pzj.js", "AssetFile": "js/gallery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10400"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MLECxpZ94SsHa01VmxKcuWxwdWOy5VcK4QJAzamln2I=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "04qgh60pzj"}, {"Name": "integrity", "Value": "sha256-MLECxpZ94SsHa01VmxKcuWxwdWOy5VcK4QJAzamln2I="}, {"Name": "label", "Value": "js/gallery.js"}]}, {"Route": "js/gallery.js", "AssetFile": "js/gallery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10400"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MLECxpZ94SsHa01VmxKcuWxwdWOy5VcK4QJAzamln2I=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MLECxpZ94SsHa01VmxKcuWxwdWOy5VcK4QJAzamln2I="}]}, {"Route": "js/live-reload.js", "AssetFile": "js/live-reload.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1592"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ldA/cpKlARnmQ2LplhGvRUF60swUDLCzVVBLXxgtw+0=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ldA/cpKlARnmQ2LplhGvRUF60swUDLCzVVBLXxgtw+0="}]}, {"Route": "js/live-reload.jueltxtbha.js", "AssetFile": "js/live-reload.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1592"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ldA/cpKlARnmQ2LplhGvRUF60swUDLCzVVBLXxgtw+0=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jueltxtbha"}, {"Name": "integrity", "Value": "sha256-ldA/cpKlARnmQ2LplhGvRUF60swUDLCzVVBLXxgtw+0="}, {"Name": "label", "Value": "js/live-reload.js"}]}, {"Route": "js/main.4by8n4qq8m.js", "AssetFile": "js/main.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12166"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UHnnUE1/Ghrh680wXIxKC+w9lxG77M/dZGHdjOkCDko=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 05:24:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4by8n4qq8m"}, {"Name": "integrity", "Value": "sha256-UHnnUE1/Ghrh680wXIxKC+w9lxG77M/dZGHdjOkCDko="}, {"Name": "label", "Value": "js/main.js"}]}, {"Route": "js/main.js", "AssetFile": "js/main.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12166"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UHnnUE1/Ghrh680wXIxKC+w9lxG77M/dZGHdjOkCDko=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 05:24:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UHnnUE1/Ghrh680wXIxKC+w9lxG77M/dZGHdjOkCDko="}]}, {"Route": "js/main.js.b0lom1h8bq.bak", "AssetFile": "js/main.js.bak", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12721"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Wwp15DBBy/k12hqpCWQMW39qAo/rtMdHvWnRQTaw4T0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 11:04:37 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b0lom1h8bq"}, {"Name": "integrity", "Value": "sha256-Wwp15DBBy/k12hqpCWQMW39qAo/rtMdHvWnRQTaw4T0="}, {"Name": "label", "Value": "js/main.js.bak"}]}, {"Route": "js/main.js.bak", "AssetFile": "js/main.js.bak", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12721"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Wwp15DBBy/k12hqpCWQMW39qAo/rtMdHvWnRQTaw4T0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 11:04:37 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wwp15DBBy/k12hqpCWQMW39qAo/rtMdHvWnRQTaw4T0="}]}, {"Route": "js/navbar.48931x5q9g.js", "AssetFile": "js/navbar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5174"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jBmOoauwLIDYzmf74ufO+ZVciPtajYVpLR/ySH39oEA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 05:39:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48931x5q9g"}, {"Name": "integrity", "Value": "sha256-jBmOoauwLIDYzmf74ufO+ZVciPtajYVpLR/ySH39oEA="}, {"Name": "label", "Value": "js/navbar.js"}]}, {"Route": "js/navbar.js", "AssetFile": "js/navbar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5174"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jBmOoauwLIDYzmf74ufO+ZVciPtajYVpLR/ySH39oEA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 25 Mar 2025 05:39:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jBmOoauwLIDYzmf74ufO+ZVciPtajYVpLR/ySH39oEA="}]}, {"Route": "js/placeholders.js", "AssetFile": "js/placeholders.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1201"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sCaWffFnVqkcXKSzVMN6DIeF/U0vLCXYemZFgA3llNo=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sCaWffFnVqkcXKSzVMN6DIeF/U0vLCXYemZFgA3llNo="}]}, {"Route": "js/placeholders.o6s0k5p54b.js", "AssetFile": "js/placeholders.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1201"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sCaWffFnVqkcXKSzVMN6DIeF/U0vLCXYemZFgA3llNo=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 23:08:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o6s0k5p54b"}, {"Name": "integrity", "Value": "sha256-sCaWffFnVqkcXKSzVMN6DIeF/U0vLCXYemZFgA3llNo="}, {"Name": "label", "Value": "js/placeholders.js"}]}, {"Route": "resources/cat-descriptions.ay97dwzmtw.json", "AssetFile": "resources/cat-descriptions.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"Iosc+f8ZEw4byUUXcuFX8+XjpRapKvvaW9hJU9VturY=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:52:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ay97dwzmtw"}, {"Name": "integrity", "Value": "sha256-Iosc+f8ZEw4byUUXcuFX8+XjpRapKvvaW9hJU9VturY="}, {"Name": "label", "Value": "resources/cat-descriptions.json"}]}, {"Route": "resources/cat-descriptions.json", "AssetFile": "resources/cat-descriptions.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1731"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"Iosc+f8ZEw4byUUXcuFX8+XjpRapKvvaW9hJU9VturY=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 06:52:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Iosc+f8ZEw4byUUXcuFX8+XjpRapKvvaW9hJU9VturY="}]}, {"Route": "resources/gallery/unknown-10.11-260325-1.11zu8158th.jpg", "AssetFile": "resources/gallery/unknown-10.11-260325-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "102136"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"K/cjUh6tX3bgBgctLQKoGvWFtX6GL+4sIqX1QlFg0vk=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Oct 2024 20:46:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "11zu8158th"}, {"Name": "integrity", "Value": "sha256-K/cjUh6tX3bgBgctLQKoGvWFtX6GL+4sIqX1QlFg0vk="}, {"Name": "label", "Value": "resources/gallery/unknown-10.11-260325-1.jpg"}]}, {"Route": "resources/gallery/unknown-10.11-260325-1.jpg", "AssetFile": "resources/gallery/unknown-10.11-260325-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "102136"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"K/cjUh6tX3bgBgctLQKoGvWFtX6GL+4sIqX1QlFg0vk=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Oct 2024 20:46:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K/cjUh6tX3bgBgctLQKoGvWFtX6GL+4sIqX1QlFg0vk="}]}, {"Route": "resources/gallery/unknown-10.11-260325-2.ft9dydh1lt.png", "AssetFile": "resources/gallery/unknown-10.11-260325-2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "105731"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"83jyjaoV6m4hbXWapmrA/YrNURok003mRDPiF0bdjMg=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:40:59 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft9dydh1lt"}, {"Name": "integrity", "Value": "sha256-83jyjaoV6m4hbXWapmrA/YrNURok003mRDPiF0bdjMg="}, {"Name": "label", "Value": "resources/gallery/unknown-10.11-260325-2.png"}]}, {"Route": "resources/gallery/unknown-10.11-260325-2.png", "AssetFile": "resources/gallery/unknown-10.11-260325-2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "105731"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"83jyjaoV6m4hbXWapmrA/YrNURok003mRDPiF0bdjMg=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:40:59 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-83jyjaoV6m4hbXWapmrA/YrNURok003mRDPiF0bdjMg="}]}, {"Route": "resources/gallery/unknown-10.11-260325-3.png", "AssetFile": "resources/gallery/unknown-10.11-260325-3.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "81165"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Hdd8fXGz+FDxAE6taDaYM2K7ywiqMgJpCJAcrWF9hH8=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:42:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hdd8fXGz+FDxAE6taDaYM2K7ywiqMgJpCJAcrWF9hH8="}]}, {"Route": "resources/gallery/unknown-10.11-260325-3.rywcb3eli0.png", "AssetFile": "resources/gallery/unknown-10.11-260325-3.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "81165"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Hdd8fXGz+FDxAE6taDaYM2K7ywiqMgJpCJAcrWF9hH8=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:42:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rywcb3eli0"}, {"Name": "integrity", "Value": "sha256-Hdd8fXGz+FDxAE6taDaYM2K7ywiqMgJpCJAcrWF9hH8="}, {"Name": "label", "Value": "resources/gallery/unknown-10.11-260325-3.png"}]}, {"Route": "resources/gallery/unknown-10.11-260325-4.png", "AssetFile": "resources/gallery/unknown-10.11-260325-4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "79397"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"n6vgU871ipw2Iw26g3NWJ53mws13c7ZZpmD5v+/tBUI=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n6vgU871ipw2Iw26g3NWJ53mws13c7ZZpmD5v+/tBUI="}]}, {"Route": "resources/gallery/unknown-10.11-260325-4.zf21dz5sw9.png", "AssetFile": "resources/gallery/unknown-10.11-260325-4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "79397"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"n6vgU871ipw2Iw26g3NWJ53mws13c7ZZpmD5v+/tBUI=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zf21dz5sw9"}, {"Name": "integrity", "Value": "sha256-n6vgU871ipw2Iw26g3NWJ53mws13c7ZZpmD5v+/tBUI="}, {"Name": "label", "Value": "resources/gallery/unknown-10.11-260325-4.png"}]}, {"Route": "resources/gallery/unknown-10.11-260325-5.png", "AssetFile": "resources/gallery/unknown-10.11-260325-5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "61386"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"G20+LXVJmypwTr6eQd0qdAEFlcmahdNLJd1JUzCKwTY=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G20+LXVJmypwTr6eQd0qdAEFlcmahdNLJd1JUzCKwTY="}]}, {"Route": "resources/gallery/unknown-10.11-260325-5.vz5nn2rywc.png", "AssetFile": "resources/gallery/unknown-10.11-260325-5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "61386"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"G20+LXVJmypwTr6eQd0qdAEFlcmahdNLJd1JUzCKwTY=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:32 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vz5nn2rywc"}, {"Name": "integrity", "Value": "sha256-G20+LXVJmypwTr6eQd0qdAEFlcmahdNLJd1JUzCKwTY="}, {"Name": "label", "Value": "resources/gallery/unknown-10.11-260325-5.png"}]}, {"Route": "resources/gallery/unknown-10.11-260325-6.6bms0fhigj.png", "AssetFile": "resources/gallery/unknown-10.11-260325-6.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "99523"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"8ovg2HpEZ90A9WSUCFM50t5cuBGa5InyYqDRaVkx2ds=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6bms0fhigj"}, {"Name": "integrity", "Value": "sha256-8ovg2HpEZ90A9WSUCFM50t5cuBGa5InyYqDRaVkx2ds="}, {"Name": "label", "Value": "resources/gallery/unknown-10.11-260325-6.png"}]}, {"Route": "resources/gallery/unknown-10.11-260325-6.png", "AssetFile": "resources/gallery/unknown-10.11-260325-6.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "99523"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"8ovg2HpEZ90A9WSUCFM50t5cuBGa5InyYqDRaVkx2ds=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8ovg2HpEZ90A9WSUCFM50t5cuBGa5InyYqDRaVkx2ds="}]}, {"Route": "resources/gallery/unknown-10.11-260325-7.0w5dod67jd.png", "AssetFile": "resources/gallery/unknown-10.11-260325-7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "93505"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"EOsvU4z9rWcAu5UZl8TomqmDM/V62ILgyiwmsBEqAIM=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:45:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0w5dod67jd"}, {"Name": "integrity", "Value": "sha256-EOsvU4z9rWcAu5UZl8TomqmDM/V62ILgyiwmsBEqAIM="}, {"Name": "label", "Value": "resources/gallery/unknown-10.11-260325-7.png"}]}, {"Route": "resources/gallery/unknown-10.11-260325-7.png", "AssetFile": "resources/gallery/unknown-10.11-260325-7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "93505"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"EOsvU4z9rWcAu5UZl8TomqmDM/V62ILgyiwmsBEqAIM=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:45:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EOsvU4z9rWcAu5UZl8TomqmDM/V62ILgyiwmsBEqAIM="}]}, {"Route": "resources/gallery/unknown-10.11-260325-8.png", "AssetFile": "resources/gallery/unknown-10.11-260325-8.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "123495"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1MMYomNZfAikdmG2a4Mp3CQA2afCdoU4tgMCanabrKY=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:45:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1MMYomNZfAikdmG2a4Mp3CQA2afCdoU4tgMCanabrKY="}]}, {"Route": "resources/gallery/unknown-10.11-260325-8.sset85ljxl.png", "AssetFile": "resources/gallery/unknown-10.11-260325-8.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "123495"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1MMYomNZfAikdmG2a4Mp3CQA2afCdoU4tgMCanabrKY=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:45:40 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sset85ljxl"}, {"Name": "integrity", "Value": "sha256-1MMYomNZfAikdmG2a4Mp3CQA2afCdoU4tgMCanabrKY="}, {"Name": "label", "Value": "resources/gallery/unknown-10.11-260325-8.png"}]}, {"Route": "resources/kittens/Georgia-2.5-230325-5.2bu66n3lvb.jpeg", "AssetFile": "resources/kittens/Georgia-2.5-230325-5.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "34052"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Ei8pXpBg79PM69redqw1dFrRbQ7m8bg4alNNITJMe2Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 04:46:36 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2bu66n3lvb"}, {"Name": "integrity", "Value": "sha256-Ei8pXpBg79PM69redqw1dFrRbQ7m8bg4alNNITJMe2Q="}, {"Name": "label", "Value": "resources/kittens/Georgia-2.5-230325-5.jpeg"}]}, {"Route": "resources/kittens/Georgia-2.5-230325-5.jpeg", "AssetFile": "resources/kittens/Georgia-2.5-230325-5.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "34052"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Ei8pXpBg79PM69redqw1dFrRbQ7m8bg4alNNITJMe2Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 04:46:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ei8pXpBg79PM69redqw1dFrRbQ7m8bg4alNNITJMe2Q="}]}, {"Route": "resources/kittens/ben-1.8-230325-2.jb30hc0pal.jpg", "AssetFile": "resources/kittens/ben-1.8-230325-2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "262949"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MU6a8Fp6u9Dtqw5FJn6M6Wk4WIF+IBDMgKJqz6eExCQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 04:50:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jb30hc0pal"}, {"Name": "integrity", "Value": "sha256-MU6a8Fp6u9Dtqw5FJn6M6Wk4WIF+IBDMgKJqz6eExCQ="}, {"Name": "label", "Value": "resources/kittens/ben-1.8-230325-2.jpg"}]}, {"Route": "resources/kittens/ben-1.8-230325-2.jpg", "AssetFile": "resources/kittens/ben-1.8-230325-2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "262949"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MU6a8Fp6u9Dtqw5FJn6M6Wk4WIF+IBDMgKJqz6eExCQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Mar 2025 04:50:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MU6a8Fp6u9Dtqw5FJn6M6Wk4WIF+IBDMgKJqz6eExCQ="}]}, {"Route": "resources/queens/Ghostgums-4-030324-1.6e3ntblqdo.png", "AssetFile": "resources/queens/Ghostgums-4-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "121129"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7omo5IzUsRpR7c2czXzs1l1+6emc944FFdTsSjlV5aM=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:32 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6e3ntblqdo"}, {"Name": "integrity", "Value": "sha256-7omo5IzUsRpR7c2czXzs1l1+6emc944FFdTsSjlV5aM="}, {"Name": "label", "Value": "resources/queens/Ghostgums-4-030324-1.png"}]}, {"Route": "resources/queens/Ghostgums-4-030324-1.png", "AssetFile": "resources/queens/Ghostgums-4-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "121129"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7omo5IzUsRpR7c2czXzs1l1+6emc944FFdTsSjlV5aM=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7omo5IzUsRpR7c2czXzs1l1+6emc944FFdTsSjlV5aM="}]}, {"Route": "resources/queens/Indy-8-010123-1.7w6x1is2yd.png", "AssetFile": "resources/queens/Indy-8-010123-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "224099"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"JwqJ8LK7wid4sihKUEG0G/Kiyl4mP9wceZJY6XJ4YGU=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:40:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7w6x1is2yd"}, {"Name": "integrity", "Value": "sha256-JwqJ8LK7wid4sihKUEG0G/Kiyl4mP9wceZJY6XJ4YGU="}, {"Name": "label", "Value": "resources/queens/Indy-8-010123-1.png"}]}, {"Route": "resources/queens/Indy-8-010123-1.png", "AssetFile": "resources/queens/Indy-8-010123-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "224099"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"JwqJ8LK7wid4sihKUEG0G/Kiyl4mP9wceZJY6XJ4YGU=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:40:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwqJ8LK7wid4sihKUEG0G/Kiyl4mP9wceZJY6XJ4YGU="}]}, {"Route": "resources/queens/Lara-6-010123-1.png", "AssetFile": "resources/queens/Lara-6-010123-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "162077"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nLSQZ1bFOGqFzuH7VtDenh5502K/mbqAYNIMF2Dlqh0=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:42:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nLSQZ1bFOGqFzuH7VtDenh5502K/mbqAYNIMF2Dlqh0="}]}, {"Route": "resources/queens/Lara-6-010123-1.si89cri3cg.png", "AssetFile": "resources/queens/Lara-6-010123-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162077"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nLSQZ1bFOGqFzuH7VtDenh5502K/mbqAYNIMF2Dlqh0=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:42:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "si89cri3cg"}, {"Name": "integrity", "Value": "sha256-nLSQZ1bFOGqFzuH7VtDenh5502K/mbqAYNIMF2Dlqh0="}, {"Name": "label", "Value": "resources/queens/Lara-6-010123-1.png"}]}, {"Route": "resources/queens/Loretta-3-080824-1.jpg", "AssetFile": "resources/queens/Loretta-3-080824-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "408309"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"HJ31MSHMVd3Gj35ntpSMlYyE96l9HbITvp2PuTtel9A=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Oct 2024 20:46:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HJ31MSHMVd3Gj35ntpSMlYyE96l9HbITvp2PuTtel9A="}]}, {"Route": "resources/queens/Loretta-3-080824-1.kghujbn9me.jpg", "AssetFile": "resources/queens/Loretta-3-080824-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "408309"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"HJ31MSHMVd3Gj35ntpSMlYyE96l9HbITvp2PuTtel9A=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Oct 2024 20:46:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kghujbn9me"}, {"Name": "integrity", "Value": "sha256-HJ31MSHMVd3Gj35ntpSMlYyE96l9HbITvp2PuTtel9A="}, {"Name": "label", "Value": "resources/queens/Loretta-3-080824-1.jpg"}]}, {"Route": "resources/queens/<PERSON>_<PERSON>-2-030324-1.9nbop6nggm.png", "AssetFile": "resources/queens/<PERSON>_<PERSON>-2-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "161142"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"9VGYz26L0qQ5PNxhmPRrE+b7F1vbS/Z+pM6RFzl2Cwc=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:45:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9nbop6nggm"}, {"Name": "integrity", "Value": "sha256-9VGYz26L0qQ5PNxhmPRrE+b7F1vbS/Z+pM6RFzl2Cwc="}, {"Name": "label", "Value": "resources/queens/<PERSON>_<PERSON>-2-030324-1.png"}]}, {"Route": "resources/queens/<PERSON>_<PERSON>-2-030324-1.png", "AssetFile": "resources/queens/<PERSON>_<PERSON>-2-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "161142"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"9VGYz26L0qQ5PNxhmPRrE+b7F1vbS/Z+pM6RFzl2Cwc=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:45:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9VGYz26L0qQ5PNxhmPRrE+b7F1vbS/Z+pM6RFzl2Cwc="}]}, {"Route": "resources/queens/Rosie-3-030324-1.d76x8g9yr3.png", "AssetFile": "resources/queens/Rosie-3-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "161115"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vwo+KNuydi62tADkIIFLec8tLJltLB0l26v9SOBodvQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d76x8g9yr3"}, {"Name": "integrity", "Value": "sha256-Vwo+KNuydi62tADkIIFLec8tLJltLB0l26v9SOBodvQ="}, {"Name": "label", "Value": "resources/queens/Rosie-3-030324-1.png"}]}, {"Route": "resources/queens/Rosie-3-030324-1.png", "AssetFile": "resources/queens/Rosie-3-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "161115"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vwo+KNuydi62tADkIIFLec8tLJltLB0l26v9SOBodvQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vwo+KNuydi62tADkIIFLec8tLJltLB0l26v9SOBodvQ="}]}, {"Route": "resources/queens/Sera-1.11-030324-1.ayckz0e0yw.png", "AssetFile": "resources/queens/Sera-1.11-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "231117"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"kmGUDXWiIgNVOsnEJaZcL8T03v7DbY2BZJ9lgMaJjC0=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:45:40 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ayckz0e0yw"}, {"Name": "integrity", "Value": "sha256-kmGUDXWiIgNVOsnEJaZcL8T03v7DbY2BZJ9lgMaJjC0="}, {"Name": "label", "Value": "resources/queens/Sera-1.11-030324-1.png"}]}, {"Route": "resources/queens/Sera-1.11-030324-1.png", "AssetFile": "resources/queens/Sera-1.11-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "231117"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"kmGUDXWiIgNVOsnEJaZcL8T03v7DbY2BZJ9lgMaJjC0=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:45:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmGUDXWiIgNVOsnEJaZcL8T03v7DbY2BZJ9lgMaJjC0="}]}, {"Route": "resources/queens/<PERSON><PERSON><PERSON>_<PERSON>ji-1.9-030324-1.6bms0fhigj.png", "AssetFile": "resources/queens/<PERSON><PERSON><PERSON>_<PERSON>ji-1.9-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "99523"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"8ovg2HpEZ90A9WSUCFM50t5cuBGa5InyYqDRaVkx2ds=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6bms0fhigj"}, {"Name": "integrity", "Value": "sha256-8ovg2HpEZ90A9WSUCFM50t5cuBGa5InyYqDRaVkx2ds="}, {"Name": "label", "Value": "resources/queens/<PERSON><PERSON><PERSON>_<PERSON>ji-1.9-030324-1.png"}]}, {"Route": "resources/queens/<PERSON><PERSON><PERSON>_<PERSON>ji-1.9-030324-1.png", "AssetFile": "resources/queens/<PERSON><PERSON><PERSON>_<PERSON>ji-1.9-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "99523"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"8ovg2HpEZ90A9WSUCFM50t5cuBGa5InyYqDRaVkx2ds=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:44:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8ovg2HpEZ90A9WSUCFM50t5cuBGa5InyYqDRaVkx2ds="}]}, {"Route": "resources/studs/Dennis-10.3-030324-1.ipqucas4it.jpg", "AssetFile": "resources/studs/Dennis-10.3-030324-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "317552"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"SmkXYlOTQqniAMYulhiWrCHYH78JZFjFrzVw64eT1LA=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:34:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ipqucas4it"}, {"Name": "integrity", "Value": "sha256-SmkXYlOTQqniAMYulhiWrCHYH78JZFjFrzVw64eT1LA="}, {"Name": "label", "Value": "resources/studs/Dennis-10.3-030324-1.jpg"}]}, {"Route": "resources/studs/Dennis-10.3-030324-1.jpg", "AssetFile": "resources/studs/Dennis-10.3-030324-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "317552"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"SmkXYlOTQqniAMYulhiWrCHYH78JZFjFrzVw64eT1LA=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:34:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SmkXYlOTQqniAMYulhiWrCHYH78JZFjFrzVw64eT1LA="}]}, {"Route": "resources/studs/Kingsley_Park_Soren-1.6-030324-1.8pkyrtqshx.png", "AssetFile": "resources/studs/Kingsley_Park_Soren-1.6-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "481927"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"TMxcMTDGchxvmdT43/cMN+fmPKJV6mcNUVc+9r1EewQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:33:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8pkyrtqshx"}, {"Name": "integrity", "Value": "sha256-TMxcMTDGchxvmdT43/cMN+fmPKJV6mcNUVc+9r1EewQ="}, {"Name": "label", "Value": "resources/studs/Kingsley_Park_Soren-1.6-030324-1.png"}]}, {"Route": "resources/studs/Kingsley_Park_Soren-1.6-030324-1.png", "AssetFile": "resources/studs/Kingsley_Park_Soren-1.6-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "481927"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"TMxcMTDGchxvmdT43/cMN+fmPKJV6mcNUVc+9r1EewQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:33:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TMxcMTDGchxvmdT43/cMN+fmPKJV6mcNUVc+9r1EewQ="}]}, {"Route": "resources/studs/Rapper-16.2-030324-1.6tahw9syox.png", "AssetFile": "resources/studs/Rapper-16.2-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "621252"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"+nTmZW0yZFYL4zLMVCeZY0Aod/o1K4K9p5jF29mqXNE=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:35:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6tahw9syox"}, {"Name": "integrity", "Value": "sha256-+nTmZW0yZFYL4zLMVCeZY0Aod/o1K4K9p5jF29mqXNE="}, {"Name": "label", "Value": "resources/studs/Rapper-16.2-030324-1.png"}]}, {"Route": "resources/studs/Rapper-16.2-030324-1.png", "AssetFile": "resources/studs/Rapper-16.2-030324-1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "621252"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"+nTmZW0yZFYL4zLMVCeZY0Aod/o1K4K9p5jF29mqXNE=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:35:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+nTmZW0yZFYL4zLMVCeZY0Aod/o1K4K9p5jF29mqXNE="}]}, {"Route": "resources/studs/Yendor_Rhydian_Illusion-3-030324-1.2bu66n3lvb.jpg", "AssetFile": "resources/studs/Yendor_Rhydian_Illusion-3-030324-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "34052"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Ei8pXpBg79PM69redqw1dFrRbQ7m8bg4alNNITJMe2Q=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:32:30 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2bu66n3lvb"}, {"Name": "integrity", "Value": "sha256-Ei8pXpBg79PM69redqw1dFrRbQ7m8bg4alNNITJMe2Q="}, {"Name": "label", "Value": "resources/studs/Yendor_Rhydian_Illusion-3-030324-1.jpg"}]}, {"Route": "resources/studs/Yendor_Rhydian_Illusion-3-030324-1.jpg", "AssetFile": "resources/studs/Yendor_Rhydian_Illusion-3-030324-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "34052"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Ei8pXpBg79PM69redqw1dFrRbQ7m8bg4alNNITJMe2Q=\""}, {"Name": "Last-Modified", "Value": "Sun, 21 Jan 2024 15:32:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ei8pXpBg79PM69redqw1dFrRbQ7m8bg4alNNITJMe2Q="}]}]}