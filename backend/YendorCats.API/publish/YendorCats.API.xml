<?xml version="1.0"?>
<doc>
    <assembly>
        <name>YendorCats.API</name>
    </assembly>
    <members>
        <member name="T:YendorCats.API.Configuration.JwtSettings">
            <summary>
            Configuration settings for JWT authentication
            </summary>
        </member>
        <member name="P:YendorCats.API.Configuration.JwtSettings.Secret">
            <summary>
            Secret key used to sign the JWT token
            </summary>
        </member>
        <member name="P:YendorCats.API.Configuration.JwtSettings.Issuer">
            <summary>
            Issuer of the JWT token
            </summary>
        </member>
        <member name="P:YendorCats.API.Configuration.JwtSettings.Audience">
            <summary>
            Audience of the JWT token
            </summary>
        </member>
        <member name="P:YendorCats.API.Configuration.JwtSettings.ExpiryMinutes">
            <summary>
            Token expiry time in minutes
            </summary>
        </member>
        <member name="P:YendorCats.API.Configuration.JwtSettings.RefreshTokenExpiryDays">
            <summary>
            Refresh token expiry time in days
            </summary>
        </member>
        <member name="T:YendorCats.API.Controllers.AuthController">
            <summary>
            Controller for authentication operations
            </summary>
        </member>
        <member name="M:YendorCats.API.Controllers.AuthController.#ctor(YendorCats.API.Services.IAuthService,YendorCats.API.Services.IUserService,Microsoft.Extensions.Logging.ILogger{YendorCats.API.Controllers.AuthController})">
            <summary>
            Constructor for AuthController
            </summary>
            <param name="authService">The authentication service</param>
            <param name="userService">The user service</param>
            <param name="logger">Logger for controller operations</param>
        </member>
        <member name="M:YendorCats.API.Controllers.AuthController.Login(YendorCats.API.Controllers.LoginModel)">
            <summary>
            Login with username and password
            </summary>
            <param name="loginModel">Login credentials</param>
            <returns>JWT token and refresh token</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.AuthController.Register(YendorCats.API.Controllers.RegisterModel)">
            <summary>
            Register a new user
            </summary>
            <param name="registerModel">Registration information</param>
            <returns>Success message and user ID</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.AuthController.RefreshToken(YendorCats.API.Controllers.RefreshTokenModel)">
            <summary>
            Refresh an access token using a refresh token
            </summary>
            <param name="refreshModel">The refresh token</param>
            <returns>New JWT token and refresh token</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.AuthController.Logout">
            <summary>
            Revoke a refresh token (logout)
            </summary>
            <returns>Success message</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.AuthController.ValidateToken(YendorCats.API.Controllers.ValidateTokenModel)">
            <summary>
            Validate a token
            </summary>
            <param name="tokenModel">The token to validate</param>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="T:YendorCats.API.Controllers.LoginModel">
            <summary>
            Model for login requests
            </summary>
        </member>
        <member name="P:YendorCats.API.Controllers.LoginModel.Username">
            <summary>
            Username for login
            </summary>
        </member>
        <member name="P:YendorCats.API.Controllers.LoginModel.Password">
            <summary>
            Password for login
            </summary>
        </member>
        <member name="T:YendorCats.API.Controllers.RegisterModel">
            <summary>
            Model for user registration
            </summary>
        </member>
        <member name="P:YendorCats.API.Controllers.RegisterModel.Username">
            <summary>
            Username for the new account
            </summary>
        </member>
        <member name="P:YendorCats.API.Controllers.RegisterModel.Email">
            <summary>
            Email address
            </summary>
        </member>
        <member name="P:YendorCats.API.Controllers.RegisterModel.FirstName">
            <summary>
            First name
            </summary>
        </member>
        <member name="P:YendorCats.API.Controllers.RegisterModel.LastName">
            <summary>
            Last name
            </summary>
        </member>
        <member name="P:YendorCats.API.Controllers.RegisterModel.Password">
            <summary>
            Password for the new account
            </summary>
        </member>
        <member name="T:YendorCats.API.Controllers.RefreshTokenModel">
            <summary>
            Model for refresh token requests
            </summary>
        </member>
        <member name="P:YendorCats.API.Controllers.RefreshTokenModel.RefreshToken">
            <summary>
            The refresh token
            </summary>
        </member>
        <member name="T:YendorCats.API.Controllers.ValidateTokenModel">
            <summary>
            Model for token validation requests
            </summary>
        </member>
        <member name="P:YendorCats.API.Controllers.ValidateTokenModel.Token">
            <summary>
            The token to validate
            </summary>
        </member>
        <member name="T:YendorCats.API.Controllers.CatGalleryController">
            <summary>
            Controller for managing cat gallery images with metadata extracted from filenames
            </summary>
        </member>
        <member name="M:YendorCats.API.Controllers.CatGalleryController.#ctor(Microsoft.AspNetCore.Hosting.IWebHostEnvironment,Microsoft.Extensions.Logging.ILogger{YendorCats.API.Controllers.CatGalleryController})">
            <summary>
            Constructor for CatGalleryController
            </summary>
        </member>
        <member name="M:YendorCats.API.Controllers.CatGalleryController.GetCatImages(System.String)">
            <summary>
            Gets all cat gallery images from all categories or a specific category
            </summary>
            <param name="category">Optional category filter (studs, queens, kittens, gallery)</param>
            <returns>A list of cat gallery images with metadata</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(System.String,System.String,System.Boolean)">
            <summary>
            Gets cat gallery images for a specific category, ordered by the specified criteria
            </summary>
            <param name="category">Category to scan (studs, queens, kittens, gallery)</param>
            <param name="orderBy">Order by field (date, name, age)</param>
            <param name="descending">Whether to sort in descending order</param>
            <returns>A list of sorted cat gallery images</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatGalleryController.ScanDirectoryForImages(System.String)">
            <summary>
            Scan a directory for cat gallery images and extract metadata from filenames
            </summary>
        </member>
        <member name="M:YendorCats.API.Controllers.CatGalleryController.CreateFallbackImage(System.String,System.String,System.String)">
            <summary>
            Create a fallback image entry for files that don't match the naming convention
            </summary>
        </member>
        <member name="T:YendorCats.API.Controllers.CatsController">
            <summary>
            Controller for cat-related operations
            </summary>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.#ctor(YendorCats.API.Services.ICatService,Microsoft.Extensions.Logging.ILogger{YendorCats.API.Controllers.CatsController})">
            <summary>
            Constructor for the cats controller
            </summary>
            <param name="catService">Service for cat operations</param>
            <param name="logger">Logger for the controller</param>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.GetAllCats(System.Boolean,System.Boolean)">
            <summary>
            Get all cats
            </summary>
            <param name="includeImages">Whether to include images in the response</param>
            <param name="includeInactive">Whether to include inactive cats</param>
            <returns>Collection of all cats</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.GetCatById(System.Int32,System.Boolean)">
            <summary>
            Get a cat by ID
            </summary>
            <param name="id">ID of the cat to retrieve</param>
            <param name="includeImages">Whether to include images in the response</param>
            <returns>Cat with the specified ID</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.AddCat(YendorCats.API.Models.Cat)">
            <summary>
            Add a new cat
            </summary>
            <param name="cat">Cat to add</param>
            <returns>Newly created cat</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.UpdateCat(System.Int32,YendorCats.API.Models.Cat)">
            <summary>
            Update an existing cat
            </summary>
            <param name="id">ID of the cat to update</param>
            <param name="cat">Updated cat data</param>
            <returns>No content if successful</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.DeleteCat(System.Int32)">
            <summary>
            Delete a cat
            </summary>
            <param name="id">ID of the cat to delete</param>
            <returns>No content if successful</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.GetCatsByAvailability(System.Boolean,System.Boolean)">
            <summary>
            Get cats by availability status
            </summary>
            <param name="available">Availability status to filter by</param>
            <param name="includeImages">Whether to include images in the response</param>
            <returns>Filtered collection of cats</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.GetCatsByBreed(System.String,System.Boolean,System.Boolean)">
            <summary>
            Get cats by breed
            </summary>
            <param name="breed">Breed to filter by</param>
            <param name="includeImages">Whether to include images in the response</param>
            <param name="includeInactive">Whether to include inactive cats</param>
            <returns>Filtered collection of cats</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.SetCatAvailability(System.Int32,System.Boolean)">
            <summary>
            Set cat availability status
            </summary>
            <param name="id">ID of the cat</param>
            <param name="isAvailable">New availability status</param>
            <returns>No content if successful</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.SetCatActiveStatus(System.Int32,System.Boolean)">
            <summary>
            Set cat active status
            </summary>
            <param name="id">ID of the cat</param>
            <param name="isActive">New active status</param>
            <returns>No content if successful</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.CatsController.GetAllBreeds">
            <summary>
            Get all available breeds
            </summary>
            <returns>Collection of all breeds</returns>
        </member>
        <member name="T:YendorCats.API.Controllers.UsersController">
            <summary>
            Controller for user management operations
            </summary>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.#ctor(YendorCats.API.Services.IUserService,Microsoft.Extensions.Logging.ILogger{YendorCats.API.Controllers.UsersController})">
            <summary>
            Constructor for the UsersController
            </summary>
            <param name="userService">The user service for user operations</param>
            <param name="logger">Logger for controller operations</param>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.GetAllUsers">
            <summary>
            Get all users (Admin only)
            </summary>
            <returns>List of all users</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.GetUser(System.Int32)">
            <summary>
            Get a user by ID (Admin only)
            </summary>
            <param name="id">User ID</param>
            <returns>User details</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.GetCurrentUser">
            <summary>
            Get the current logged-in user
            </summary>
            <returns>Current user details</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.CreateUser(YendorCats.API.Models.User,System.String)">
            <summary>
            Create a new user (Admin only)
            </summary>
            <param name="user">User data</param>
            <param name="password">Password for the new user</param>
            <returns>Created user</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.UpdateUser(System.Int32,YendorCats.API.Models.User)">
            <summary>
            Update an existing user (Admin or self)
            </summary>
            <param name="id">User ID</param>
            <param name="user">Updated user data</param>
            <returns>No content if successful</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.DeleteUser(System.Int32)">
            <summary>
            Delete a user (Admin only)
            </summary>
            <param name="id">User ID to delete</param>
            <returns>No content if successful</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.ChangePassword(System.Int32,System.String,System.String)">
            <summary>
            Change user password (Admin or self)
            </summary>
            <param name="id">User ID</param>
            <param name="currentPassword">Current password</param>
            <param name="newPassword">New password</param>
            <returns>Ok if successful, BadRequest if password is incorrect</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.CheckUsernameAvailability(System.String)">
            <summary>
            Check if a username is available
            </summary>
            <param name="username">Username to check</param>
            <returns>True if available, false if taken</returns>
        </member>
        <member name="M:YendorCats.API.Controllers.UsersController.CheckEmailAvailability(System.String)">
            <summary>
            Check if an email is available
            </summary>
            <param name="email">Email to check</param>
            <returns>True if available, false if taken</returns>
        </member>
        <member name="T:YendorCats.API.Data.AppDbContext">
            <summary>
            Database context for the Yendor Cats application
            </summary>
        </member>
        <member name="M:YendorCats.API.Data.AppDbContext.#ctor(Microsoft.EntityFrameworkCore.DbContextOptions{YendorCats.API.Data.AppDbContext})">
            <summary>
            Constructor for the database context
            </summary>
            <param name="options">The DbContext options</param>
        </member>
        <member name="P:YendorCats.API.Data.AppDbContext.Cats">
            <summary>
            Collection of cats in the database
            </summary>
        </member>
        <member name="P:YendorCats.API.Data.AppDbContext.CatImages">
            <summary>
            Collection of cat images in the database
            </summary>
        </member>
        <member name="P:YendorCats.API.Data.AppDbContext.Users">
            <summary>
            Collection of users in the database
            </summary>
        </member>
        <member name="M:YendorCats.API.Data.AppDbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            Configure the database model
            </summary>
            <param name="modelBuilder">The model builder</param>
        </member>
        <member name="T:YendorCats.API.Middleware.ErrorHandlingMiddleware">
            <summary>
            Middleware for handling exceptions globally and returning appropriate HTTP responses
            </summary>
        </member>
        <member name="M:YendorCats.API.Middleware.ErrorHandlingMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Logging.ILogger{YendorCats.API.Middleware.ErrorHandlingMiddleware},Microsoft.Extensions.Hosting.IHostEnvironment)">
            <summary>
            Constructor for the error handling middleware
            </summary>
            <param name="next">The next middleware in the pipeline</param>
            <param name="logger">Logger for recording exceptions</param>
            <param name="environment">Host environment to determine if in development mode</param>
        </member>
        <member name="M:YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Invoke method called by the middleware pipeline
            </summary>
            <param name="context">The HTTP context</param>
            <returns>A task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Middleware.ErrorHandlingMiddleware.HandleExceptionAsync(Microsoft.AspNetCore.Http.HttpContext,System.Exception)">
            <summary>
            Handles the exception and generates an appropriate HTTP response
            </summary>
            <param name="context">The HTTP context</param>
            <param name="exception">The exception that was thrown</param>
            <returns>A task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Middleware.ErrorHandlingMiddleware.GetErrorTitle(System.Net.HttpStatusCode)">
            <summary>
            Gets a human-readable title for the HTTP status code
            </summary>
            <param name="statusCode">The HTTP status code</param>
            <returns>A descriptive title for the status code</returns>
        </member>
        <member name="T:YendorCats.API.Middleware.FileWatcherMiddleware">
            <summary>
            Middleware to watch for file changes in the frontend directory and copy them to wwwroot.
            </summary>
        </member>
        <member name="M:YendorCats.API.Middleware.FileWatcherMiddleware.UseFileWatcher(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Hosting.IWebHostEnvironment,Microsoft.Extensions.Logging.ILogger{System.IO.FileSystemWatcher})">
            <summary>
            Configures a file system watcher to monitor changes in the frontend directory
            and automatically copy updated files to wwwroot.
            </summary>
            <param name="app">The application builder</param>
            <param name="env">The web hosting environment</param>
            <param name="logger">The logger</param>
            <returns>The application builder</returns>
        </member>
        <member name="M:YendorCats.API.Middleware.FileWatcherMiddleware.CopyFileToWwwroot(System.String,System.String,System.String,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Copies a file from the frontend directory to wwwroot.
            </summary>
        </member>
        <member name="T:YendorCats.API.Middleware.LiveReloadMiddleware">
            <summary>
            Middleware to handle live reload functionality for frontend development.
            </summary>
        </member>
        <member name="M:YendorCats.API.Middleware.LiveReloadMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Hosting.IHostEnvironment)">
            <summary>
            Initializes a new instance of the <see cref="T:YendorCats.API.Middleware.LiveReloadMiddleware"/> class.
            </summary>
            <param name="next">The next middleware in the pipeline.</param>
            <param name="environment">The hosting environment.</param>
        </member>
        <member name="M:YendorCats.API.Middleware.LiveReloadMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Invokes the middleware.
            </summary>
            <param name="context">The HTTP context.</param>
        </member>
        <member name="M:YendorCats.API.Middleware.LiveReloadMiddleware.NotifyChange">
            <summary>
            Updates the version number to trigger a browser refresh.
            </summary>
        </member>
        <member name="T:YendorCats.API.Middleware.LiveReloadMiddlewareExtensions">
            <summary>
            Extension methods for the LiveReloadMiddleware.
            </summary>
        </member>
        <member name="M:YendorCats.API.Middleware.LiveReloadMiddlewareExtensions.UseLiveReload(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Adds the live reload middleware to the application pipeline.
            </summary>
            <param name="app">The application builder.</param>
            <returns>The application builder.</returns>
        </member>
        <member name="M:YendorCats.API.Middleware.LiveReloadMiddlewareExtensions.NotifyLiveReloadChange">
            <summary>
            Notifies the live reload middleware that a change has occurred.
            </summary>
        </member>
        <member name="T:YendorCats.API.Migrations.InitialCreate">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Migrations.InitialCreate.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Migrations.InitialCreate.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Migrations.InitialCreate.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:YendorCats.API.Models.Breed">
            <summary>
            Represents a cat breed in the Yendor Cats system
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Breed.Id">
            <summary>
            Unique identifier for the breed
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Breed.Name">
            <summary>
            Name of the breed
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Breed.Description">
            <summary>
            Detailed description of the breed
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Breed.Origin">
            <summary>
            Origin country of the breed
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Breed.LifeExpectancy">
            <summary>
            Average life expectancy range
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Breed.Temperament">
            <summary>
            Temperament characteristics
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Breed.Cats">
            <summary>
            Collection of cats belonging to this breed
            </summary>
        </member>
        <member name="T:YendorCats.API.Models.Cat">
            <summary>
            Represents a cat in the system
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Id">
            <summary>
            The unique identifier for the cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Name">
            <summary>
            The name of the cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Breed">
            <summary>
            The breed of the cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.DateOfBirth">
            <summary>
            The date of birth of the cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Gender">
            <summary>
            The gender of the cat (M/F)
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Color">
            <summary>
            The color of the cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Description">
            <summary>
            A description of the cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Price">
            <summary>
            The price of the cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.IsAvailable">
            <summary>
            Whether the cat is available for purchase
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.IsActive">
            <summary>
            Whether the cat is active in the system
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Markings">
            <summary>
            Any special markings or characteristics of the cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.CreatedAt">
            <summary>
            The date the cat was added to the system
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.UpdatedAt">
            <summary>
            The date the cat was last updated
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Images">
            <summary>
            The collection of images for this cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.MotherId">
            <summary>
            The parent/mother of this cat (if known)
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.FatherId">
            <summary>
            The parent/father of this cat (if known)
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Mother">
            <summary>
            Navigation property for the mother
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Father">
            <summary>
            Navigation property for the father
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Cat.Offspring">
            <summary>
            Navigation property for offspring where this cat is the mother
            </summary>
        </member>
        <member name="T:YendorCats.API.Models.CatGalleryImage">
            <summary>
            Represents a cat image with metadata extracted from the filename.
            Filename format: [cat's name]-[age]-[date(DDMMYY)]-[order].jpg
            Example: Georgia-2.6-230325-1.jpg
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatGalleryImage.Id">
            <summary>
            The unique identifier for the image
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatGalleryImage.CatName">
            <summary>
            The cat's name extracted from the filename
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatGalleryImage.Age">
            <summary>
            The cat's age in years extracted from the filename
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatGalleryImage.DateTaken">
            <summary>
            The date the image was taken
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatGalleryImage.OrderNumber">
            <summary>
            The ordering number for images taken on the same date
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatGalleryImage.ImageUrl">
            <summary>
            The URL path to the image
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatGalleryImage.Category">
            <summary>
            The category of the cat (e.g., "studs", "queens", "kittens")
            </summary>
        </member>
        <member name="M:YendorCats.API.Models.CatGalleryImage.FromFilename(System.String,System.String,System.String)">
            <summary>
            Parse a filename to extract cat image metadata
            </summary>
            <param name="filePath">The full path to the image file</param>
            <param name="relativePath">The relative URL path to access the image</param>
            <param name="category">The category folder the image belongs to</param>
            <returns>A CatGalleryImage object with extracted metadata, or null if parsing fails</returns>
        </member>
        <member name="T:YendorCats.API.Models.CatImage">
            <summary>
            Represents an image associated with a cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.Id">
            <summary>
            The unique identifier for the image
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.CatId">
            <summary>
            The ID of the cat this image belongs to
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.FileName">
            <summary>
            The file name of the image
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.FilePath">
            <summary>
            The file path where the image is stored
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.ContentType">
            <summary>
            The content type (MIME type) of the image
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.Caption">
            <summary>
            The caption or description for the image
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.IsPrimary">
            <summary>
            Indicates if this is the primary/main image for the cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.DisplayOrder">
            <summary>
            The order/sequence number for display purposes
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.UploadedAt">
            <summary>
            The date the image was uploaded
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.FileSize">
            <summary>
            The size of the image file in bytes
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.Width">
            <summary>
            The width of the image in pixels
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.Height">
            <summary>
            The height of the image in pixels
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.CatImage.Cat">
            <summary>
            Navigation property to the associated cat
            </summary>
        </member>
        <member name="T:YendorCats.API.Models.Litter">
            <summary>
            Represents a litter of kittens in the Yendor Cats system
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.Id">
            <summary>
            Unique identifier for the litter
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.Name">
            <summary>
            Name or identifier for the litter
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.Description">
            <summary>
            Detailed description of the litter
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.BirthDate">
            <summary>
            Date the kittens were born
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.FatherId">
            <summary>
            ID of the father cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.Father">
            <summary>
            Navigation property to the father
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.MotherId">
            <summary>
            ID of the mother cat
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.Mother">
            <summary>
            Navigation property to the mother
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.AvailableDate">
            <summary>
            Date the kittens will be available for new homes
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.Kittens">
            <summary>
            Collection of kittens in this litter
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.LitterSize">
            <summary>
            Number of kittens in the litter
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.Notes">
            <summary>
            Additional notes about the litter
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.IsFeatured">
            <summary>
            Whether this litter is featured on the website
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.Status">
            <summary>
            Current status of the litter (Announced, Born, Available, etc.)
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.CreatedDate">
            <summary>
            Date this record was created
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.Litter.LastUpdated">
            <summary>
            Date this record was last updated
            </summary>
        </member>
        <member name="T:YendorCats.API.Models.User">
            <summary>
            Represents a user in the system
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.Id">
            <summary>
            The unique identifier for the user
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.Username">
            <summary>
            The username used for authentication
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.Email">
            <summary>
            The user's email address
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.FirstName">
            <summary>
            The user's first name
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.LastName">
            <summary>
            The user's last name
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.PasswordHash">
            <summary>
            The hashed password (never returned in API responses)
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.PasswordSalt">
            <summary>
            Random salt used for password hashing (never returned in API responses)
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.Role">
            <summary>
            The user's role (Admin, Editor, Viewer)
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.IsActive">
            <summary>
            Whether the user account is active
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.CreatedAt">
            <summary>
            The date and time when the user was created
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.LastLoginAt">
            <summary>
            The date and time when the user last logged in
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.RefreshToken">
            <summary>
            The refresh token for authentication
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.RefreshTokenExpiryTime">
            <summary>
            The expiry date for the refresh token
            </summary>
        </member>
        <member name="P:YendorCats.API.Models.User.FullName">
            <summary>
            Gets the full name of the user
            </summary>
        </member>
        <member name="T:YendorCats.API.Services.AuthService">
            <summary>
            Service for handling authentication and authorization
            </summary>
        </member>
        <member name="M:YendorCats.API.Services.AuthService.#ctor(YendorCats.API.Data.AppDbContext,Microsoft.Extensions.Logging.ILogger{YendorCats.API.Services.AuthService},Microsoft.Extensions.Options.IOptions{YendorCats.API.Configuration.JwtSettings},ISecretsManagerService,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            Constructor for the authentication service
            </summary>
            <param name="context">Database context</param>
            <param name="logger">Logger for logging operations</param>
            <param name="jwtSettings">JWT configuration settings</param>
            <param name="secretsManager">AWS Secrets Manager service</param>
            <param name="environment">Web host environment</param>
        </member>
        <member name="M:YendorCats.API.Services.AuthService.AuthenticateAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.ValidateToken(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.GenerateRefreshTokenAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.RefreshTokenAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.RevokeRefreshTokenAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.GetUserIdFromToken(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.GetUsernameFromToken(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.GetRoleFromToken(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.HashPassword(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.VerifyPassword(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.AuthService.GenerateJwtToken(YendorCats.API.Models.User)">
            <summary>
            Generates a JWT token for the specified user
            </summary>
            <param name="user">The user to generate a token for</param>
            <returns>A JWT token string</returns>
        </member>
        <member name="M:YendorCats.API.Services.AuthService.GetTokenValidationParameters">
            <summary>
            Gets the token validation parameters with the appropriate configuration
            </summary>
            <returns>Token validation parameters</returns>
        </member>
        <member name="T:YendorCats.API.Services.CatService">
            <summary>
            Service for managing cats in the system
            </summary>
        </member>
        <member name="M:YendorCats.API.Services.CatService.#ctor(YendorCats.API.Data.AppDbContext,Microsoft.Extensions.Logging.ILogger{YendorCats.API.Services.CatService})">
            <summary>
            Constructor for the cat service
            </summary>
            <param name="context">Database context</param>
            <param name="logger">Logger for logging operations</param>
        </member>
        <member name="M:YendorCats.API.Services.CatService.GetAllCatsAsync(System.Boolean,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.CatService.GetCatByIdAsync(System.Int32,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.CatService.GetCatsByBreedAsync(System.String,System.Boolean,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.CatService.GetCatsByAvailabilityAsync(System.Boolean,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.CatService.AddCatAsync(YendorCats.API.Models.Cat)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.CatService.UpdateCatAsync(YendorCats.API.Models.Cat)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.CatService.DeleteCatAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.CatService.SetAvailabilityAsync(System.Int32,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.CatService.SetActiveStatusAsync(System.Int32,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.CatService.GetAllBreedsAsync">
            <inheritdoc />
        </member>
        <member name="T:YendorCats.API.Services.IAuthService">
            <summary>
            Interface for authentication and authorization operations
            </summary>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.AuthenticateAsync(System.String,System.String)">
            <summary>
            Authenticate a user and generate a JWT token
            </summary>
            <param name="username">Username</param>
            <param name="password">Password</param>
            <returns>A tuple containing JWT token and refresh token if authentication is successful</returns>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.ValidateToken(System.String)">
            <summary>
            Validate a JWT token
            </summary>
            <param name="token">The JWT token to validate</param>
            <returns>True if the token is valid, false otherwise</returns>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.GenerateRefreshTokenAsync(System.Int32)">
            <summary>
            Generate a new refresh token for a user
            </summary>
            <param name="userId">The ID of the user</param>
            <returns>The refresh token</returns>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.RefreshTokenAsync(System.String)">
            <summary>
            Verify a refresh token and generate a new JWT token
            </summary>
            <param name="refreshToken">The refresh token</param>
            <returns>A tuple containing a new JWT token and refresh token if the refresh token is valid</returns>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.RevokeRefreshTokenAsync(System.Int32)">
            <summary>
            Revoke a refresh token
            </summary>
            <param name="userId">The ID of the user</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.GetUserIdFromToken(System.String)">
            <summary>
            Get the user ID from a JWT token
            </summary>
            <param name="token">The JWT token</param>
            <returns>The user ID from the token</returns>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.GetUsernameFromToken(System.String)">
            <summary>
            Get the username from a JWT token
            </summary>
            <param name="token">The JWT token</param>
            <returns>The username from the token</returns>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.GetRoleFromToken(System.String)">
            <summary>
            Get the user role from a JWT token
            </summary>
            <param name="token">The JWT token</param>
            <returns>The user role from the token</returns>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.HashPassword(System.String)">
            <summary>
            Hash a password
            </summary>
            <param name="password">The password to hash</param>
            <returns>A tuple containing the password hash and salt</returns>
        </member>
        <member name="M:YendorCats.API.Services.IAuthService.VerifyPassword(System.String,System.String,System.String)">
            <summary>
            Verify a password against a hash
            </summary>
            <param name="password">The password to verify</param>
            <param name="storedHash">The hash to verify against</param>
            <param name="storedSalt">The salt used for hashing</param>
            <returns>True if the password matches the hash, false otherwise</returns>
        </member>
        <member name="T:YendorCats.API.Services.ICatService">
            <summary>
            Interface for cat-related operations
            </summary>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.GetAllCatsAsync(System.Boolean,System.Boolean)">
            <summary>
            Get all cats
            </summary>
            <param name="includeImages">Whether to include images in the result</param>
            <param name="includeInactive">Whether to include inactive cats</param>
            <returns>All cats matching the criteria</returns>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.GetCatByIdAsync(System.Int32,System.Boolean)">
            <summary>
            Get a cat by ID
            </summary>
            <param name="id">The ID of the cat to retrieve</param>
            <param name="includeImages">Whether to include images in the result</param>
            <returns>The requested cat or null if not found</returns>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.GetCatsByBreedAsync(System.String,System.Boolean,System.Boolean)">
            <summary>
            Get cats by breed
            </summary>
            <param name="breed">The breed to filter by</param>
            <param name="includeImages">Whether to include images in the result</param>
            <param name="includeInactive">Whether to include inactive cats</param>
            <returns>All cats of the specified breed</returns>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.GetCatsByAvailabilityAsync(System.Boolean,System.Boolean)">
            <summary>
            Get cats by availability status
            </summary>
            <param name="available">The availability status to filter by</param>
            <param name="includeImages">Whether to include images in the result</param>
            <returns>All cats with the specified availability status</returns>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.AddCatAsync(YendorCats.API.Models.Cat)">
            <summary>
            Add a new cat
            </summary>
            <param name="cat">The cat to add</param>
            <returns>The added cat with its new ID</returns>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.UpdateCatAsync(YendorCats.API.Models.Cat)">
            <summary>
            Update an existing cat
            </summary>
            <param name="cat">The updated cat data</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.DeleteCatAsync(System.Int32)">
            <summary>
            Delete a cat
            </summary>
            <param name="id">The ID of the cat to delete</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.SetAvailabilityAsync(System.Int32,System.Boolean)">
            <summary>
            Set a cat's availability status
            </summary>
            <param name="id">The ID of the cat</param>
            <param name="isAvailable">The new availability status</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.SetActiveStatusAsync(System.Int32,System.Boolean)">
            <summary>
            Set a cat's active status
            </summary>
            <param name="id">The ID of the cat</param>
            <param name="isActive">The new active status</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.ICatService.GetAllBreedsAsync">
            <summary>
            Get all available breeds
            </summary>
            <returns>All breeds in the system</returns>
        </member>
        <member name="T:YendorCats.API.Services.IImageService">
            <summary>
            Interface for image-related operations
            </summary>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.GetImagesForCatAsync(System.Int32)">
            <summary>
            Get all images for a cat
            </summary>
            <param name="catId">The ID of the cat</param>
            <returns>All images for the cat</returns>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.GetImageByIdAsync(System.Int32)">
            <summary>
            Get an image by ID
            </summary>
            <param name="id">The ID of the image to retrieve</param>
            <returns>The requested image or null if not found</returns>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.GetPrimaryImageForCatAsync(System.Int32)">
            <summary>
            Get the primary image for a cat
            </summary>
            <param name="catId">The ID of the cat</param>
            <returns>The primary image for the cat or null if not found</returns>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.AddImageAsync(System.Int32,System.String,System.String,System.IO.Stream,System.String,System.Boolean)">
            <summary>
            Add a new image
            </summary>
            <param name="catId">The ID of the cat</param>
            <param name="fileName">The filename</param>
            <param name="contentType">The content type (MIME type)</param>
            <param name="fileStream">The file stream</param>
            <param name="caption">Optional caption</param>
            <param name="isPrimary">Whether this is the primary image</param>
            <returns>The added image with its new ID</returns>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.UpdateImageAsync(YendorCats.API.Models.CatImage)">
            <summary>
            Update an existing image
            </summary>
            <param name="image">The updated image data</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.DeleteImageAsync(System.Int32)">
            <summary>
            Delete an image
            </summary>
            <param name="id">The ID of the image to delete</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.SetAsPrimaryAsync(System.Int32)">
            <summary>
            Set an image as the primary image for a cat
            </summary>
            <param name="imageId">The ID of the image</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.GetImageFilePathAsync(System.Int32)">
            <summary>
            Get the physical file path for an image
            </summary>
            <param name="imageId">The ID of the image</param>
            <returns>The physical file path for the image or null if not found</returns>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.SaveImageFileAsync(System.String,System.IO.Stream)">
            <summary>
            Save an image file to disk
            </summary>
            <param name="fileName">The filename</param>
            <param name="fileStream">The file stream</param>
            <returns>The file path where the image was saved</returns>
        </member>
        <member name="M:YendorCats.API.Services.IImageService.DeleteImageFileAsync(System.String)">
            <summary>
            Delete an image file from disk
            </summary>
            <param name="filePath">The file path of the image to delete</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="T:YendorCats.API.Services.ImageService">
            <summary>
            Service for managing cat images
            </summary>
        </member>
        <member name="M:YendorCats.API.Services.ImageService.#ctor(YendorCats.API.Data.AppDbContext,Microsoft.Extensions.Logging.ILogger{YendorCats.API.Services.ImageService},Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            Constructor for the image service
            </summary>
            <param name="context">Database context</param>
            <param name="logger">Logger for logging operations</param>
            <param name="environment">Web host environment for file paths</param>
        </member>
        <member name="M:YendorCats.API.Services.ImageService.GetImagesForCatAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.GetImageByIdAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.GetPrimaryImageForCatAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.AddImageAsync(System.Int32,System.String,System.String,System.IO.Stream,System.String,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.UpdateImageAsync(YendorCats.API.Models.CatImage)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.DeleteImageAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.SetAsPrimaryAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.GetImageFilePathAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.SaveImageFileAsync(System.String,System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.DeleteImageFileAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.ImageService.ResetPrimaryFlagForCatAsync(System.Int32)">
            <summary>
            Reset the primary flag for all images of a cat
            </summary>
            <param name="catId">The ID of the cat</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.ImageService.SetNewPrimaryImageAsync(System.Int32)">
            <summary>
            Set a new primary image for a cat after the current primary is deleted
            </summary>
            <param name="catId">The ID of the cat</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.ImageService.GetNextDisplayOrderAsync(System.Int32)">
            <summary>
            Get the next display order value for a cat's images
            </summary>
            <param name="catId">The ID of the cat</param>
            <returns>The next display order value</returns>
        </member>
        <member name="T:YendorCats.API.Services.IUserService">
            <summary>
            Interface for user-related operations
            </summary>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.GetAllUsersAsync">
            <summary>
            Get all users
            </summary>
            <returns>All users in the system</returns>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.GetUserByIdAsync(System.Int32)">
            <summary>
            Get a user by ID
            </summary>
            <param name="id">The ID of the user to retrieve</param>
            <returns>The requested user or null if not found</returns>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.GetUserByUsernameAsync(System.String)">
            <summary>
            Get a user by username
            </summary>
            <param name="username">The username to search for</param>
            <returns>The requested user or null if not found</returns>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.GetUserByEmailAsync(System.String)">
            <summary>
            Get a user by email
            </summary>
            <param name="email">The email to search for</param>
            <returns>The requested user or null if not found</returns>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.AddUserAsync(YendorCats.API.Models.User,System.String)">
            <summary>
            Add a new user
            </summary>
            <param name="user">The user to add</param>
            <param name="password">The plaintext password (will be hashed)</param>
            <returns>The added user with its new ID</returns>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.UpdateUserAsync(YendorCats.API.Models.User)">
            <summary>
            Update an existing user
            </summary>
            <param name="user">The updated user data</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.DeleteUserAsync(System.Int32)">
            <summary>
            Delete a user
            </summary>
            <param name="id">The ID of the user to delete</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.ChangePasswordAsync(System.Int32,System.String,System.String)">
            <summary>
            Change a user's password
            </summary>
            <param name="userId">The ID of the user</param>
            <param name="currentPassword">The current password</param>
            <param name="newPassword">The new password</param>
            <returns>True if the password was changed, false otherwise</returns>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.IsUsernameAvailableAsync(System.String)">
            <summary>
            Check if a username is available
            </summary>
            <param name="username">The username to check</param>
            <returns>True if the username is available, false otherwise</returns>
        </member>
        <member name="M:YendorCats.API.Services.IUserService.IsEmailAvailableAsync(System.String)">
            <summary>
            Check if an email is available
            </summary>
            <param name="email">The email to check</param>
            <returns>True if the email is available, false otherwise</returns>
        </member>
        <member name="T:YendorCats.API.Services.UserService">
            <summary>
            Service for managing users in the system
            </summary>
        </member>
        <member name="M:YendorCats.API.Services.UserService.#ctor(YendorCats.API.Data.AppDbContext,YendorCats.API.Services.IAuthService,Microsoft.Extensions.Logging.ILogger{YendorCats.API.Services.UserService})">
            <summary>
            Constructor for the user service
            </summary>
            <param name="context">Database context</param>
            <param name="authService">Authentication service for password hashing</param>
            <param name="logger">Logger for logging operations</param>
        </member>
        <member name="M:YendorCats.API.Services.UserService.GetAllUsersAsync">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.UserService.GetUserByIdAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.UserService.GetUserByUsernameAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.UserService.GetUserByEmailAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.UserService.AddUserAsync(YendorCats.API.Models.User,System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.UserService.UpdateUserAsync(YendorCats.API.Models.User)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.UserService.DeleteUserAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.UserService.ChangePasswordAsync(System.Int32,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.UserService.IsUsernameAvailableAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:YendorCats.API.Services.UserService.IsEmailAvailableAsync(System.String)">
            <inheritdoc />
        </member>
    </members>
</doc>
