/*
 * Yendor Cats - Base Styles
 * Contains CSS reset and base element styling
 */

/* Reset */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-body);
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--accent-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--accent-secondary);
}

ul, ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Form elements */
button, input, textarea, select {
    font-family: var(--font-body);
    font-size: 1rem;
    line-height: 1.6;
    color: inherit;
}

button {
    cursor: pointer;
    border: none;
    background: none;
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section-padding {
    padding: var(--spacing-xl) 0;
}

.bg-primary {
    background-color: var(--bg-primary);
}

.bg-secondary {
    background-color: var(--bg-secondary);
}

.text-center {
    text-align: center;
}

/* Utility Classes */
.hidden {
    display: none;
}

.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.shadow-img {
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
}

.shadow-img:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Media Queries */
@media (min-width: 768px) {
    h1 {
        font-size: 3rem;
    }
    
    h2 {
        font-size: 2.5rem;
    }
    
    h3 {
        font-size: 2rem;
    }
    
    .container {
        padding: 0 var(--spacing-lg);
    }
}

@media (min-width: 992px) {
    html {
        font-size: 18px;
    }
} 