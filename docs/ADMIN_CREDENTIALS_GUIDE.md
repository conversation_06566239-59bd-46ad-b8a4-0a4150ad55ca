# 🔐 Admin Credentials & User Management Guide

## **Default Admin Credentials**

### **Current Default Login**
```
Username: admin
Email: <EMAIL>
Password: YendorAdmin123!
```

### **⚠️ IMPORTANT: Change Default Password Immediately**
The default password should be changed immediately after first login for security.

## **🔧 Customizing Default Admin Credentials**

### **Method 1: Environment Variables**
```bash
# Set custom default admin credentials
export YENDOR_DEFAULT_ADMIN_USERNAME="your_admin_username"
export YENDOR_DEFAULT_ADMIN_EMAIL="<EMAIL>"
export YENDOR_DEFAULT_ADMIN_PASSWORD="YourSecurePassword123!"
```

### **Method 2: .env File**
```bash
# Add to your .env file
YENDOR_DEFAULT_ADMIN_USERNAME=your_admin_username
YENDOR_DEFAULT_ADMIN_EMAIL=<EMAIL>
YENDOR_DEFAULT_ADMIN_PASSWORD=YourSecurePassword123!
```

### **Method 3: Setup Script**
```bash
# Run the environment setup script
./scripts/setup-env.sh

# It will prompt for custom admin credentials
```

## **👤 Admin User Management**

### **Admin Roles**
| Role | Permissions | Description |
|------|-------------|-------------|
| **SuperAdmin** | Full system access | Can create/manage other admins |
| **Admin** | Upload & metadata management | Standard admin operations |
| **Editor** | Basic metadata editing | Limited editing permissions |

### **Web Interface**
```bash
# Open the admin management interface
open scripts/admin-management.html

# Login with your admin credentials
# Access user management features
```

## **🔑 Password Management**

### **Change Your Password**
1. **Login** to admin management interface
2. **Navigate** to "Change Password" section
3. **Enter** current password and new password
4. **Confirm** new password
5. **Submit** the form

### **Password Requirements**
- **Minimum 8 characters**
- **Mix of letters, numbers, symbols** (recommended)
- **Different from current password**
- **Confirmed twice** for accuracy

### **API Endpoint**
```bash
# Change password via API
curl -X POST "http://localhost:5002/api/AdminUserManagement/change-password" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currentPassword": "current_password",
    "newPassword": "new_secure_password",
    "confirmPassword": "new_secure_password"
  }'
```

## **👥 Managing Other Admin Users (SuperAdmin Only)**

### **Create New Admin User**
```bash
# Via API
curl -X POST "http://localhost:5002/api/AdminUserManagement/create" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "new_admin",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "role": "Admin"
  }'
```

### **List All Admin Users**
```bash
# Via API
curl "http://localhost:5002/api/AdminUserManagement/list" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

### **Update Admin Role**
```bash
# Via API
curl -X PUT "http://localhost:5002/api/AdminUserManagement/{user_id}/role" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "Editor"
  }'
```

### **Deactivate Admin User**
```bash
# Via API
curl -X POST "http://localhost:5002/api/AdminUserManagement/{user_id}/deactivate" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

## **🔒 Security Features**

### **Account Lockout**
- **5 failed login attempts** → Account locked for 30 minutes
- **Automatic reset** on successful login
- **Manual unlock** by SuperAdmin (if needed)

### **Password Security**
- **PBKDF2 hashing** with 10,000 iterations
- **Random salt** per password
- **Secure comparison** to prevent timing attacks

### **Session Management**
- **JWT tokens** with configurable expiration
- **HTTP-only cookies** for web interface
- **Automatic logout** on token expiration

## **🚀 Quick Start Guide**

### **1. First Login**
```bash
# Open admin management interface
open scripts/admin-management.html

# Login with default credentials:
# Username: admin
# Password: YendorAdmin123!
```

### **2. Change Default Password**
1. Navigate to "Change Password" section
2. Enter current password: `YendorAdmin123!`
3. Set new secure password
4. Confirm and submit

### **3. Update Profile**
1. Navigate to "Update Profile" section
2. Change email from default `<EMAIL>`
3. Use your actual email address
4. Submit changes

### **4. Create Additional Admins (Optional)**
1. Navigate to "Manage Admin Users" section (SuperAdmin only)
2. Fill in new admin details
3. Choose appropriate role
4. Create admin user

## **🔧 API Endpoints Reference**

### **Authentication**
| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/AdminAuth/login` | POST | Admin login |
| `/api/AdminAuth/logout` | POST | Admin logout |
| `/api/AdminAuth/me` | GET | Current user info |

### **User Management**
| Endpoint | Method | Purpose | Auth Required |
|----------|--------|---------|---------------|
| `/api/AdminUserManagement/change-password` | POST | Change own password | Admin+ |
| `/api/AdminUserManagement/update-profile` | PUT | Update own profile | Admin+ |
| `/api/AdminUserManagement/list` | GET | List all admins | SuperAdmin |
| `/api/AdminUserManagement/create` | POST | Create new admin | SuperAdmin |
| `/api/AdminUserManagement/{id}/role` | PUT | Update admin role | SuperAdmin |
| `/api/AdminUserManagement/{id}/deactivate` | POST | Deactivate admin | SuperAdmin |

## **🛡️ Security Best Practices**

### **For Production**
1. **Change default credentials** immediately
2. **Use strong passwords** (12+ characters)
3. **Enable HTTPS** for all admin operations
4. **Rotate passwords** regularly (quarterly)
5. **Monitor login attempts** and failed authentications
6. **Limit SuperAdmin accounts** to minimum necessary

### **For Development**
1. **Use different credentials** than production
2. **Don't commit credentials** to version control
3. **Use environment variables** for configuration
4. **Test password changes** before deploying

## **🔍 Troubleshooting**

### **Can't Login**
- Check username/email spelling
- Verify password (case-sensitive)
- Check if account is locked (wait 30 minutes)
- Verify application is running on correct port

### **Forgot Password**
- **SuperAdmin can reset** other admin passwords
- **Database access** required for manual reset
- **Recreate admin** via API initialization endpoint

### **Permission Denied**
- Check user role permissions
- Verify JWT token is valid
- Confirm endpoint requires your role level

### **API Errors**
- Check Authorization header format: `Bearer YOUR_TOKEN`
- Verify Content-Type: `application/json`
- Check request body format matches API specification

## **📞 Support Commands**

### **Reset to Default Admin**
```bash
# Reinitialize default admin (development only)
curl -X POST "http://localhost:5002/api/AdminAuth/init"
```

### **Check Current User**
```bash
# Get current user info
curl "http://localhost:5002/api/AdminAuth/me" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### **Test Authentication**
```bash
# Test login
curl -X POST "http://localhost:5002/api/AdminAuth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"YendorAdmin123!"}'
```

---

**Remember: Always change default credentials and use strong passwords for production deployments!** 🔐
