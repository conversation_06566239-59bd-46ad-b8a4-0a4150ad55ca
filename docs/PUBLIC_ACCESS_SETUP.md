# 🌐 Public Access Setup Guide

This guide explains how to configure public read access for cat photos while maintaining secure admin operations.

## **Architecture Overview**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Public Users  │    │   Admin Users    │    │  Backblaze B2   │
│                 │    │                  │    │                 │
│ View Photos     │    │ Upload/Edit      │    │ Public Read     │
│ No Auth Required│────│ JWT Auth Required│────│ Admin Write     │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## **Security Model**

### **✅ Public Operations (No Authentication)**
- **View cat photos** via public URLs
- **Browse galleries** by category
- **Search by cat name**
- **View metadata** (name, age, description)

### **🔐 Admin Operations (JWT Authentication Required)**
- **Upload new photos**
- **Edit metadata**
- **Delete photos**
- **Manage cat profiles**

## **Backblaze B2 Configuration**

### **Step 1: Set Bucket to Public Read**

```bash
# Install B2 CLI
pip install b2

# Authorize with your credentials
b2 authorize-account <your_key_id> <your_application_key>

# Set bucket to public read
b2 update-bucket --bucketType allPublic yendor

# Verify configuration
b2 get-bucket yendor
```

**What this does:**
- ✅ Anyone can **read** files from your bucket
- ✅ Only your API key can **upload/delete/modify** files
- ✅ Public URLs work without authentication
- ✅ Admin operations remain secure

### **Step 2: Test Public Access**

```bash
# Test public URL access (should work without auth)
curl -I "https://f004.backblazeb2.com/file/yendor/studs/louie/image.jpg"

# Should return: HTTP/2 200 OK
```

## **Application Configuration**

### **Public API Endpoints**

| Endpoint | Purpose | Authentication |
|----------|---------|----------------|
| `GET /api/PublicGallery/category/{category}` | Get photos by category | None |
| `GET /api/PublicGallery/cat/{catName}` | Get photos by cat name | None |
| `GET /api/PublicGallery/featured` | Get random featured photos | None |

### **Admin API Endpoints**

| Endpoint | Purpose | Authentication |
|----------|---------|----------------|
| `POST /api/PhotoUpload/upload` | Upload new photos | JWT Required |
| `POST /api/S3Metadata/update` | Update metadata | JWT Required |
| `POST /api/AdminAuth/login` | Admin login | None |

### **Frontend Updates**

The frontend has been updated to use public APIs:

```javascript
// Old (required authentication)
const apiBaseUrl = '/api/CatGallery';

// New (public access)
const apiBaseUrl = '/api/PublicGallery';
```

## **URL Structure**

### **Public Photo URLs**
```
https://f004.backblazeb2.com/file/yendor/{category}/{catName}/{filename}

Examples:
https://f004.backblazeb2.com/file/yendor/studs/louie/image1.jpg
https://f004.backblazeb2.com/file/yendor/queens/bella/portrait.jpg
https://f004.backblazeb2.com/file/yendor/kittens/mittens/kitten.jpg
```

### **API Response Format**
```json
{
  "category": "studs",
  "count": 15,
  "sortBy": "name",
  "images": [
    {
      "id": "uuid",
      "catName": "Louie",
      "imageUrl": "https://f004.backblazeb2.com/file/yendor/studs/louie/image.jpg",
      "category": "studs",
      "dateTaken": "2024-01-15T10:30:00Z",
      "orderNumber": 1,
      "age": 3.5,
      "description": "Beautiful Maine Coon stud",
      "breed": "Maine Coon",
      "gender": "M"
    }
  ],
  "source": "database",
  "retrievedAt": "2024-01-15T12:00:00Z"
}
```

## **Security Benefits**

### **🔒 Credential Protection**
- **S3 credentials** stored in environment variables
- **No hardcoded secrets** in source code
- **Admin JWT tokens** for secure operations
- **Public read** doesn't expose credentials

### **🔒 Access Control**
- **Visitors** can only view photos
- **Admins** can upload and manage
- **Role-based permissions** (SuperAdmin, Admin, Editor)
- **Account lockout** after failed login attempts

### **🔒 Performance Benefits**
- **Direct CDN access** for photo viewing
- **No API overhead** for public photos
- **Cached responses** for better performance
- **Reduced server load** for public operations

## **Testing the Setup**

### **Test Public Access**
```bash
# Test category endpoint
curl "http://localhost:5002/api/PublicGallery/category/studs"

# Test cat-specific endpoint
curl "http://localhost:5002/api/PublicGallery/cat/louie"

# Test featured photos
curl "http://localhost:5002/api/PublicGallery/featured?count=3"
```

### **Test Admin Access**
```bash
# Login as admin
curl -X POST "http://localhost:5002/api/AdminAuth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"your_password"}'

# Use token for protected operations
curl -X POST "http://localhost:5002/api/S3Metadata/update" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"s3Key":"studs/louie/image.jpg","catName":"Louie","age":"3.5"}'
```

## **Deployment Checklist**

### **Before Going Live**
- [ ] Set Backblaze bucket to public read
- [ ] Configure environment variables
- [ ] Test public photo URLs
- [ ] Verify admin authentication works
- [ ] Update CORS for your domain
- [ ] Enable HTTPS in production

### **Production Environment Variables**
```bash
# Required for production
YENDOR_S3_ACCESS_KEY=your_production_key
YENDOR_S3_SECRET_KEY=your_production_secret
YENDOR_DEFAULT_ADMIN_PASSWORD=strong_production_password
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=https://0.0.0.0:443
```

### **HTTPS Configuration**
```bash
# For production with Let's Encrypt
sudo certbot certonly --standalone -d yendorcats.yourdomain.com

# Update appsettings.Production.json with certificate paths
```

## **Monitoring & Maintenance**

### **Log Monitoring**
- Monitor admin login attempts
- Track failed authentication
- Watch for unusual access patterns
- Monitor S3 usage and costs

### **Security Maintenance**
- Rotate S3 credentials quarterly
- Update admin passwords regularly
- Review admin user accounts
- Monitor public access logs

## **Troubleshooting**

### **Common Issues**

**Public photos not loading:**
- Check bucket is set to `allPublic`
- Verify public URL format
- Test direct URL access

**Admin operations failing:**
- Check environment variables are set
- Verify JWT token is valid
- Confirm admin user exists

**CORS errors:**
- Update CORS policy for your domain
- Check browser developer tools
- Verify API endpoints are correct

### **Support Commands**
```bash
# Check bucket status
b2 get-bucket yendor

# Test API health
curl "http://localhost:5002/api/PublicGallery/featured?count=1"

# Check admin user exists
curl -X POST "http://localhost:5002/api/AdminAuth/init"
```

---

**Your cat photos are now publicly accessible while maintaining secure admin control!** 🎉
