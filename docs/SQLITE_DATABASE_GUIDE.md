# SQLite Database Guide for YendorCats

This document explains how the SQLite database setup works for the YendorCats application when running in Docker containers.

## Overview

YendorCats has been configured to use SQLite for administrative user credential persistence. This provides several benefits:

1. Simplified deployment with no need for a separate database server
2. Faster container startup times through pre-built database
3. Persistent storage across container restarts
4. Easy backup and restore processes

## Database Locations

- **Development**: `Data/yendorcats.db` (relative to project root)
- **Production**: `/app/data/yendorcats.db` (inside container)

## Database Workflows

### Development Workflow

In development mode, Entity Framework Core will:

1. Check for an existing database file
2. Apply any pending migrations if the database exists
3. Create a new database if it doesn't exist
4. Initialize a default admin user if needed

To create your initial migration:

```bash
cd backend/YendorCats.API
chmod +x Data/create-migration.sh
./Data/create-migration.sh
```

To manually run migrations:

```bash
dotnet ef database update
```

### Production Workflow

In production mode (Docker), the system will:

1. Build a pre-populated SQLite database during Docker image creation
2. Copy this pre-built database to a mounted volume on first container start
3. Use the database from the mounted volume for all operations
4. Preserve data between container restarts and updates

## Docker Volume Configuration

The application uses a Docker volume for database persistence:

```yaml
volumes:
  - api-data:/app/data # SQLite database persistence
```

This ensures data survives container restarts, rebuilds, and updates.

## Admin User Management

The system will create a secure default admin user on first startup:

- **Username**: `admin` (can be overridden with `YENDOR_DEFAULT_ADMIN_USERNAME`)
- **Email**: `<EMAIL>` (can be overridden with `YENDOR_DEFAULT_ADMIN_EMAIL`)
- **Password**: Auto-generated secure password (can be overridden with `YENDOR_DEFAULT_ADMIN_PASSWORD`)

When auto-generating a password, the system will:
1. Output the password to the console/logs during first startup
2. Generate a strong 16-character password with mixed cases, numbers, and symbols
3. Require you to change this password after first login

## Backing Up the Database

To backup the SQLite database from a running container:

```bash
# Create a backup with timestamp
docker exec yendorcats-api /bin/bash -c "mkdir -p /app/backups && cp /app/data/yendorcats.db /app/backups/yendorcats_\$(date +%Y%m%d_%H%M%S).db"

# Copy the backup to the host
docker cp yendorcats-api:/app/backups ./database-backups
```

## Restoring the Database

To restore a database backup:

```bash
# Copy backup to container
docker cp ./database-backups/yendorcats_20250710_120000.db yendorcats-api:/app/data/yendorcats.db

# Restart the container to use the restored database
docker restart yendorcats-api
```

## Technical Details

### Pre-building the Database

The Docker build process:

1. Uses a multi-stage build approach
2. Creates the migration files in an intermediate build stage
3. Builds a production-ready SQLite database with schema applied
4. Copies the pre-built database to the final image
5. Sets up an entrypoint script to handle first-run initialization

This approach provides several benefits:
- Faster container startup (no migration execution needed at runtime)
- Smaller production image (no Entity Framework SDK tools required)
- Reliable schema deployment
- Simplified volume persistence

### Connection String Configuration

The SQLite connection string can be customized via:

- `appsettings.json` - `ConnectionStrings:SqliteConnection`
- Environment variables - `ConnectionStrings__SqliteConnection`

Default values are:
- Development: `Data Source=Data/yendorcats.db`
- Production: `Data Source=/app/data/yendorcats.db`
