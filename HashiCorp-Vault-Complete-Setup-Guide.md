---

# <PERSON><PERSON><PERSON><PERSON><PERSON> Vault Complete Setup Guide for Multi-Website VPS

## Overview

This guide provides a complete step-by-step process to set up Hashi<PERSON>orp Vault on your VPS for managing secrets across all your websites. Vault will serve as a centralized, secure secrets management system for database credentials, API keys, JWT secrets, and other sensitive data.

## Tags
#hashicorp #vault #secrets-management #vps #multi-website #security #automation #docker #ssl #backup

---

## Prerequisites Check

### System Requirements
#prerequisites #system #requirements

```bash
# Check your VPS specs
free -h          # RAM: Minimum 1GB, Recommended 2GB+
df -h            # Storage: Minimum 10GB free
nproc            # CPU: 1+ cores
uname -a         # OS: Linux (Ubuntu/Debian/CentOS)
```

### Required Software Installation
#software #installation #docker

```bash
# Install Docker and Docker Compose (if not already installed)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installations
docker --version
docker-compose --version

# Log out and back in for group changes to take effect
```

---

## Step 1: Create Vault Directory Structure

### Directory Setup
#directory #structure #organization

```bash
# Create main Vault directory
sudo mkdir -p /opt/vault/{config,data,logs,scripts,backups,ssl}
cd /opt/vault

# Set proper ownership
sudo chown -R $USER:$USER /opt/vault
chmod 755 /opt/vault
chmod 700 /opt/vault/{data,ssl,backups}
chmod 755 /opt/vault/{config,logs,scripts}

# Create subdirectories for organization
mkdir -p config/{policies,auth}
mkdir -p scripts/{backup,maintenance,deployment}
mkdir -p ssl/{certs,keys}
mkdir -p tokens
chmod 700 tokens
```

---

## Step 2: Generate SSL Certificates

### Option A: Self-Signed Certificate (Development/Testing)
#ssl #self-signed #development

```bash
# Generate self-signed certificate
cd /opt/vault/ssl

# Create certificate (replace vault.yourdomain.com with your domain)
openssl req -x509 -newkey rsa:4096 \
  -keyout keys/vault.key \
  -out certs/vault.crt \
  -days 365 -nodes \
  -subj "/C=US/ST=State/L=City/O=YourCompany/CN=vault.yourdomain.com"

# Set proper permissions
chmod 600 keys/vault.key
chmod 644 certs/vault.crt
```

### Option B: Let's Encrypt Certificate (Production)
#ssl #letsencrypt #production

```bash
# Install Certbot
sudo apt update
sudo apt install certbot

# Generate certificate (replace with your actual domain)
sudo certbot certonly --standalone \
  -d vault.yourdomain.com \
  --email <EMAIL> \
  --agree-tos --non-interactive

# Copy certificates to Vault directory
sudo cp /etc/letsencrypt/live/vault.yourdomain.com/fullchain.pem /opt/vault/ssl/certs/vault.crt
sudo cp /etc/letsencrypt/live/vault.yourdomain.com/privkey.pem /opt/vault/ssl/keys/vault.key
sudo chown $USER:$USER /opt/vault/ssl/certs/vault.crt /opt/vault/ssl/keys/vault.key
```

---

## Step 3: Create Vault Configuration

### Main Configuration File
#configuration #vault #setup

```bash
# Create main Vault configuration file
cat > /opt/vault/config/vault.hcl << 'EOF'
# Vault Configuration for Multi-Website VPS
ui = true

# Storage backend - file storage
storage "file" {
  path = "/vault/data"
}

# HTTPS listener
listener "tcp" {
  address = "0.0.0.0:8200"
  tls_cert_file = "/vault/ssl/certs/vault.crt"
  tls_key_file = "/vault/ssl/keys/vault.key"
  tls_min_version = "tls12"
}

# HTTP listener (for internal container communication)
listener "tcp" {
  address = "0.0.0.0:8201"
  tls_disable = true
}

# API and cluster addresses (replace with your domain)
api_addr = "https://vault.yourdomain.com:8200"
cluster_addr = "https://vault.yourdomain.com:8201"

# Disable memory locking for containers
disable_mlock = true

# Enable audit logging
audit "file" {
  file_path = "/vault/logs/audit.log"
}

# Performance and security settings
default_lease_ttl = "168h"    # 7 days
max_lease_ttl = "720h"        # 30 days
EOF
```

---

## Step 4: Create Docker Compose Configuration

### Docker Compose Setup
#docker #compose #deployment

```bash
# Create Docker Compose file
cat > /opt/vault/docker-compose.yml << 'EOF'
version: '3.8'

services:
  vault:
    image: vault:1.15.2
    container_name: vault-server
    restart: unless-stopped
    ports:
      - "8200:8200"  # HTTPS API
      - "8201:8201"  # HTTP internal
    environment:
      VAULT_ADDR: 'http://0.0.0.0:8201'
      VAULT_API_ADDR: 'https://vault.yourdomain.com:8200'
      VAULT_CLUSTER_ADDR: 'https://vault.yourdomain.com:8201'
      VAULT_LOG_LEVEL: 'INFO'
    volumes:
      - ./data:/vault/data:rw
      - ./config:/vault/config:ro
      - ./logs:/vault/logs:rw
      - ./ssl:/vault/ssl:ro
    cap_add:
      - IPC_LOCK
    command: vault server -config=/vault/config/vault.hcl
    networks:
      - vault-network
    healthcheck:
      test: ["CMD", "vault", "status", "-address=http://localhost:8201"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  vault-network:
    driver: bridge
    name: vault-network

volumes:
  vault-data:
    driver: local
  vault-logs:
    driver: local
EOF
```

---

## Step 5: Create Management Scripts

### Vault Startup Script
#scripts #startup #management

```bash
cat > /opt/vault/scripts/start-vault.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 Starting HashiCorp Vault..."

cd /opt/vault

# Start Vault container
docker-compose up -d

# Wait for Vault to start
echo "⏳ Waiting for Vault to start..."
sleep 10

# Check if Vault is running
if docker-compose ps | grep -q "vault-server.*Up"; then
    echo "✅ Vault is running"
    echo "📊 Vault UI: https://vault.yourdomain.com:8200"
    echo "🔧 Internal API: http://localhost:8201"
else
    echo "❌ Failed to start Vault"
    docker-compose logs vault
    exit 1
fi
EOF

chmod +x /opt/vault/scripts/start-vault.sh
```

### Vault Stop Script
#scripts #stop #management

```bash
cat > /opt/vault/scripts/stop-vault.sh << 'EOF'
#!/bin/bash
set -e

echo "🛑 Stopping HashiCorp Vault..."

cd /opt/vault

# Stop Vault container
docker-compose down

echo "✅ Vault stopped"
EOF

chmod +x /opt/vault/scripts/stop-vault.sh
```

### Vault Status Script
#scripts #status #monitoring

```bash
cat > /opt/vault/scripts/vault-status.sh << 'EOF'
#!/bin/bash

echo "📊 Vault Status Check"
echo "===================="

cd /opt/vault

# Check if container is running
if docker-compose ps | grep -q "vault-server.*Up"; then
    echo "✅ Container: Running"
    
    # Check Vault status
    export VAULT_ADDR='http://localhost:8201'
    if docker-compose exec vault vault status 2>/dev/null; then
        echo "✅ Vault: Healthy"
    else
        echo "⚠️  Vault: Sealed or Unhealthy"
    fi
else
    echo "❌ Container: Not Running"
fi

# Show resource usage
echo ""
echo "📈 Resource Usage:"
docker stats vault-server --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
EOF

chmod +x /opt/vault/scripts/vault-status.sh
```

---

## Step 6: Initialize Vault

### One-Time Initialization
#initialization #vault #setup

```bash
# Start Vault
cd /opt/vault
./scripts/start-vault.sh

# Wait for Vault to be ready
sleep 15

# Initialize Vault (ONE TIME ONLY)
export VAULT_ADDR='http://localhost:8201'
docker-compose exec vault vault operator init -key-shares=5 -key-threshold=3 -format=json > vault-init.json

# Extract and save keys securely
cat vault-init.json | jq -r '.unseal_keys_b64[]' > unseal-keys.txt
cat vault-init.json | jq -r '.root_token' > root-token.txt

# Secure the files
chmod 600 vault-init.json unseal-keys.txt root-token.txt

echo "🔐 Vault initialized!"
echo "📁 Keys saved to: unseal-keys.txt"
echo "🎫 Root token saved to: root-token.txt"
echo ""
echo "⚠️  IMPORTANT: Store these files securely and separately!"
```

---

## Step 7: Unseal Vault

### Vault Unsealing Process
#unseal #vault #activation

```bash
# Unseal Vault using the first 3 keys
export VAULT_ADDR='http://localhost:8201'

echo "🔓 Unsealing Vault..."
head -3 unseal-keys.txt | while read key; do
    docker-compose exec vault vault operator unseal "$key"
done

echo "✅ Vault is now unsealed and ready!"
```

---

## Step 8: Configure Authentication and Policies

### Initial Configuration
#authentication #policies #security

```bash
# Set root token for initial configuration
export VAULT_ADDR='http://localhost:8201'
export VAULT_TOKEN=$(cat root-token.txt)

# Enable KV secrets engine
docker-compose exec vault vault secrets enable -path=secret kv-v2

# Create general website policy
cat > /opt/vault/config/policies/website-general.hcl << 'EOF'
# General policy for website secrets
path "secret/data/websites/{{identity.entity.name}}/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/websites/{{identity.entity.name}}/*" {
  capabilities = ["list"]
}

# Allow reading shared secrets
path "secret/data/shared/*" {
  capabilities = ["read", "list"]
}
EOF

# Apply the policy
docker-compose exec vault vault policy write website-general /vault/config/policies/website-general.hcl
```

---
