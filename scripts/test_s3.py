import boto3

# Configuration
BUCKET_NAME = "yendor"
ENDPOINT_URL = "https://s3.us-west-004.backblazeb2.com"

def create_s3_client():
    """Create S3 client for Backblaze B2"""
    return boto3.client(
        's3',
        endpoint_url=ENDPOINT_URL,
        region_name='us-west-004'
    )

def list_objects():
    """List objects in the S3 bucket"""
    s3_client = create_s3_client()
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=BUCKET_NAME)
        for page in pages:
            if 'Contents' not in page:
                continue
            for obj in page['Contents']:
                print(obj['Key'])
    except Exception as e:
        print(f"Error listing objects: {e}")

if __name__ == "__main__":
    list_objects()
