#!/usr/bin/env python3
"""
Photo Index Generator for YendorCats
Generates a static photo index from S3 bucket without requiring credentials for public access
"""

import boto3
import json
import sys
import os
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Load .env file
load_dotenv(dotenv_path=Path(__file__).resolve().parent.parent / '.env')

# Configuration
BUCKET_NAME = "yendor"
ENDPOINT_URL = "https://s3.us-west-004.backblazeb2.com"
CATEGORIES = ["studs", "queens", "kittens", "gallery"]
IMAGE_EXTENSIONS = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".heic", ".tif"]

def create_s3_client():
    """Create S3 client for Backblaze B2"""
    access_key = os.environ.get('YENDOR_S3_ACCESS_KEY')
    secret_key = os.environ.get('YENDOR_S3_SECRET_KEY')
    
    if not access_key or not secret_key:
        print("❌ Error: S3 credentials not found in environment variables")
        print("Please set YENDOR_S3_ACCESS_KEY and YENDOR_S3_SECRET_KEY")
        sys.exit(1)
    
    return boto3.client(
        's3',
        endpoint_url=ENDPOINT_URL,
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        region_name='us-west-004'
    )

def is_image_file(filename):
    """Check if file is an image based on extension"""
    return any(filename.lower().endswith(ext) for ext in IMAGE_EXTENSIONS)

def extract_cat_name_from_path(s3_key):
    """Extract cat name from S3 key path"""
    parts = s3_key.split('/')
    if len(parts) >= 2:
        cat_name = parts[1]
        # Capitalize first letter
        return cat_name.capitalize()
    return "Unknown"

def get_object_metadata(s3_client, s3_key):
    """Get metadata for an S3 object"""
    try:
        response = s3_client.head_object(Bucket=BUCKET_NAME, Key=s3_key)
        return response.get('Metadata', {})
    except Exception as e:
        print(f"⚠️  Warning: Could not get metadata for {s3_key}: {e}")
        return {}

def generate_photo_index():
    """Generate the photo index from S3 bucket"""
    print("🏗️  Generating photo index from S3 bucket...")
    
    s3_client = create_s3_client()
    
    index = {
        "lastUpdated": datetime.utcnow().isoformat() + "Z",
        "categories": {},
        "metadata": {}
    }
    
    total_photos = 0
    
    for category in CATEGORIES:
        print(f"📂 Processing category: {category}")
        
        try:
            # List all objects in the category
            paginator = s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=BUCKET_NAME, Prefix=f"{category}/")
            
            category_data = {}
            category_count = 0
            
            for page in pages:
                if 'Contents' not in page:
                    continue
                    
                for obj in page['Contents']:
                    s3_key = obj['Key']
                    
                    # Skip .bzEmpty files and non-images
                    if '.bzEmpty' in s3_key or not is_image_file(s3_key):
                        continue
                    
                    # Parse path: category/catName/filename
                    path_parts = s3_key.split('/')
                    if len(path_parts) < 3:
                        continue
                    
                    cat_name = path_parts[1]
                    filename = path_parts[2]
                    
                    # Initialize cat entry if not exists
                    if cat_name not in category_data:
                        category_data[cat_name] = []
                    
                    # Add filename to cat's photo list
                    if filename not in category_data[cat_name]:
                        category_data[cat_name].append(filename)
                        category_count += 1
                        total_photos += 1
                    
                    # Get metadata for this photo
                    metadata = get_object_metadata(s3_client, s3_key)
                    if metadata:
                        index["metadata"][s3_key] = {
                            "catName": metadata.get("cat-name", extract_cat_name_from_path(s3_key)),
                            "age": metadata.get("age", "0"),
                            "description": metadata.get("description", ""),
                            "breed": metadata.get("breed", "Maine Coon"),
                            "gender": metadata.get("gender", ""),
                            "color": metadata.get("color", ""),
                            "dateTaken": metadata.get("date-taken", ""),
                            "personality": metadata.get("personality", ""),
                            "bloodline": metadata.get("bloodline", "")
                        }
                    else:
                        # Create basic metadata from path
                        index["metadata"][s3_key] = {
                            "catName": extract_cat_name_from_path(s3_key),
                            "age": "0",
                            "description": "",
                            "breed": "Maine Coon",
                            "gender": "",
                            "color": "",
                            "dateTaken": "",
                            "personality": "",
                            "bloodline": ""
                        }
            
            if category_data:
                index["categories"][category] = category_data
                print(f"✅ Found {category_count} photos in {len(category_data)} cats for category {category}")
            else:
                print(f"⚠️  No photos found for category {category}")
                
        except Exception as e:
            print(f"❌ Error processing category {category}: {e}")
            continue
    
    print(f"\n📊 Index generation complete:")
    print(f"   Total photos: {total_photos}")
    print(f"   Categories: {len(index['categories'])}")
    print(f"   Cats with metadata: {len(index['metadata'])}")
    
    return index

def save_index_to_file(index, output_path):
    """Save the index to a JSON file"""
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(index, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Index saved to: {output_path}")
        return True
    except Exception as e:
        print(f"❌ Error saving index: {e}")
        return False

def main():
    """Main function"""
    print("🏷️  YendorCats Photo Index Generator")
    print("=" * 50)
    
    # Determine output path
    if len(sys.argv) > 1:
        output_path = sys.argv[1]
    else:
        # Default to backend Data directory
        script_dir = Path(__file__).parent
        backend_dir = script_dir.parent / "backend" / "YendorCats.API" / "Data"
        output_path = backend_dir / "PhotoIndex.json"
    
    print(f"📁 Output path: {output_path}")
    
    # Generate the index
    try:
        index = generate_photo_index()
        
        if save_index_to_file(index, output_path):
            print("\n🎉 Photo index generation completed successfully!")
            print("\nNext steps:")
            print("1. The index file is now available for public API access")
            print("2. No S3 credentials needed for public photo viewing")
            print("3. Restart your application to use the new index")
            print("4. Test with: curl 'http://localhost:5002/api/PublicGallery/category/studs'")
        else:
            print("\n❌ Failed to save index file")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
