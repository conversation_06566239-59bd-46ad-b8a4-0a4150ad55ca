services:
  migrations:
    build:
      context: backend/CabUCA.API
      dockerfile: Dockerfile.migrations
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/olmate/cabuca-migrations-testing:latest
    env_file:
      - .env
    volumes:
      - sqlite-data:/Data

  api:
    build:
      context: backend/CabUCA.API
      dockerfile: Dockerfile
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/olmate/cabuca-api-testing:latest
    env_file:
      - .env
    volumes:
      - sqlite-data:/Data

  admin:
    build:
      context: admin-frontend/
      dockerfile: Dockerfile
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/olmate/cabuca-admin-testing:latest
    env_file:
      - .env

volumes:
  sqlite-data:
