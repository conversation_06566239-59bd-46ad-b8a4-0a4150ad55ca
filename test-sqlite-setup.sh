#!/bin/bash

echo "Testing SQLite setup locally..."

# Navigate to the API directory
cd backend/YendorCats.API

# Check if migrations exist
echo "Checking for migrations..."
if [ -d "Migrations" ] && [ "$(ls -A Migrations)" ]; then
    echo "✓ Migrations found"
    ls -la Migrations/
else
    echo "✗ No migrations found"
    echo "Creating initial migration..."
    dotnet ef migrations add InitialCreate
fi

# Test database creation
echo "Testing database creation..."
export ConnectionStrings__SqliteConnection="Data Source=test-yendorcats.db"

# Apply migrations
echo "Applying migrations..."
dotnet ef database update

# Check if database was created
if [ -f "test-yendorcats.db" ]; then
    echo "✓ SQLite database created successfully"
    echo "Database size: $(du -h test-yendorcats.db)"
    
    # Test running the application
    echo "Testing application startup..."
    timeout 10s dotnet run --urls="http://localhost:5005" &
    APP_PID=$!
    
    sleep 5
    
    # Test if the application responds
    if curl -s http://localhost:5005 > /dev/null; then
        echo "✓ Application started successfully"
    else
        echo "✗ Application failed to respond"
    fi
    
    # Clean up
    kill $APP_PID 2>/dev/null || true
    rm -f test-yendorcats.db
else
    echo "✗ Failed to create SQLite database"
fi

echo "Test complete!"
