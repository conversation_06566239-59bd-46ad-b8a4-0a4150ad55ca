version: '3.8'

services:
  # Backend API service
  api:
    build:
      context: .
      dockerfile: backend/YendorCats.API/Dockerfile
    image: yendorcats/api:latest
    container_name: yendorcats-api
    restart: unless-stopped
    ports:
      - "5003:80"
      - "5004:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80;https://+:443
      - AWS__Region=us-west-004
      - AWS__UseCredentialsFromSecrets=false
      - AWS__S3__BucketName=yendor
      - AWS__S3__UseDirectS3Urls=true
      - AWS__S3__ServiceUrl=https://s3.us-west-004.backblazeb2.com
      - AWS__S3__PublicUrl=https://f004.backblazeb2.com/file/yendor/{key}
      - AWS__S3__UseCdn=false
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - ConnectionStrings__DefaultConnection=Server=db;Database=YendorCats;User=${MYSQL_USER};Password=${MYSQL_PASSWORD};Port=3306;
      - JwtSettings__Secret=${YENDOR_JWT_SECRET}
    volumes:
      - api-data:/app/data # SQLite database persistence
      - api-logs:/app/Logs
    depends_on:
      - db
    networks:
      - yendorcats-network

  # MariaDB Database
  db:
    image: mariadb:10.11
    container_name: yendorcats-db
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=YendorCats
    volumes:
      - mariadb-data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - yendorcats-network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # File Upload Service
  uploader:
    build:
      context: ./tools/file-uploader
      dockerfile: Dockerfile
    image: yendorcats/uploader:latest
    container_name: yendorcats-uploader
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - AWS_S3_BUCKET_NAME=yendor
      - AWS_S3_REGION=us-west-004
      - AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      - API_BASE_URL=http://api
    depends_on:
      - api
    networks:
      - yendorcats-network

networks:
  yendorcats-network:
    driver: bridge

volumes:
  api-data:
  api-logs:
  mariadb-data:
