/**
 * Implementation Validation Script for S3 Metadata Editing System
 * Validates file structure, code presence, and component integration
 */

const fs = require('fs');
const path = require('path');

// Test results tracking
const results = {
    passed: 0,
    failed: 0,
    details: []
};

function log(message, type = 'INFO') {
    console.log(`[${type}] ${message}`);
}

function assert(condition, message) {
    if (condition) {
        results.passed++;
        results.details.push({ status: 'PASS', message });
        log(`✅ PASS: ${message}`);
    } else {
        results.failed++;
        results.details.push({ status: 'FAIL', message });
        log(`❌ FAIL: ${message}`);
    }
}

// Validation 1: Backend Service Files
function validateBackendServices() {
    log('Validating Backend Services...', 'TEST');

    // Check S3StorageService interface
    const interfacePath = 'backend/YendorCats.API/Services/IS3StorageService.cs';
    const implementationPath = 'backend/YendorCats.API/Services/S3StorageService.cs';
    
    assert(fs.existsSync(interfacePath), 'IS3StorageService interface file exists');
    assert(fs.existsSync(implementationPath), 'S3StorageService implementation file exists');
    
    if (fs.existsSync(interfacePath)) {
        const interfaceContent = fs.readFileSync(interfacePath, 'utf8');
        assert(interfaceContent.includes('UpdateObjectMetadataAsync'), 'UpdateObjectMetadataAsync method in interface');
        assert(interfaceContent.includes('GetS3ConfigurationAsync'), 'GetS3ConfigurationAsync method in interface');
        assert(interfaceContent.includes('SearchByMetadataAsync'), 'SearchByMetadataAsync method in interface');
        assert(interfaceContent.includes('S3ObjectWithMetadata'), 'S3ObjectWithMetadata class defined');
    }
    
    if (fs.existsSync(implementationPath)) {
        const implContent = fs.readFileSync(implementationPath, 'utf8');
        assert(implContent.includes('UpdateObjectMetadataAsync'), 'UpdateObjectMetadataAsync method implemented');
        assert(implContent.includes('CopyObjectRequest'), 'S3 metadata update using copy operation');
        assert(implContent.includes('GetBucketLocationRequest'), 'S3 configuration retrieval implemented');
        assert(implContent.includes('SearchByMetadataAsync'), 'Metadata search functionality implemented');
    }
}

// Validation 2: Controller Endpoints
function validateControllers() {
    log('Validating Controller Endpoints...', 'TEST');

    const s3MetadataPath = 'backend/YendorCats.API/Controllers/S3MetadataController.cs';
    const adminPath = 'backend/YendorCats.API/Controllers/AdminController.cs';
    
    assert(fs.existsSync(s3MetadataPath), 'S3MetadataController exists');
    assert(fs.existsSync(adminPath), 'AdminController exists');
    
    if (fs.existsSync(s3MetadataPath)) {
        const s3Content = fs.readFileSync(s3MetadataPath, 'utf8');
        
        // Check for comprehensive metadata fields
        const metadataFields = [
            'CatName', 'CatId', 'RegisteredName', 'FatherId', 'MotherId',
            'BreedingStatus', 'AvailabilityStatus', 'PhotoType', 'Tags',
            'ChampionTitles', 'GenerationLevel'
        ];
        
        metadataFields.forEach(field => {
            assert(s3Content.includes(field), `Metadata field ${field} present in controller`);
        });
        
        assert(s3Content.includes('bulk-update'), 'Bulk update endpoint present');
        assert(s3Content.includes('AdminAuthorize'), 'Admin authorization required');
    }
    
    if (fs.existsSync(adminPath)) {
        const adminContent = fs.readFileSync(adminPath, 'utf8');
        assert(adminContent.includes('s3/config'), 'S3 config endpoint present');
        assert(adminContent.includes('cats/list-all'), 'List all cats endpoint present');
        assert(adminContent.includes('photos/unlinked'), 'Unlinked photos endpoint present');
        assert(adminContent.includes('cats/search'), 'Cat search endpoint present');
        assert(adminContent.includes('CatSearchRequest'), 'Search request model present');
    }
}

// Validation 3: Frontend Interface
function validateFrontendInterface() {
    log('Validating Frontend Interface...', 'TEST');

    const metadataEditorPath = 'frontend/admin-metadata-editor.html';
    const adminJsPath = 'frontend/js/admin.js';
    
    assert(fs.existsSync(metadataEditorPath), 'Admin metadata editor HTML exists');
    assert(fs.existsSync(adminJsPath), 'Admin JavaScript file exists');
    
    if (fs.existsSync(metadataEditorPath)) {
        const editorContent = fs.readFileSync(metadataEditorPath, 'utf8');
        
        // Check for tab structure
        const requiredTabs = ['overview', 'cats', 'photos', 'bulk', 'pedigree'];
        requiredTabs.forEach(tab => {
            assert(editorContent.includes(`${tab}-panel`), `${tab} tab panel present`);
        });
        
        // Check for key functionality
        assert(editorContent.includes('bulk-cat-name'), 'Bulk cat name field present');
        assert(editorContent.includes('bulk-bloodline'), 'Bulk bloodline field present');
        assert(editorContent.includes('applyBulkMetadata'), 'Apply bulk metadata function present');
        assert(editorContent.includes('togglePhotoSelection'), 'Photo selection functionality present');
        assert(editorContent.includes('loadUnlinkedPhotos'), 'Load unlinked photos function present');
        assert(editorContent.includes('litter-birth-date'), 'Litter management present');
        assert(editorContent.includes('father-select'), 'Pedigree builder present');
        
        // Check for Maine Coon specific features
        assert(editorContent.includes('Maine Coon'), 'Maine Coon breed specific');
        assert(editorContent.includes('bloodline'), 'Bloodline management');
        assert(editorContent.includes('champion'), 'Champion title tracking');
        assert(editorContent.includes('pedigree'), 'Pedigree functionality');
    }
    
    if (fs.existsSync(adminJsPath)) {
        const adminJsContent = fs.readFileSync(adminJsPath, 'utf8');
        assert(adminJsContent.includes('admin-metadata-editor.html'), 'Link to metadata editor');
        assert(adminJsContent.includes('loadQuickStats'), 'Quick stats functionality');
        assert(adminJsContent.includes('totalCats'), 'Cat statistics tracking');
        assert(adminJsContent.includes('breedingStatus'), 'Breeding status tracking');
    }
}

// Validation 4: Data Model Completeness
function validateDataModel() {
    log('Validating Data Model Completeness...', 'TEST');

    const controllerPath = 'backend/YendorCats.API/Controllers/S3MetadataController.cs';
    
    if (fs.existsSync(controllerPath)) {
        const content = fs.readFileSync(controllerPath, 'utf8');
        
        // Maine Coon breeding specific fields
        const breedingFields = [
            'bloodline', 'champion-titles', 'generation-level',
            'father-id', 'mother-id', 'breeding-status',
            'availability-status', 'registration-number'
        ];
        
        breedingFields.forEach(field => {
            assert(content.includes(field), `Breeding field ${field} implemented`);
        });
        
        // Photo organization fields
        const photoFields = ['photo-type', 'age-at-photo', 'tags'];
        photoFields.forEach(field => {
            assert(content.includes(field), `Photo field ${field} implemented`);
        });
        
        // System fields
        assert(content.includes('updated-by'), 'System tracking: updated-by');
        assert(content.includes('updated-at'), 'System tracking: updated-at');
    }
}

// Validation 5: Integration Points
function validateIntegration() {
    log('Validating Integration Points...', 'TEST');

    // Check for proper API base URL configuration
    const metadataEditorPath = 'frontend/admin-metadata-editor.html';
    if (fs.existsSync(metadataEditorPath)) {
        const content = fs.readFileSync(metadataEditorPath, 'utf8');
        assert(content.includes('http://localhost:5002/api'), 'Correct API base URL');
        assert(content.includes('Authorization'), 'JWT token authentication');
        assert(content.includes('admin_token'), 'Token storage handling');
    }

    // Check for S3 service integration
    const s3ServicePath = 'backend/YendorCats.API/Services/S3StorageService.cs';
    if (fs.existsSync(s3ServicePath)) {
        const content = fs.readFileSync(s3ServicePath, 'utf8');
        assert(content.includes('AWS'), 'AWS S3 integration');
        assert(content.includes('metadata'), 'Metadata handling');
        assert(content.includes('CopyObjectRequest'), 'Metadata update mechanism');
    }
}

// Validation 6: Security and Authorization
function validateSecurity() {
    log('Validating Security Implementation...', 'TEST');

    const s3MetadataPath = 'backend/YendorCats.API/Controllers/S3MetadataController.cs';
    if (fs.existsSync(s3MetadataPath)) {
        const content = fs.readFileSync(s3MetadataPath, 'utf8');
        assert(content.includes('AdminAuthorize'), 'Admin authorization attribute present');
        assert(content.includes('SuperAdmin'), 'SuperAdmin role checking');
        assert(content.includes('Admin'), 'Admin role checking');
        assert(content.includes('Editor'), 'Editor role checking');
    }

    const adminPath = 'backend/YendorCats.API/Controllers/AdminController.cs';
    if (fs.existsSync(adminPath)) {
        const content = fs.readFileSync(adminPath, 'utf8');
        assert(content.includes('Authorize'), 'Authorization required');
        assert(content.includes('Admin'), 'Admin role required');
    }
}

// Main validation runner
function runValidation() {
    console.log('='.repeat(60));
    console.log('S3 METADATA EDITING SYSTEM - IMPLEMENTATION VALIDATION');
    console.log('='.repeat(60));

    const startTime = Date.now();

    try {
        validateBackendServices();
        validateControllers();
        validateFrontendInterface();
        validateDataModel();
        validateIntegration();
        validateSecurity();
    } catch (error) {
        log(`Validation error: ${error.message}`, 'ERROR');
        results.failed++;
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('VALIDATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Checks: ${results.passed + results.failed}`);
    console.log(`Passed: ${results.passed}`);
    console.log(`Failed: ${results.failed}`);
    console.log(`Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
    console.log(`Duration: ${duration}s`);

    // Save report
    const report = {
        summary: {
            totalChecks: results.passed + results.failed,
            passed: results.passed,
            failed: results.failed,
            successRate: ((results.passed / (results.passed + results.failed)) * 100).toFixed(1),
            duration: duration,
            timestamp: new Date().toISOString()
        },
        details: results.details
    };

    fs.writeFileSync('validation-report.json', JSON.stringify(report, null, 2));
    console.log('\nDetailed validation report saved to validation-report.json');

    if (results.failed === 0) {
        console.log('\n🎉 ALL VALIDATIONS PASSED! Implementation is complete and ready for testing.');
    } else {
        console.log(`\n⚠️  ${results.failed} validation(s) failed. Review the details above.`);
    }

    return results.failed === 0;
}

// Run validation
runValidation();
