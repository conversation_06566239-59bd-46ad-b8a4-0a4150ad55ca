# Test Images for YendorCats

This directory contains sample images for testing the YendorCats application.

## Directory Structure

```
test-images/
├── gallery/
├── kittens/
├── queens/
└── studs/
```

## Image Requirements

Place sample cat images in each directory according to the following guidelines:

### Queens Directory
- File naming format: `[Name]-[Age]-[Date]-[Number].jpg/png`
- Example: `Loretta-3-080824-1.jpg`
- At least 3 queen images should be provided

### Studs Directory
- File naming format: `[Name]-[Age]-[Date]-[Number].jpg/png`
- Example: `Dennis-10.3-030324-1.jpg`
- At least 2 stud images should be provided

### Kittens Directory
- File naming format: `[Name/unknown]-[Age]-[Date]-[Number].jpg/png`
- Example: `Georgia-2.5-230325-5.jpeg` or `unknown-1.8-230325-2.jpg`
- At least 2 kitten images should be provided

### Gallery Directory
- File naming format: `gallery-[Number].jpg/png`
- Example: `gallery-1.jpg`
- At least 5 gallery images should be provided

## Image Sources

For testing purposes, you can use royalty-free cat images from the following sources:
- Pexels: https://www.pexels.com/search/maine%20coon/
- Unsplash: https://unsplash.com/s/photos/maine-coon
- Pixabay: https://pixabay.com/images/search/maine%20coon/

## Usage

These images will be automatically uploaded to the MinIO (S3) test environment using the `upload-test-images.sh` script.