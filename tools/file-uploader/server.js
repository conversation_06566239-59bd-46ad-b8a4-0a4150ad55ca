/**
 * YendorCats File Uploader Service
 *
 * This service provides a simple web interface for uploading cat images to Backblaze B2.
 * It extracts metadata from filenames and allows adding additional information.
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const cors = require('cors');
const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
require('dotenv').config();

// Create Express app
const app = express();
const port = process.env.PORT || 80;

// Configure AWS SDK for Backblaze B2
const s3 = new AWS.S3({
  endpoint: process.env.AWS_S3_ENDPOINT,
  accessKeyId: process.env.AWS_S3_ACCESS_KEY,
  secretAccessKey: process.env.AWS_S3_SECRET_KEY,
  s3ForcePathStyle: true,
  signatureVersion: 'v4',
  region: process.env.AWS_S3_REGION
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate a unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    // Accept only image files
    const filetypes = /jpeg|jpg|png|gif|webp/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }

    cb(new Error('Only image files are allowed!'));
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Upload endpoint
app.post('/upload', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { name, age, category, description } = req.body;

    if (!name || !category) {
      return res.status(400).json({ error: 'Name and category are required' });
    }

    // Format the filename according to the convention: Name-Age-Date-Number.ext
    const date = new Date().toISOString().slice(2, 10).replace(/-/g, '');
    const formattedAge = age || 'unknown';
    const formattedName = name.replace(/\s+/g, '_');

    // Get a random number for the image sequence
    const randomNum = Math.floor(Math.random() * 9) + 1;

    // Create the formatted filename
    const formattedFilename = `${formattedName}-${formattedAge}-${date}-${randomNum}${path.extname(req.file.originalname)}`;

    // Determine the S3 key based on category
    const s3Key = `resources/${category.toLowerCase()}/${formattedFilename}`;

    // Upload to Backblaze B2
    const fileContent = fs.readFileSync(req.file.path);

    // Extract all metadata fields from the request
    const {
      gender,
      breed,
      bloodline,
      hair_color,
      personality,
      traits,
      date_taken,
      tags,
      mother,
      father
    } = req.body;

    // Get file metadata
    const fileSize = req.file.size;
    const fileFormat = path.extname(req.file.originalname).substring(1);
    const dateUploaded = new Date().toISOString();
    const uploaderIp = req.ip || req.connection.remoteAddress;
    const uploaderUserAgent = req.headers['user-agent'];
    const uploadSessionId = req.sessionID || `session-${Date.now()}`;

    // Format date taken if provided
    let formattedDateTaken = '';
    if (date_taken) {
      formattedDateTaken = new Date(date_taken).toISOString();
    }

    const params = {
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Key: s3Key,
      Body: fileContent,
      ContentType: req.file.mimetype,
      ACL: 'public-read',
      Metadata: {
        // Required fields
        'name': name,
        'gender': gender || '',
        'category': category,

        // Optional user-provided fields
        'age': formattedAge,
        'description': description || '',
        'breed': breed || '',
        'bloodline': bloodline || '',
        'hair_color': hair_color || '',
        'personality': personality || '',
        'traits': traits || '',
        'tags': tags || '',
        'mother': mother || '',
        'father': father || '',

        // System metadata
        'date_uploaded': dateUploaded,
        'date_taken': formattedDateTaken,
        'file_format': fileFormat,
        'file_size': fileSize.toString(),
        'content_type': req.file.mimetype,
        'uploader_ip': uploaderIp,
        'uploader_user_agent': uploaderUserAgent,
        'upload_session_id': uploadSessionId
      }
    };

    const uploadResult = await s3.upload(params).promise();

    // Clean up the temporary file
    fs.unlinkSync(req.file.path);

    // Update the cat-descriptions.json file via the API if needed
    if (description) {
      try {
        const apiUrl = `${process.env.API_BASE_URL}/api/cats/descriptions`;
        // This would be implemented to call the API to update descriptions
        // For now, we'll just log it
        console.log(`Would update description for ${name} in category ${category}`);
      } catch (apiError) {
        console.error('Error updating cat description:', apiError);
      }
    }

    res.json({
      success: true,
      message: 'File uploaded successfully',
      fileUrl: uploadResult.Location,
      fileName: formattedFilename
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({ error: 'Failed to upload file', details: error.message });
  }
});

// Get categories endpoint
app.get('/categories', (req, res) => {
  // Return the available categories
  const categories = ['studs', 'queens', 'kittens', 'gallery'];
  res.json(categories);
});

// Start the server
app.listen(port, () => {
  console.log(`File uploader service running on port ${port}`);
});
