# YendorCats File Uploader

A simple web interface for uploading cat images to Backblaze B2 with metadata.

## Features

- Upload images to Backblaze B2
- Extract metadata from filenames
- Add additional information like name, age, and description
- Preview images before upload
- Responsive design

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example` and fill in your Backblaze B2 credentials
4. Start the server:
   ```
   npm start
   ```

## Docker

To build and run with Docker:

```bash
docker build -t yendorcats/uploader .
docker run -p 5002:80 --env-file .env yendorcats/uploader
```

## Environment Variables

- `AWS_S3_BUCKET_NAME`: Your Backblaze B2 bucket name
- `AWS_S3_REGION`: Backblaze B2 region (e.g., us-west-004)
- `AWS_S3_ENDPOINT`: Backblaze B2 endpoint URL
- `AWS_S3_ACCESS_KEY`: Your Backblaze B2 application key ID
- `AWS_S3_SECRET_KEY`: Your Backblaze B2 application key
- `API_BASE_URL`: URL of the YendorCats API
- `PORT`: Port to run the server on (default: 80)

## File Naming Convention

Files are renamed according to the following convention:

```
Name-Age-Date-Number.ext
```

For example:
```
Fluffy-2.5-230427-1.jpg
```

This indicates:
- Name: Fluffy
- Age: 2.5 years
- Date: April 27, 2023
- Number: 1 (sequence number)
- Extension: jpg

## Categories

Images can be uploaded to the following categories:
- studs
- queens
- kittens
- gallery

Each category has its own directory in the Backblaze B2 bucket.
