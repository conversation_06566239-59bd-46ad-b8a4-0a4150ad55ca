<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YendorCats - Image Uploader</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-cat"></i> YendorCats Image Uploader</h1>
            <p>Upload cat images to the gallery with metadata</p>
        </header>

        <main>
            <div class="upload-container">
                <form id="upload-form" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="category">Category <span class="required">*</span></label>
                        <select id="category" name="category" required>
                            <option value="" disabled selected>Select a category</option>
                            <option value="studs">Studs</option>
                            <option value="queens">Queens</option>
                            <option value="kittens">Kittens</option>
                            <option value="gallery">Gallery</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="name">Cat Name <span class="required">*</span></label>
                        <input type="text" id="name" name="name" placeholder="e.g., Fluffy" required>
                    </div>

                    <div class="form-group">
                        <label for="age">Age (years.months)</label>
                        <input type="text" id="age" name="age" placeholder="e.g., 2.5">
                        <small>Format: years.months (e.g., 2.5 for 2 years and 5 months)</small>
                    </div>

                    <div class="form-group">
                        <label for="gender">Gender <span class="required">*</span></label>
                        <select id="gender" name="gender" required>
                            <option value="" disabled selected>Select gender</option>
                            <option value="M">Male</option>
                            <option value="F">Female</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="breed">Breed</label>
                        <input type="text" id="breed" name="breed" placeholder="e.g., Siamese">
                    </div>

                    <div class="form-group">
                        <label for="bloodline">Bloodline</label>
                        <input type="text" id="bloodline" name="bloodline" placeholder="e.g., Champion lineage">
                    </div>

                    <div class="form-group">
                        <label for="hair_color">Hair Color</label>
                        <input type="text" id="hair_color" name="hair_color" placeholder="e.g., Brown Tabby">
                    </div>

                    <div class="form-group">
                        <label for="personality">Personality</label>
                        <input type="text" id="personality" name="personality" placeholder="e.g., Playful, Affectionate">
                    </div>

                    <div class="form-group">
                        <label for="traits">Special Traits</label>
                        <input type="text" id="traits" name="traits" placeholder="e.g., Long fur, Green eyes">
                    </div>

                    <div class="form-group">
                        <label for="date_taken">Date Photo Taken</label>
                        <input type="date" id="date_taken" name="date_taken">
                    </div>

                    <div class="form-group">
                        <label for="tags">Tags</label>
                        <input type="text" id="tags" name="tags" placeholder="e.g., kitten, playful, adoption">
                        <small>Separate tags with commas</small>
                    </div>

                    <div class="form-group">
                        <label for="mother">Mother</label>
                        <input type="text" id="mother" name="mother" placeholder="Mother's name">
                    </div>

                    <div class="form-group">
                        <label for="father">Father</label>
                        <input type="text" id="father" name="father" placeholder="Father's name">
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4" placeholder="Enter a description of the cat..."></textarea>
                    </div>

                    <div class="form-group file-input-group">
                        <label for="image">Image <span class="required">*</span></label>
                        <div class="file-input-container">
                            <input type="file" id="image" name="image" accept="image/*" required>
                            <div class="file-preview-container">
                                <img id="image-preview" src="#" alt="Preview" style="display: none;">
                            </div>
                        </div>
                        <small>Max file size: 10MB. Supported formats: JPG, PNG, GIF, WebP</small>
                    </div>

                    <div class="form-group">
                        <label>Filename Preview</label>
                        <div class="filename-preview" id="filename-preview">
                            <code>Name-Age-Date-Number.jpg</code>
                        </div>
                        <small>The file will be renamed according to this format</small>
                    </div>

                    <div class="form-actions">
                        <button type="submit" id="upload-button">
                            <i class="fas fa-upload"></i> Upload Image
                        </button>
                    </div>
                </form>
            </div>

            <div class="upload-status" id="upload-status" style="display: none;">
                <div class="status-content">
                    <div id="status-icon" class="status-icon">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <h3 id="status-message">Uploading...</h3>
                    <p id="status-details"></p>
                    <div class="uploaded-image-container" id="uploaded-image-container" style="display: none;">
                        <img id="uploaded-image" src="#" alt="Uploaded Image">
                        <div class="image-details">
                            <p><strong>URL:</strong> <a id="image-url" href="#" target="_blank"></a></p>
                            <p><strong>Filename:</strong> <span id="image-filename"></span></p>
                        </div>
                    </div>
                    <button id="upload-another-button" style="display: none;">Upload Another Image</button>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2024 YendorCats. All rights reserved.</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
