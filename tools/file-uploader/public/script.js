/**
 * YendorCats File Uploader JavaScript
 * 
 * This script handles the file upload functionality, including:
 * - Image preview
 * - Filename preview
 * - Form submission
 * - Upload status display
 */

document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const uploadForm = document.getElementById('upload-form');
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('image-preview');
    const nameInput = document.getElementById('name');
    const ageInput = document.getElementById('age');
    const categorySelect = document.getElementById('category');
    const filenamePreview = document.getElementById('filename-preview');
    const uploadStatus = document.getElementById('upload-status');
    const statusIcon = document.getElementById('status-icon');
    const statusMessage = document.getElementById('status-message');
    const statusDetails = document.getElementById('status-details');
    const uploadedImageContainer = document.getElementById('uploaded-image-container');
    const uploadedImage = document.getElementById('uploaded-image');
    const imageUrl = document.getElementById('image-url');
    const imageFilename = document.getElementById('image-filename');
    const uploadAnotherButton = document.getElementById('upload-another-button');

    // Show image preview when a file is selected
    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            
            reader.readAsDataURL(this.files[0]);
        }
    });

    // Update filename preview when inputs change
    function updateFilenamePreview() {
        const name = nameInput.value || 'Name';
        const formattedName = name.replace(/\s+/g, '_');
        const age = ageInput.value || 'unknown';
        const date = new Date().toISOString().slice(2, 10).replace(/-/g, '');
        const extension = imageInput.files && imageInput.files[0] ? 
            getFileExtension(imageInput.files[0].name) : 'jpg';
        
        filenamePreview.innerHTML = `<code>${formattedName}-${age}-${date}-1.${extension}</code>`;
    }

    // Get file extension
    function getFileExtension(filename) {
        return filename.split('.').pop().toLowerCase();
    }

    // Add event listeners for input changes
    nameInput.addEventListener('input', updateFilenamePreview);
    ageInput.addEventListener('input', updateFilenamePreview);
    imageInput.addEventListener('change', updateFilenamePreview);

    // Handle form submission
    uploadForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Show upload status
        uploadStatus.style.display = 'block';
        statusIcon.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        statusMessage.textContent = 'Uploading...';
        statusDetails.textContent = 'Please wait while your image is being uploaded.';
        uploadedImageContainer.style.display = 'none';
        uploadAnotherButton.style.display = 'none';
        
        // Scroll to status
        uploadStatus.scrollIntoView({ behavior: 'smooth' });
        
        // Create form data
        const formData = new FormData(this);
        
        try {
            // Send the upload request
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                // Show success message
                statusIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
                statusIcon.className = 'status-icon success';
                statusMessage.textContent = 'Upload Successful!';
                statusDetails.textContent = 'Your image has been uploaded successfully.';
                
                // Display uploaded image
                uploadedImage.src = result.fileUrl;
                imageUrl.href = result.fileUrl;
                imageUrl.textContent = result.fileUrl;
                imageFilename.textContent = result.fileName;
                uploadedImageContainer.style.display = 'block';
                uploadAnotherButton.style.display = 'inline-block';
            } else {
                // Show error message
                statusIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
                statusIcon.className = 'status-icon error';
                statusMessage.textContent = 'Upload Failed';
                statusDetails.textContent = result.error || 'An error occurred during upload.';
                uploadAnotherButton.style.display = 'inline-block';
            }
        } catch (error) {
            console.error('Error:', error);
            
            // Show error message
            statusIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            statusIcon.className = 'status-icon error';
            statusMessage.textContent = 'Upload Failed';
            statusDetails.textContent = 'An unexpected error occurred. Please try again.';
            uploadAnotherButton.style.display = 'inline-block';
        }
    });
    
    // Handle "Upload Another" button
    uploadAnotherButton.addEventListener('click', function() {
        // Reset form
        uploadForm.reset();
        imagePreview.style.display = 'none';
        updateFilenamePreview();
        
        // Hide status
        uploadStatus.style.display = 'none';
        
        // Scroll to form
        uploadForm.scrollIntoView({ behavior: 'smooth' });
    });
    
    // Initialize filename preview
    updateFilenamePreview();
});
