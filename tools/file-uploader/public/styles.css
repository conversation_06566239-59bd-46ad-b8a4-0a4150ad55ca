/* YendorCats File Uploader Styles */

:root {
    --primary-color: #3a1c71;
    --secondary-color: #d76d77;
    --accent-color: #ffaf7b;
    --text-color: #333;
    --light-color: #f8f9fa;
    --border-color: #ddd;
    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

header {
    text-align: center;
    margin-bottom: 2rem;
}

header h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

header p {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

main {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.upload-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.required {
    color: var(--error-color);
}

input[type="text"],
textarea,
select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

input[type="text"]:focus,
textarea:focus,
select:focus {
    border-color: var(--secondary-color);
    outline: none;
}

input[type="file"] {
    padding: 0.5rem 0;
}

small {
    display: block;
    margin-top: 0.25rem;
    color: #666;
    font-size: 0.85rem;
}

.file-input-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.file-preview-container {
    margin-top: 1rem;
    text-align: center;
}

#image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.filename-preview {
    background-color: #f5f5f5;
    padding: 0.75rem;
    border-radius: 5px;
    font-family: monospace;
    margin-top: 0.5rem;
}

.form-actions {
    margin-top: 2rem;
    text-align: center;
}

button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

button:hover {
    background-color: #2a1352;
}

.upload-status {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    text-align: center;
}

.status-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.status-icon.success {
    color: var(--success-color);
}

.status-icon.error {
    color: var(--error-color);
}

.uploaded-image-container {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #f9f9f9;
    border-radius: 5px;
}

#uploaded-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.image-details {
    text-align: left;
    margin-top: 1rem;
}

.image-details p {
    margin-bottom: 0.5rem;
}

#upload-another-button {
    margin-top: 1.5rem;
}

footer {
    text-align: center;
    margin-top: 3rem;
    color: #666;
}

/* Responsive styles */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .upload-container,
    .upload-status {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.5rem;
    }
    
    button {
        width: 100%;
    }
}
