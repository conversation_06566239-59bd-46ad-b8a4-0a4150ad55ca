#!/bin/bash

# Deployment script for YendorCats application
# This script builds the application, sets up AWS resources, and deploys to AWS

set -e  # Exit on any error

# Constants
APP_NAME="yendorcats"
REGION=${AWS_REGION:-"ap-southeast-2"}
ECR_REPO="${APP_NAME}-api"
RDS_IDENTIFIER="${APP_NAME}-db"
EC2_NAME="${APP_NAME}-web"
FRONTEND_DIR="$(pwd)/frontend"
BACKEND_DIR="$(pwd)/backend/YendorCats.API"
DOCKER_TAG="latest"

# Color outputs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}============================================${NC}"
echo -e "${GREEN}     YendorCats Deployment Script          ${NC}"
echo -e "${GREEN}============================================${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if we're authenticated with AWS
aws sts get-caller-identity > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${RED}AWS CLI is not authenticated. Please run 'aws configure' first.${NC}"
    exit 1
fi

# Function to show a step
show_step() {
    echo -e "\n${YELLOW}STEP: $1${NC}"
}

# Function to show success
show_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

# 1. Build the frontend
show_step "Building the frontend"
cd "$FRONTEND_DIR"
echo "Building frontend assets..."
# Add your frontend build commands here if needed
show_success "Frontend built successfully"

# 2. Build the backend
show_step "Building the backend"
cd "$BACKEND_DIR"
echo "Restoring .NET packages..."
dotnet restore
echo "Building the API..."
dotnet build --configuration Release --no-restore
echo "Creating Docker image..."
docker build -t "$APP_NAME:$DOCKER_TAG" -f Dockerfile .
show_success "Backend built successfully"

# 3. Set up AWS resources if needed
show_step "Checking AWS resources"

# Check if ECR repository exists, create if not
echo "Checking ECR repository..."
aws ecr describe-repositories --repository-names "$ECR_REPO" --region "$REGION" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Creating ECR repository..."
    aws ecr create-repository --repository-name "$ECR_REPO" --region "$REGION"
    show_success "ECR repository created"
else
    echo "ECR repository already exists"
fi

# Login to ECR
echo "Logging in to ECR..."
aws ecr get-login-password --region "$REGION" | docker login --username AWS --password-stdin "$(aws sts get-caller-identity --query Account --output text).dkr.ecr.$REGION.amazonaws.com"

# Tag and push the Docker image
show_step "Publishing Docker image to ECR"
ECR_URI="$(aws sts get-caller-identity --query Account --output text).dkr.ecr.$REGION.amazonaws.com/$ECR_REPO:$DOCKER_TAG"
docker tag "$APP_NAME:$DOCKER_TAG" "$ECR_URI"
docker push "$ECR_URI"
show_success "Docker image pushed to ECR: $ECR_URI"

# 4. Set up AWS Secrets Manager
show_step "Setting up AWS Secrets Manager"
echo "Do you want to run the AWS Secrets Manager setup script? (y/n)"
read -r setup_secrets

if [[ "$setup_secrets" =~ ^[Yy]$ ]]; then
    echo "Running setup-aws-secrets.sh..."
    "$(pwd)/../tools/setup-aws-secrets.sh"
    show_success "AWS Secrets Manager setup completed"
else
    echo "Skipping AWS Secrets Manager setup"
fi

# 5. Update EC2 instance
show_step "Updating EC2 instance"
echo "Do you want to update the EC2 instance with the new Docker image? (y/n)"
read -r update_ec2

if [[ "$update_ec2" =~ ^[Yy]$ ]]; then
    echo "Please enter the SSH connection string to your EC2 instance (e.g., ec2-user@your-instance-ip):"
    read -r ec2_ssh
    
    echo "Connecting to EC2 instance and updating Docker container..."
    ssh "$ec2_ssh" << EOF
        # Pull the latest image
        aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $(aws sts get-caller-identity --query Account --output text).dkr.ecr.$REGION.amazonaws.com
        docker pull $ECR_URI
        
        # Stop the existing container (if any)
        docker stop $APP_NAME 2>/dev/null || true
        docker rm $APP_NAME 2>/dev/null || true
        
        # Run the new container
        docker run -d --name $APP_NAME \
            -p 80:80 -p 443:443 \
            --restart always \
            -e ASPNETCORE_ENVIRONMENT=Production \
            $ECR_URI
        
        echo "Container updated!"
EOF
    
    show_success "EC2 instance updated successfully"
else
    echo "Skipping EC2 update"
fi

# Final success message
echo -e "\n${GREEN}============================================${NC}"
echo -e "${GREEN}     Deployment completed successfully!     ${NC}"
echo -e "${GREEN}============================================${NC}"
echo -e "Frontend built and included in the Docker image"
echo -e "Docker image pushed to: ${YELLOW}$ECR_URI${NC}"
echo -e "If you updated the EC2 instance, the app should be running at your EC2 public address"
echo -e "\nThank you for using the YendorCats deployment tool!" 