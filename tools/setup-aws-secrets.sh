#!/bin/bash

# Setup AWS Secrets Manager for YendorCats application
# This script creates the necessary AWS Secrets Manager secrets for the application

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo "jq is not installed. Please install it first."
    exit 1
fi

# Default region
region=${AWS_REGION:-"ap-southeast-2"}

# Check if we're authenticated with AWS
aws sts get-caller-identity > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "AWS CLI is not authenticated. Please run 'aws configure' first."
    exit 1
fi

echo "Setting up AWS Secrets Manager secrets for YendorCats application..."

# Generate a secure random JWT secret
jwt_secret=$(openssl rand -base64 32)

# Prepare app secrets JSON
cat > app_secrets.json << EOF
{
  "DbConnectionString": "Host=YOUR_RDS_HOST;Database=YendorCats;Username=YOUR_RDS_USERNAME;Password=YOUR_RDS_PASSWORD;",
  "JwtSecret": "$jwt_secret",
  "JwtIssuer": "YendorCatsApi",
  "JwtAudience": "YendorCatsClients",
  "JwtExpiryMinutes": 60,
  "RefreshExpiryDays": 7,
  "S3AccessKey": "YOUR_S3_ACCESS_KEY",
  "S3SecretKey": "YOUR_S3_SECRET_KEY",
  "S3SessionToken": "",
  "ApiKey": "YOUR_API_KEY"
}
EOF

echo "Created temporary app_secrets.json template. Please edit it with your actual database credentials and API key."
echo "Press Enter to continue after editing the file, or Ctrl+C to cancel..."
read

# Create the main app secrets
echo "Creating yendorcats/app-secrets in AWS Secrets Manager..."
aws secretsmanager create-secret \
    --name yendorcats/app-secrets \
    --description "YendorCats application secrets" \
    --secret-string file://app_secrets.json \
    --region $region

if [ $? -ne 0 ]; then
    echo "Failed to create yendorcats/app-secrets in AWS Secrets Manager."
    exit 1
fi

echo "Successfully created yendorcats/app-secrets in AWS Secrets Manager."

# Clean up
echo "Cleaning up temporary files..."
rm app_secrets.json

echo "Done!"
echo ""
echo "Next steps:"
echo "1. Update your EC2 instance IAM role to allow access to these secrets"
echo "2. Use the following IAM policy:"
echo ""
cat << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": "arn:aws:secretsmanager:$region:*:secret:yendorcats/*"
    }
  ]
}
EOF
echo ""
echo "3. Make sure your EC2 instance has this policy attached to its IAM role"