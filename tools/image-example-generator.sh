#!/bin/bash

# YendorCats Image Example Generator
# This script creates example image files with the correct naming convention

echo "YendorCats Image Example Generator"
echo "======================================"
echo "This script will help you create example image files with the correct naming convention."
echo "The naming convention is: [cat's name]-[age]-[date(DDMMYY)]-[order].jpg"
echo

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "ImageMagick is required to create the example images. Please install it first."
    echo "On Ubuntu/Debian: sudo apt-get install imagemagick"
    echo "On macOS: brew install imagemagick"
    echo "On Windows: https://imagemagick.org/script/download.php"
    exit 1
fi

# Default values
DEFAULT_WIDTH=800
DEFAULT_HEIGHT=600
DEFAULT_TEXT_COLOR="white"
DEFAULT_BG_COLOR="black"
DEFAULT_DESTINATION="../backend/YendorCats.API/wwwroot/resources"

# Function to create example image
create_example_image() {
    local name=$1
    local age=$2
    local date=$3
    local order=$4
    local category=$5
    local width=$6
    local height=$7
    local bg_color=$8
    local text_color=$9

    local filename="${name}-${age}-${date}-${order}.jpg"
    local destination_path="${DEFAULT_DESTINATION}/${category}"
    
    # Create destination directory if it doesn't exist
    mkdir -p "$destination_path"
    
    echo "Creating example image: $filename in category: $category"
    
    # Create an image with the cat name and metadata
    convert -size ${width}x${height} \
        xc:${bg_color} \
        -gravity center \
        -pointsize 36 \
        -fill ${text_color} \
        -draw "text 0,0 '${name}'" \
        -pointsize 24 \
        -draw "text 0,50 'Age: ${age} years'" \
        -draw "text 0,100 'Date: ${date}'" \
        -draw "text 0,150 'Order: ${order}'" \
        -draw "text 0,200 'Category: ${category}'" \
        "${destination_path}/${filename}"
    
    echo "✓ Created ${destination_path}/${filename}"
}

# Ask if user wants to create examples automatically or manually
echo "Do you want to create:"
echo "1) Automatic example images (quick)"
echo "2) Enter your own cat details (custom)"
read -p "Enter your choice (1/2): " choice

if [ "$choice" = "1" ]; then
    echo "Creating automatic example images..."
    echo
    
    # Stud examples
    create_example_image "Leo" "3.5" "230325" "1" "studs" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    create_example_image "Max" "4.2" "230324" "1" "studs" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    create_example_image "Thor" "2.8" "230323" "1" "studs" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    
    # Queen examples
    create_example_image "Luna" "2.6" "230325" "1" "queens" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    create_example_image "Bella" "3.7" "230324" "1" "queens" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    create_example_image "Nala" "1.9" "230323" "1" "queens" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    
    # Kitten examples
    create_example_image "Whiskers" "0.3" "230325" "1" "kittens" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    create_example_image "Mittens" "0.4" "230324" "1" "kittens" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    create_example_image "Socks" "0.2" "230323" "1" "kittens" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    
    # Gallery examples
    create_example_image "Family" "2.0" "230325" "1" "gallery" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    create_example_image "Group" "3.0" "230324" "1" "gallery" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    create_example_image "Playful" "1.0" "230323" "1" "gallery" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    
    echo
    echo "✓ All example images created successfully!"
    
elif [ "$choice" = "2" ]; then
    while true; do
        echo
        echo "Enter cat details (leave name blank to finish):"
        
        read -p "Cat name: " name
        [ -z "$name" ] && break
        
        read -p "Age (years, can use decimal like 2.5): " age
        read -p "Date taken (format DDMMYY, e.g., 230325 for March 23, 2025): " date
        read -p "Order number (e.g., 1 for first image of the day): " order
        
        echo "Select category:"
        echo "1) Studs (male cats)"
        echo "2) Queens (female cats)"
        echo "3) Kittens"
        echo "4) Gallery (general images)"
        read -p "Enter category number (1-4): " cat_choice
        
        case $cat_choice in
            1) category="studs" ;;
            2) category="queens" ;;
            3) category="kittens" ;;
            4) category="gallery" ;;
            *) 
                echo "Invalid choice. Using gallery as default."
                category="gallery"
                ;;
        esac
        
        create_example_image "$name" "$age" "$date" "$order" "$category" $DEFAULT_WIDTH $DEFAULT_HEIGHT $DEFAULT_BG_COLOR $DEFAULT_TEXT_COLOR
    done
    
    echo
    echo "✓ Custom images created successfully!"
    
else
    echo "Invalid choice. Exiting."
    exit 1
fi

echo
echo "Images are stored in: ${DEFAULT_DESTINATION}/{category}"
echo "You can now use these images to test the automatic metadata extraction."
echo "Restart the application if it's already running to see the changes." 